{
  // Place your nuxt-cost-frontend workspace snippets here. Each snippet is defined under a snippet name and has a scope, prefix, body and
  // description. Add comma separated ids of the languages where the snippet is applicable in the scope field. If scope
  // is left empty or omitted, the snippet gets applied to all languages. The prefix is what is
  // used to trigger the snippet and the body will be expanded and inserted. Possible variables are:
  // $1, $2 for tab stops, $0 for the final cursor position, and ${1:label}, ${2:another} for placeholders.
  // Placeholders with the same ids are connected.
  // Example:
  // "Print to console": {
  // 	"scope": "javascript,typescript",
  // 	"prefix": "log",
  // 	"body": [
  // 		"console.log('$1');",
  // 		"$2"
  // 	],
  // 	"description": "Log output to console"
  // }
  "Unit test": {
    "body": [
      "import { mount, type ComponentMountingOptions } from '@vue/test-utils'",
      "import ${TM_FILENAME_BASE/(.*)\\..+$/$1/} from './${TM_FILENAME_BASE/(.*)\\..+$/$1/}.vue'",
      "import { createTestingPinia } from '@pinia/testing'",
      "",
<<<<<<< Updated upstream
=======
      "//#region MOCKS",
      "//#endregion MOCKS",
      "",
      "",
>>>>>>> Stashed changes
      "//#region SETUP FACTORY",
      "const given = ({ parameter='value' } = {}) => {",
      "  const props: InstanceType<typeof ${TM_FILENAME_BASE/(.*)\\..+$/$1/}>['\\$props'] = {}",
      "  const mountOptions: ComponentMountingOptions<typeof ${TM_FILENAME_BASE/(.*)\\..+$/$1/}> = {",
      "    props: {",
      "      ...props",
      "    },",
<<<<<<< Updated upstream
=======
      "    data: () => {},",
      "    stubs: [],",
>>>>>>> Stashed changes
      "    global: {",
      "      stubs: [],",
      "      plugins: [createTestingPinia()],",
      "    },",
      "  }",
      "",
      "  const wrapper = mount(${TM_FILENAME_BASE/(.*)\\..+$/$1/}, mountOptions)",
      "",
      "  //#region HELPERS",
      "  const getTestObject = () =>",
      "    wrapper.find('[data-test=\"test-object\"]')",
      "  //#endregion HELPERS",
      "",
      "  //#region WHEN & THEN",
      "  const when={}",
      "  const then={}",
      "  //#endregion WHEN & THEN",
      "",
      "  return {",
      "    when,",
      "    then",
      "  }",
      "}",
      "//#endregion SETUP FACTORY",
      "",
      "//#region TESTS",
      "describe('Test component functionality',() => {",
      "  it('- test something',() => {",
      "",
      "  })",
      "})",
      "//#endregion TESTS",
      "",
    ],
    "description": "Snippet for creating a new unit test file",
  },
  "Vue component": {
    "body": [
      "<template>",
      "  <div v-data-test></div>",
      "</template>",
      "",
      "<script setup lang=\"ts\">",
      "//#region PROPS",
      "const props = defineProps<{$1}>()",
      "//#endregion PROPS",
      "",
      "//#region EMITS",
      "const emits = defineEmits<{$2}>()",
      "//#endregion EMITS",
      "",
      "//#region LIFECYCLE",
      "//#endregion LIFECYCLE",
      "",
      "//#region Functionality A",
      "//#endregion Functionality A",
      "</script>",
      "",
      "<script lang=\"ts\">",
      "export default {",
      "  name: '$TM_FILENAME_BASE'",
      "}",
      "",
      "</script>",
      "<style lang=\"postcss\" scoped></style>",
    ],
    "description": "Snippet for creating a new component",
  },
  "Zod Schema": {
    "prefix": "zod",
    "body": [
      "import { z } from 'zod'",
      "import { createGenerator, createGuard } from '@/schemas/helpers'",
      "",
      "export const ${1:$TM_FILENAME_BASE}Schema = z",
      "  .object({",
      "  ${2}",
      "  })",
      "  .describe('${1:$TM_FILENAME_BASE}')",
      "",
      "export const g${1:$TM_FILENAME_BASE} = createGenerator(${1:$TM_FILENAME_BASE}Schema, {${3}})",
      "",
      "export const is${1:$TM_FILENAME_BASE} = createGuard(${1:$TM_FILENAME_BASE}Schema)",
      "",
      "declare global {",
      "  type ${1:$TM_FILENAME_BASE} = z.infer<typeof ${1:$TM_FILENAME_BASE}Schema>",
      "}",
      "",
    ],
    "description": "Snippet for adding a new Schema",
  },
  "Story": {
    "isFileTemplate": true,
    "body": [
      "import type { Meta, StoryObj } from '@storybook/vue3'",
      "import ${1:$TM_FILENAME_BASE} from './${1:$TM_FILENAME_BASE}.vue'",
      "",
      "const meta: Meta<typeof ${1:$TM_FILENAME_BASE}> = {",
      "  component: ${1:$TM_FILENAME_BASE},",
      "  tags: ['autodocs']",
      "}",
      "export default meta",
      "",
      "type Story = StoryObj<typeof ${1:$TM_FILENAME_BASE}>",
      "",
      "${2}",
    ],
  },
}
