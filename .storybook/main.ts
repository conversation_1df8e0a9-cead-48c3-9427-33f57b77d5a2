import type { StorybookConfig } from '@storybook/vue3-vite'
import { resolve } from 'path'

const config: StorybookConfig = {
  stories: ['../packages/design/**/*.stories.ts'],
  addons: [
    '@chromatic-com/storybook',
    '@storybook/addon-a11y',
    'storybook-addon-pseudo-states',
    '@storybook/addon-docs',
  ],
  framework: {
    name: '@storybook/vue3-vite',
    options: {
      docgen: 'vue-component-meta',
    },
  },
  staticDirs: ['../assets'],
  viteFinal: async (config) => {
    config.resolve = config.resolve || {}
    config.resolve.alias = {
      ...config.resolve.alias,
      '@tset/design': resolve(__dirname, '../packages/design'),
      '@tset/shared-utils': resolve(__dirname, '../packages/shared/utils'),
    }
    return config
  },
}

export default config
