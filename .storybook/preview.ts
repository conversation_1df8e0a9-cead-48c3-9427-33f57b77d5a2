import '@@/assets/css/_base.css'
import '@@/assets/css/style.css'
import '@@/assets/css/tailwind.css'
import type { Preview } from '@storybook/vue3-vite'
import { setup } from '@storybook/vue3-vite'
import { initializeDirectives } from '@tset/shared-utils/directives'
import { iconsPlugin } from '@tset/shared-utils/plugins/icons'
import { configure } from 'storybook/test'
import { createI18n } from 'vue-i18n'
import { createMemoryHistory, createRouter } from 'vue-router'

const router = createRouter({
  history: createMemoryHistory(),
  routes: [],
})

setup((app) => {
  if (app) {
    app.use(iconsPlugin)
    app.use(router)
    initializeDirectives(app)

    // just doing app.use(i18n) throws an error message "Cannot read properties of undefined (reading 'app')" but the storybook still works
    // providing it we get rid of the error message
    const i18n = createI18n({})
    app.config.globalProperties.$t = i18n.global.t
    app.provide('i18n', i18n)
  }
})

configure({ testIdAttribute: 'data-test' })

const preview: Preview = {
  parameters: {
    controls: {
      matchers: {
        color: /(background|color)$/i,
        date: /Date$/i,
      },
    },
  },
}

export default preview
