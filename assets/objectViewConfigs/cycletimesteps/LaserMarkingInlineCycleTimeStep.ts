const LaserMarkingInlineCycleTimeStep: ObjectViewConfig = {
  title: {
    title: 'displayDesignation',
    kpi: 'time',
  },
  cards: [
    {
      columns: [
        {
          sections: [
            {
              title: 'cycleTime',
              fieldNames: [
                'time',
              ],
            },
            {
              title: '',
              fieldNames: [
                'numberOfPcbsPerPanel',
                'numberOfMarkingsPerPcb',
                'numberOfSides',
                'panelInfeedTime',
                'panelOutFeedTime'
              ],
            },
            {
              title: 'MarkingPcbs',
              fieldNames: [
                'timePerAxisMovement',
                'axisMovementTotalTime',
                'timePerMarking',
                'markingTotalTime',
                'timePerScan',
                'scanningTotalTime',
                'panelFlippingTime',
                'panelFlippingTotalTime'
              ],
            },
            {
              title: 'MarkingPanel',
              fieldNames: [
                'timePerCodeAxisMovement',
                'axisMovementPerCodeTotalTime',
                'timePerCodeMarking',
                'codeMarkingTotalTime',
                'timePerCodeScan',
                'codeScanningTotalTime',
                'totalCodeMarkingTime'
              ],
            },
          ],
        },
      ],
      collapsible: false,
      initiallyCollapsed: false,
      title: 'LaserMarkingInlineCycleTimeStep',
      withSeparator: true,
    },
  ],
  hasCostTable: false,
}
export default LaserMarkingInlineCycleTimeStep
