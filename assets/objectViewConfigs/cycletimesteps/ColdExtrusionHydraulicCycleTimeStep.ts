const ColdExtrusionHydraulicCycleTimeStep = {
  title: {
    title: 'displayDesignation',
    kpi: 'adjustedTime',
  },
  cards: [
    {
      columns: [
        {
          sections: [
            {
              title: '',
              fieldNames: [
                'time',
                'adjustmentRate',
                'adjustedTime',
              ],
            },
          ],
        },
      ],
      collapsible: false,
      initiallyCollapsed: false,
      title: 'timeSettings',
      withSeparator: false,
    },
    {
      columns: [
        {
          sections: [
            {
              title: '',
              fieldNames: [
                'formingLength',
                'ramStroke',
                'pressSpeed',
                'idleStroke',
                'handlingTime',
                'blowOutAndDieLubrication',
              ],
            },
          ],
        },
      ],
      collapsible: false,
      initiallyCollapsed: false,
      title: 'cycleTimeStepParameters',
      withSeparator: false,
    },
  ],
  hasCostTable: false,
}

export default ColdExtrusionHydraulicCycleTimeStep
