const ManualPunchingNibbling: ObjectViewConfig = {
  title: {
    title: 'displayDesignation',
    kpi: 'adjustedTime',
  },
  cards: [
    {
      columns: [
        {
          sections: [
            {
              title: 'time',
              fieldNames: ['primaryTime', 'secondaryTime', 'time'],
            },
            {
              title: 'basicParameters',
              fieldNames: [
                'maxStrokes',
                'maxPunchingForce',
                'numberOfHoles',
                'materialTensileStrength',
                'sheetThickness',
                'roughnessDepth',
                'holeType',
                'holeDiameter',
                'holeLength',
                'maxSizeOfTool'
              ],
            },
          ],
        },
        {
          sections: [
            {
              title: 'detailParameters',
              fieldNames: [
                'distanceBetweenHoles',
                'feedRatePerStroke',
                'toolExchangeTime',
              ],
            },
          ],
        },
      ],
      collapsible: false,
      initiallyCollapsed: false,
      title: 'ManualPunchingNibbling',
      withSeparator: true,
    },
  ],
  hasCostTable: false,
}

export default ManualPunchingNibbling
