<template>
  <TsetCard
    class="nu-teaser-card-upload"
    :class="{
      'is-selected': isSelected,
      'is-modal-opened': isModalOpened,
    }"
  >
    <label for="file" class="file-label">
      <!-- CARD CONTENT -->
      <TsetImage :src="imageURL" class="pb-12" :alt="card.title" />
      <h3 class="text-subheader-semibold">
        {{ $nut(['shape.' + showTitleOrId, showTitleOrId]) }}
      </h3>
      <p class="text-body-light">{{ card.description }}</p>
      <!-- UPLOAD COMPONENT -->
      <FileUpload
        :ref="`upload-field-${card.id}`"
        :accept="`.stp,.sla,.step`"
        :multiple="false"
        :size="1024 * 1024 * 10"
        class="!hidden"
        @input-file="onInputFileSelected"
      />
    </label>

    <!-- #region MODAL -->
    <BaseModal
      :modal-name="`${card.id}-preview-modal`"
      :title="$t('statics.upload3d')"
      size="large"
      @before-open="beforeOpenHandler"
      @before-close="beforeCloseHandler"
    >
      <template #content>
        <div class="relative flex flex-col justify-center px-20 pt-20">
          <Ws3Renderer
            :project-id="projectId"
            :shape-id="fileUploadId"
            class="flex-1"
            @error="showError"
            @ready-to-render="onReadyToRender"
          >
            <template #error>
              <InfoBox v-if="fileSizeExceededError">
                {{
                  $t(
                    `userErrorCodes.${fileSizeExceededError.userErrorCode}`,
                    fileSizeExceededError.userParameters as FixmeType
                  )
                }}
              </InfoBox>
            </template>
          </Ws3Renderer>
          <GlbViewerLegend
            v-if="isSuccess"
            class="absolute bottom-20 left-20"
          />
        </div>
      </template>
      <template #footer>
        <div v-if="isSuccess || isError" class="modal-button-wrapper">
          <!-- CLOSE MODAL BUTTON -->
          <NuButton
            v-data-test:cancel
            :is-primary="false"
            :is-loading="isLoading"
            @click="closeModal()"
          >
            {{ modalCancelButtonText }}
          </NuButton>
          <template v-if="isSuccess">
            <!-- NEXT BUTTON -->
            <NuButton
              v-data-test:continue
              :is-primary="true"
              :is-loading="isLoading"
              @click="emitClickedNext()"
            >
              {{ $t('button.continue') }}
            </NuButton>
          </template>

          <!-- RE-UPLOAD BUTTON -->
          <label
            v-if="isError"
            v-data-test:reupload
            for="file"
            class="button-base button-primary relative flex h-36 cursor-pointer items-center transition-colors"
          >
            {{ $t('button.reupload') }}
          </label>
        </div>
      </template>
    </BaseModal>
    <!-- #endregion MODAL -->
  </TsetCard>
</template>

<script lang="ts">
import { uploadStepFile } from '@/api/smf'
import { smfStore } from '@/store'
import { useCreateManuStore } from '@domain/wizard/createManu.store'
import { $t } from '@shared/translation/nuTranslation'
import TsetCard from '@tset/design/atoms/TsetCard/TsetCard.vue'
import TsetImage from '@tset/design/atoms/TsetImage/TsetImage.vue'
import TsetWave from '@tset/design/atoms/TsetWave/TsetWave.vue'
import FileUpload from '@tset/design/molecules/TsetFileUpload.vue'
import Ws3Renderer from '@tset/shared-ui/3dViewer/Ws3Renderer.vue'
import BaseModal from '@tset/shared-ui/base/baseModal/BaseModal.vue'
import InfoBox from '@tset/shared-ui/layout/InfoBox.vue'
import NuButtonWithDD from '@tset/shared-ui/parts/NuButtonWithDD.vue'
import GlbViewerLegend from '@tset/shared-ui/smf/GlbViewerLegend.vue'
import {
  Component,
  Emit,
} from '@tset/shared-utils/decorators/component/VueClassDecorator'
import { isPreviewFileSizeExceededError } from '@tset/shared-utils/helpers/typeGuard/isPreviewFileSizeExceeded'
import { isPreviewProcessingError } from '@tset/shared-utils/helpers/typeGuard/isPreviewProcessingError'
import NuButton from './NuButton.vue'
import { NuTeaserCard } from './NuTeaserCard.vue'

const createManuStore = useCreateManuStore()

@Component({
  name: 'NuTeaserCardUpload',
  components: {
    FileUpload,
    BaseModal,
    TsetWave,
    NuButtonWithDD,
    GlbViewerLegend,
    NuButton,
    TsetCard,
    Ws3Renderer,
    InfoBox,
    TsetImage,
  },
})
export default class NuTeaserCardUpload extends NuTeaserCard {
  //#region DATA
  isModalOpened: boolean = false
  loadingState: 'idle' | 'pending' | 'success' | 'error' = 'idle'
  fileSizeExceededError: Nullable<
    PreviewFileSizeExceededError | PreviewProcessingError
  > = null

  //#endregion DATA

  //#region EMITS
  @Emit('clicked-next')
  emitClickedNext() {}
  @Emit('deselected')
  emitDeselected() {}
  //#endregion EMITS

  //#region EVENT HANDLERS
  beforeOpenHandler() {
    this.fileSizeExceededError = null
    smfStore.clearUploadId()
    this.isModalOpened = true
  }

  beforeCloseHandler() {
    this.isModalOpened = false
    smfStore.clearUploadId()
    this.emitDeselected()
  }
  //#endregion EVENT HANDLERS

  //#region GETTERS
  get modalCancelButtonText() {
    if (this.loadingState === 'success') {
      return $t('button.discard')
    }
    return $t('button.cancel')
  }

  get isLoading() {
    return createManuStore.isLoading
  }

  get isSuccess() {
    return this.loadingState === 'success'
  }

  get isError() {
    return this.loadingState === 'error'
  }

  get projectId() {
    return createManuStore.loadedProjectId!
  }

  get fileUploadId() {
    return smfStore.fileUploadId
  }

  get isNotCube() {
    return (
      createManuStore.wizard?.steps.WizardTechStep.fields.find(
        ({ name }) => name === 'modelId'
      )?.value !== 'ManufacturingCuttingAndBending'
    )
  }
  //#endregion GETTERS

  //#region FUNCTIONS
  async onInputFileSelected(newFileInput: unknown) {
    if (!newFileInput) {
      return
    }

    this.loadingState = 'pending'

    try {
      this.openModal()
      await this.uploadFile(newFileInput)
      this.select()
    } catch (error) {
      this.showError(error)
    }
  }

  showError(error: unknown) {
    if (
      (isPreviewFileSizeExceededError(error) ||
        isPreviewProcessingError(error)) &&
      this.isNotCube
    ) {
      this.fileSizeExceededError = error.response!.data
      this.loadingState = 'success'
      this.select()
    } else {
      this.loadingState = 'error'
    }
  }

  onReadyToRender() {
    this.loadingState = 'success'
  }

  openModal() {
    this.$modal.show(`${this.card.id}-preview-modal`, { overflowAllowed: true })
  }

  closeModal() {
    this.$modal.hide(`${this.card.id}-preview-modal`)
  }

  async uploadFile(newFileInput: unknown) {
    const data = await uploadStepFile(newFileInput)
    smfStore.setFileUploadId(data[0].id)
    const fileUploadIdField = createManuStore.currentSectionData?.fields.find(
      (field) => field.name === 'fileUploadId'
    )
    if (!fileUploadIdField) {
      return
    }

    createManuStore.updateField({
      ...fileUploadIdField,
      value: smfStore.fileUploadId,
    })
  }

  //#endregion FUNCTIONS
}
</script>

<style scoped lang="postcss">
.nu-teaser-card-upload {
  @apply h-full hover:border-primary-default hover:bg-primary-lightest;
}

.file-label {
  @apply block h-full cursor-pointer p-20 transition-colors;

  .is-selected & {
    @apply border-primary-default bg-primary-lightest;
  }

  .is-modal-opened & {
    cursor: inherit;
  }
}

.modal-button-wrapper {
  @apply flex justify-end gap-8 p-16 pt-0;
}
</style>
