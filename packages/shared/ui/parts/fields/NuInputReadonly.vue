<template>
  <div
    class="nu-input-readonly"
    :class="{
      'is-textarea': isTextArea,
      'justify-start pl-8': hozAlign == 'left',
      'justify-end': hozAlign == 'right',
    }"
    data-test="nu-input-readonly"
  >
    <template v-if="isArray">
      <span
        v-tooltip="tooltipOptions"
        v-data-test="'array-first-value'"
        class="truncate"
      >
        {{ arrayFirstValue }}
      </span>
      <p
        v-tooltip="tooltipOptions"
        v-data-test="'array-more-value'"
        class="mr-8 truncate"
      >
        {{ arrayMoreValue }}
      </p>
    </template>

    <template v-if="isPlainValue">
      <p
        v-tooltip="tooltipOptions"
        v-data-test="'plain-value'"
        class="mr-8 truncate"
      >
        {{ formattedValue }}
      </p>
    </template>

    <slot name="unit" />

    <template v-if="isTextArea">
      <div class="textarea w-full">
        <p
          v-data-test:value
          :class="[
            'whitespace-pre-line',
            textAreaShowMore ? 'p-open' : 'p-closed',
          ]"
        >
          {{ textAreaContent }}
        </p>
        <div v-if="displayTextAreaShowMoreButton" class="text-right">
          <a href="#" class="textarea-show-more" @click="toggleShowMore">{{
            textAreaShowMessage
          }}</a>
        </div>
      </div>
    </template>
  </div>
</template>

<script setup lang="ts">
import { userStore } from '@domain/usertset/data/user.store'
import { convert, type FieldType, type FieldUnit } from '@shared/field-types'
import {
  $formatField,
  $formatSelectableWithPath,
} from '@shared/format/formatField'
import { $formatNumber } from '@shared/format/formatNumber'
import { $nutDeep, $nutTextField, $t } from '@shared/translation/nuTranslation'
import { useInjectedManufacturing } from '@tset/shared-utils/composables/useInjectedManufacturing'
import { isTranslationFromPathNeeded } from '@tset/shared-utils/helpers/field'
import { getValue } from '@tset/shared-utils/helpers/getValue'
import { newFieldUnit } from '@tset/shared-utils/helpers/manufacturing'
import { toSelectable } from '@tset/shared-utils/helpers/selectable'
import { getTooltipForValue } from '@tset/shared-utils/helpers/tooltip'
import { computed, ref, watch } from 'vue'

const EMPTY_VALUE = '-'

//#region PROPS
const props = withDefaults(
  defineProps<{
    field: ResultField
    unit?: Nullable<FieldUnit>
    shouldValueBeFormatted?: boolean
    dataType?: FieldType
    currency?: Currency
    hozAlign?: 'left' | 'right'
  }>(),
  {
    unit: () => newFieldUnit(),
    shouldValueBeFormatted: true,
    currency: 'EUR',
    hozAlign: 'right',
  }
)
//#endregion PROPS

//#region DATA
const textAreaShowMore = ref<boolean>(false)
const textAreaMaxLineLength = ref<number>(45)
const convertedValue = ref<string>('')
const valueFromPath = ref<string>($t('statics.loading'))
//#endregion DATA

const manufacturing = useInjectedManufacturing()

const refersToAnEntity = (field: ResultField) =>
  ['EntityRef'].includes(field.type)

//#region COMPUTED
const formattedValue = computed(() => {
  if (refersToAnEntity(props.field) && props.field.value) {
    const entityDisplayDesignation = manufacturing?.value
      ?.getEntity(props.field.value?.toString())
      ?.getField('displayDesignation')
    if (entityDisplayDesignation) {
      return $formatField(entityDisplayDesignation)
    }
  }
  if (shouldLoadValueFromPath.value) {
    return valueFromPath.value
  }

  if (!props.shouldValueBeFormatted) {
    return String(props.field.value)
  }

  if (props.field.type === 'Null' || isEmptyValue.value) {
    return EMPTY_VALUE
  }
  if (shouldConvertValue.value) {
    return $formatNumber(convertedValue.value, {})
  }

  if (shouldFormatText.value) {
    return formattedText.value
  }

  if (shouldUseValueInDefaultUnit.value) {
    return $formatNumber(String(props.field.valueInDefaultUnit), {})
  }

  if (!props.unit) {
    const val =
      props.field.valueInDefaultUnit ?? getValue(props.field, props.currency)

    if (val == null) {
      return EMPTY_VALUE
    }

    return $formatNumber(String(val ?? ''), {})
  }

  if (props.field.value === null) {
    return EMPTY_VALUE
  }

  return $formatNumber(String(props.field.value), {})
})

const isEmptyValue = computed(() => {
  return !props.field.value && props.field.value !== 0
})

const shouldUseValueInDefaultUnit = computed(() => {
  const unitProp = props.unit

  return (
    unitProp &&
    unitProp.unit === props.field.metaInfo?.defaultUnit?.unit &&
    !!props.field.valueInDefaultUnit
  )
})

const shouldConvertValue = computed(() => {
  if (
    shouldUseValueInDefaultUnit.value ||
    !props.unit ||
    props.field.value === null
  ) {
    return false
  }

  const fieldUnit = props.field.unit
  const propsUnit = props.unit?.unit
  const fieldUnitDiffers = fieldUnit && fieldUnit !== propsUnit

  return fieldUnitDiffers
})

const shouldLoadValueFromPath = computed(() => {
  return isTranslationFromPathNeeded(props.field)
})

const isArray = computed(() => {
  if (isTextArea.value) {
    return false
  }

  const value = props.field.value
  return Array.isArray(value) && value.length > 0
})

const selectablesArray = computed(() => {
  if (!isArray.value) {
    return []
  }

  return (props.field.value as unknown[]).map(
    (value) => toSelectable(value)?.name
  )
})

const arrayFirstValue = computed(() => {
  return selectablesArray.value[0] ?? EMPTY_VALUE
})

const arrayMoreValue = computed(() => {
  const length = (props.field.value as []).length
  return length > 1 ? `+ ${length - 1}` : ''
})

const shouldFormatText = computed(() => {
  if (shouldUseValueInDefaultUnit.value) {
    return false
  }

  return (
    ['Text', 'Currency', 'Date'].includes(props.field.type) ||
    props.dataType?.selectables
  )
})

const formattedText = computed(() => {
  if (isEmptyValue.value) {
    return EMPTY_VALUE
  }

  return String(formatText(props.field)) || EMPTY_VALUE
})

const tooltipOptions = computed(() => {
  return {
    content: tooltipValue.value,
    placement: 'top',
  }
})

const tooltipValue = computed(() => {
  if (shouldLoadValueFromPath.value) {
    return valueFromPath.value
  }

  if (isEmptyValue.value) {
    return $t('tsetInput.missingValueTooltip')
  }

  // TextArea readonly doesn't require a tooltip
  if (isTextArea.value) {
    return null
  }
  if (isArray.value) {
    return selectablesArray.value.join(', ')
  }

  if (shouldFormatText.value) {
    return formattedText.value
  }

  let valueToFormat = shouldUseValueInDefaultUnit.value
    ? props.field.valueInDefaultUnit
    : getValue(props.field, props.currency)

  if (shouldConvertValue.value) {
    valueToFormat = convertedValue.value
  }
  const { decimalPrecision } = userStore.userSettings.formatting
  return getTooltipForValue(valueToFormat as number, decimalPrecision)
})

const isTextArea = computed<boolean>(() => {
  return !!props.field.metaInfo?.textArea
})

const isPlainValue = computed<boolean>(() => {
  return !isTextArea.value && !isArray.value
})

const textAreaShowMessage = computed<string>(() => {
  return $nutDeep(textAreaShowMore.value ? 'showLess' : 'showMore')
})

const displayTextAreaShowMoreButton = computed<boolean>(() => {
  return textAreaContent.value.length > 150
})

const textAreaValue = computed<Nullable<string>>(() => {
  return $nutTextField(props.field)
})

const textAreaContent = computed<string>(() => {
  let val = textAreaValue.value

  if (val) {
    while (getStringTextAreaLong(val)) {
      const longString = getStringTextAreaLong(val)
      const shortenedString = longString
        ?.substring(0, textAreaMaxLineLength.value)
        .concat(' ')
        .concat(longString.substring(textAreaMaxLineLength.value))
      val = val.replace(longString!, shortenedString!)
    }
  }

  return val?.trim() ?? ''
})

//#endregion COMPUTED

watch(
  [() => props.field.value, () => props.unit],
  async () => {
    if (props.field.unit && shouldConvertValue.value && props.unit) {
      const value = convert({
        type: props.field.type,
        from: props.field.unit,
        to: props.unit.unit,
        value: props.field.value as number,
      })
      convertedValue.value = String(value)
    }
  },
  { deep: true }
)

watch(
  shouldLoadValueFromPath,
  async (newValue) => {
    if (newValue) {
      const val = await $formatSelectableWithPath(props.field)
      if (val) {
        valueFromPath.value = val
      }
    }
  },
  { immediate: true }
)
//#endregion WATCHERS

//#region METHODS

/** When field has a label overwrite value with label to display it */
function formatText(field: ResultField): string {
  const fieldToFormat = field.label
    ? {
        ...field,
        value: field.label,
      }
    : { ...field }

  return $formatField(fieldToFormat)
}

function toggleShowMore() {
  textAreaShowMore.value = !textAreaShowMore.value
}

function getStringTextAreaLong(val: string): Nullable<string> {
  return (
    val.split(' ').find((x) => x.length > textAreaMaxLineLength.value) ?? null
  )
}
//#endregion METHODS

defineExpose({ tooltipValue, formatText })
</script>

<script lang="ts">
export default {
  name: 'NuInputReadonly',
}
</script>

<style lang="postcss" scoped>
.nu-input-readonly {
  @apply text-body-semibold flex h-full flex-row items-center
    rounded-lg bg-transparent
    py-6  text-left text-black-default;

  & :deep(.nu-side-units) {
    @apply mr-0;
  }
}

/* required for specificity to override height applied from outside the component */
.is-textarea {
  @apply h-full w-full;

  textarea {
    @apply bg-transparent;

    &:focus,
    &:hover {
      border-color: transparent;
      border-width: 1px;
    }
  }
}

.textarea-show-more {
  @apply text-primary-default;

  &:hover,
  &:focus {
    @apply text-primary-dark;
  }
}

.textarea {
  p {
    overflow: hidden;
  }

  .p-closed {
    @apply line-clamp-4;
  }

  .p-open {
    @apply h-full;
  }
}
</style>
