<template>
  <div :id="componentId" class="relative h-full" data-test="nu-input-number">
    <TsetInputNumeric
      :id="`tsetinput-${field.name}`"
      :field-value="fieldValue"
      :has-error="hasError"
      :has-warning="hasWarning"
      :maxlength="maxLength"
      :hide-outline="editableTableCell && !rowHovered"
      :placeholder="field.metaInfo?.placeHolder ?? '0'"
      :is-negative-numbers-allowed="isNegativeNumbersAllowed"
      :tset-table-editor="tsetTableEditor"
      :current-decimal-precision="
        userStore.userSettings.formatting.decimalPrecision
      "
      @blur="updateValue"
      @focusin="focusIn"
      @focusout="focusOut"
      @report-error="emit('report-error', $event)"
      @clear-error="emit('clear-error')"
    >
      <template #unit>
        <slot name="unit" />
      </template>
    </TsetInputNumeric>
    <slot name="system-value" :focused="isFocused" :float="true">
      <div
        v-if="isManuallyOverridden"
        data-test="nu-field-system-value-wrapper"
        class="relative z-1"
        @mousedown="restore"
      >
        <NuFieldSystemValue
          :wrapper-id="componentId"
          :field="field"
          :unit="unit"
          :tset-table-editor-last="tsetTableEditorLast"
          :is-parent-focused="isFocused"
        />
      </div>
    </slot>
  </div>
</template>
<script setup lang="ts">
import { userStore } from '@domain/usertset/data/user.store'
import { convert, typeContainsUnit, type FieldUnit } from '@shared/field-types'
import { $formatValueToBig } from '@shared/format/formatNumber'
import NuFieldSystemValue from '@tset/shared-ui/parts/fields/NuFieldSystemValue.vue'
import {
  generateUuid,
  isNotNullOrUndefined,
} from '@tset/shared-utils/helpers/general'
import { toNumber } from 'lodash'
import { computed, nextTick, ref } from 'vue'
import TsetInputNumeric from './TsetInputNumeric/TsetInputNumeric.vue'

//#region PROPS
const props = withDefaults(
  defineProps<{
    field: ResultField<number>
    unit?: Nullable<FieldUnit>
    maxLength?: number
    isManuallyOverridden?: boolean
    rowHovered?: boolean
    editableTableCell?: boolean
    tsetTableEditor?: boolean
    tsetTableEditorLast?: boolean
    hasError?: boolean
    hasWarning?: boolean
  }>(),
  {
    maxLength: 150,
    unit: null,
  }
)
//#endregion PROPS

//#region EMITS
const emit = defineEmits<{
  (e: 'restore'): void
  (e: 'focusin'): void
  (e: 'focusout'): void
  (e: 'change', field: ResultField): void
  (e: 'report-error', message: string): void
  (e: 'clear-error'): void
}>()

async function restore() {
  focusOut()
  emit('restore')
}

//#endregion EMITS

//#region DATA
const componentId = generateUuid(props.field.name)
//#endregion DATA

//#region COMPUTED
const isNegativeNumbersAllowed = computed(
  () => !!props.field.metaInfo?.allowNegative
)
//#endregion COMPUTED

//#region FIELD_INITIALIZATION
const fieldValue = computed(() => {
  const value = props.field.value
  if (isNotNullOrUndefined(value) && shouldUseValue(String(value))) {
    return toNumber($formatValueToBig(value))
  } else if (shouldUseValueInDefaultUnit()) {
    return props.field.valueInDefaultUnit ?? undefined
  } else if (shouldConvertValue()) {
    let result: ResultField<string | number>['value'] = convert({
      type: props.field.type,
      from: props.field.unit ?? '',
      to: conversionTargetUnit() as string,
      value: Number(value),
    })
    result = $formatValueToBig(result)
    return result ? toNumber(result) : undefined
  }
  return undefined
})

//#endregion FIELD_INITIALIZATION

//#region VALIDATION METHODS
/**
 * value should be used when
 * - the field.unit matches the given unit
 * - OR the field has no unit
 *
 * @returns {boolean}
 */
function shouldUseValue(value: string): boolean {
  const noUnitToUse = !props.field.unit
  const invalidUnits = props.field.unit === props.unit?.unit
  return noUnitToUse || invalidUnits
}

/**
 * valueInDefaultUnit should be used when
 * - the field has a valueInDefaultUnit
 * - AND the defaultUnit matches the given unit
 *
 * @returns {boolean}
 */
function shouldUseValueInDefaultUnit(): boolean {
  return (
    isNotNullOrUndefined(props.field.valueInDefaultUnit) &&
    props.field.metaInfo?.defaultUnit?.unit === props.unit?.unit
  )
}

/**
 * Value conversion should take place when
 * - the field has a value
 * - AND the field.unit does NOT match the given unit
 * - AND the value is convertible
 *
 * @returns {boolean}
 */
function shouldConvertValue(): boolean {
  const hasValue = !!props.field.value
  const hasValidUnit = !!props.unit && props.field.unit !== props.unit.unit
  return hasValue && hasValidUnit && canConvertValue()
}

/**
 * Value conversion can take place when
 * - the field.unit exists on the datatype
 * - AND the unit OR the defaultUnit exist on the dataType
 *
 * @returns {boolean}
 */
function canConvertValue(): boolean {
  return (
    Boolean(props.field.unit && unitExists(props.field.unit)) &&
    Boolean(conversionTargetUnit())
  )
}

/**
 * This method returns the unit which is the target for the conversion
 * If the given unit exists on the datatype, it is returned
 * Else if the defaultUnit exists on the datatype, it is returned
 * Else nothing is returned
 *
 * @returns {string | undefined} the unit which is the target for the conversion
 */
function conversionTargetUnit(): string | undefined {
  if (props.unit?.unit && unitExists(props.unit.unit)) {
    return props.unit.unit
  } else if (
    props.field.metaInfo?.defaultUnit?.unit &&
    unitExists(props.field.metaInfo?.defaultUnit?.unit)
  ) {
    return props.field.metaInfo?.defaultUnit?.unit
  }
}

function unitExists(unitToCheck: string) {
  return typeContainsUnit(props.field.type, unitToCheck)
}

//#endregion VALIDATION METHODS

//#region FOCUS STATE
const isFocused = ref<boolean>(false)

function focusIn(): void {
  isFocused.value = true
  emit('focusin')
}

function focusOut(): void {
  isFocused.value = false
  emit('focusout')
}
//#endregion FOCUS STATE

//#region INPUT
const allowEmptyValue = computed(() => props.field.metaInfo?.optional ?? false)

async function updateValue(value: string): Promise<void> {
  if (props.field.metaInfo?.min ?? props.field.metaInfo?.max) {
    await emitClampedValue(value)
  } else {
    await emitValue(value)
  }

  focusOut()
}

async function emitValue(value: string): Promise<void> {
  const emittedValue = allowEmptyValue.value && value === '' ? null : value
  emit('change', {
    ...props.field,
    value: toNumber(emittedValue),
    source: 'I',
    unit: props.unit?.unit ?? null,
  })
  await nextTick()
}

async function emitClampedValue(value: string): Promise<void> {
  let clampedValue = Number(value)
  if (props.unit?.unit) {
    let min = -Infinity
    let max = Infinity
    if (props.field.metaInfo?.min) {
      min = convert({
        type: props.field.type,
        from: props.field.unit!,
        to: props.unit!.unit,
        value: props.field.metaInfo.min,
      })
    }

    if (props.field.metaInfo?.max) {
      max = convert({
        type: props.field.type,
        from: props.field.unit!,
        to: props.unit!.unit,
        value: props.field.metaInfo.max,
      })
    }
    clampedValue = Math.min(Math.max(clampedValue, min), max)
  } else {
    if (props.field.metaInfo?.min) {
      clampedValue = Math.max(props.field.metaInfo.min, clampedValue)
    }
    if (props.field.metaInfo?.max) {
      clampedValue = Math.min(props.field.metaInfo.max, clampedValue)
    }
  }
  emit('change', {
    ...props.field,
    value: toNumber(clampedValue),
    source: 'I',
    unit: props.unit?.unit ?? null,
  })
}
//#endregion INPUT
</script>

<script lang="ts">
export default {
  name: 'NuInputNumber',
  components: {
    TsetInputNumeric,
    NuFieldSystemValue,
  },
}
</script>
