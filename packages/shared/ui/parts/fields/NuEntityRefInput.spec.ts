import TsetSelectListItem from '@tset/design/organisms/TsetSelect/TsetSelectListItem.vue'
import { gManufacturingEntity } from '@tset/shared-utils/tests/generators/manufacturingEntity'
import { gResultField } from '@tset/shared-utils/tests/generators/resultField'
import { createMockComponent } from '@tset/shared-utils/tests/mocks/withLocalVue'
import { shallowMount, type ComponentMountingOptions } from '@vue/test-utils'
import NuEntityRefInput from './NuEntityRefInput.vue'

//#region MOCKS
vi.mock('@/store/knowledge', () => {
  return {
    startAddObject: vi.fn(),
  }
})

const addObjectToCalculationMock = vi.fn()
// @ts-expect-error This is a hack for the global method for this one off special case
window.addObjectToCalculation = addObjectToCalculationMock

const entityType = 'MANUFACTURING_STEP'
const fieldName = 'field-name'
const parentId = 'parent-id'
const overwrittenParentId = 'overwritten-parent-id'
const entityClasses = ['entity-class']
//#endregion MOCKS

//#region SETUP FACTORY
const given = ({
  withEntityType = false,
  withOverwrittenParentInfo = false,
  withEntityClasses = false,
} = {}) => {
  const itemHoverMock = vi.fn()
  const closeMock = vi.fn()
  vi.clearAllMocks()
  const props: InstanceType<typeof NuEntityRefInput>['$props'] = {
    field: gResultField({
      name: fieldName,
      metaInfo: {
        entityType: withEntityType ? [entityType] : undefined,
        overwriteParentInfo: withOverwrittenParentInfo
          ? { id: overwrittenParentId }
          : undefined,
        entityClasses: withEntityClasses ? entityClasses : undefined,
      },
    }),
    parentEntity: gManufacturingEntity({ id: parentId }),
    dataType: null,
    multiple: null,
    withSystemValue: false,
  }
  const mountOptions: ComponentMountingOptions<typeof NuEntityRefInput> = {
    props: {
      ...props,
    },
    global: {
      stubs: {
        NuInputSelect: createMockComponent('NuInputSelect', {
          'footer-items': { itemHover: itemHoverMock, close: closeMock },
        }),
      },
    },
  }

  const wrapper = shallowMount(NuEntityRefInput, mountOptions)

  //#region HELPERS
  const getSelectListItemWrapper = () =>
    wrapper.findByDataTest('nu-entity-ref-input-footer-items')
  const hoverSelectListItemWrapper = () =>
    getSelectListItemWrapper().trigger('mouseenter')
  const clickSelectListItemWrapper = () =>
    getSelectListItemWrapper().trigger('click')
  const getSelectListItem = () => wrapper.findComponent(TsetSelectListItem)
  const getSelectListItemProp = () => getSelectListItem().props('item')
  //#endregion HELPERS

  return {
    when: {
      clickSelectListItemWrapper,
      hoverSelectListItemWrapper,
    },
    then: {
      getSelectListItemProp,
      itemHoverMock,
      closeMock,
    },
  }
}
//#endregion SETUP FACTORY

//#region TESTS
describe('NuEntityRefInput', () => {
  describe('when mounted', () => {
    it('- renders the select list item', () => {
      const { then } = given()

      expect(then.getSelectListItemProp()).toEqual({
        selectable: {
          key: 'new-entity',
          name: 'statics.addSomething',
        },
      })
    })
  })

  describe('when clicking the list item', () => {
    it('- closes the popup', async () => {
      const { when, then } = given()

      expect(then.closeMock).not.toHaveBeenCalled()
      await when.clickSelectListItemWrapper()
      expect(then.closeMock).toHaveBeenCalled()
    })

    describe('when entity type is provided', () => {
      describe('when the field has `overwriteParentInfo` meta info', () => {
        it('- opens the knowledge modal', async () => {
          const { when } = given({
            withEntityType: true,
            withOverwrittenParentInfo: true,
          })

          await when.clickSelectListItemWrapper()
          expect(addObjectToCalculationMock).toHaveBeenCalledWith({
            type: entityType,
            tabsToShow: ['calculationModule'],
            linkEntityField: fieldName,
            linkEntityId: overwrittenParentId,
            entityClassFilters: undefined,
          })
        })
      })

      describe('when the field does not have `overwriteParentInfo` meta info', () => {
        it('- opens the knowledge modal', async () => {
          const { when } = given({
            withEntityType: true,
          })

          await when.clickSelectListItemWrapper()
          expect(addObjectToCalculationMock).toHaveBeenCalledWith({
            type: entityType,
            tabsToShow: ['calculationModule'],
            linkEntityField: fieldName,
            linkEntityId: parentId,
            entityClassFilters: undefined,
          })
        })
      })

      describe('when entity classes are provided', async () => {
        it('- sends entity class filters to the knowledge modal', async () => {
          const { when } = given({
            withEntityType: true,
            withEntityClasses: true,
          })

          await when.clickSelectListItemWrapper()

          expect(addObjectToCalculationMock).toHaveBeenCalledWith({
            type: entityType,
            tabsToShow: ['calculationModule'],
            linkEntityField: fieldName,
            linkEntityId: parentId,
            entityClassFilters: entityClasses,
          })
        })
      })
    })

    describe('when entity type in not provided', () => {
      it('- does not open the knowledge modal', async () => {
        const { when } = given()

        await when.clickSelectListItemWrapper()
        expect(addObjectToCalculationMock).not.toHaveBeenCalled()
      })
    })
  })

  describe('when hovering the list item', () => {
    it('- triggers hover of the select component', async () => {
      const { when, then } = given()

      expect(then.itemHoverMock).not.toHaveBeenCalled()
      await when.hoverSelectListItemWrapper()
      expect(then.itemHoverMock).toHaveBeenCalledWith(-1)
    })
  })
})
//#endregion TESTS
