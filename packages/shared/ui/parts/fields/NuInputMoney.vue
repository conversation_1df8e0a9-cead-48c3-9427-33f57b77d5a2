<template>
  <div :id="componentId" class="relative h-full" data-test="nu-input-money">
    <TsetInputNumeric
      :id="`tsetinput-${field.name}`"
      :field-value="localValue"
      :hide-outline="editableTableCell && !rowHovered"
      :has-error="hasError"
      :sub-value="subValue ?? ''"
      :placeholder="field.metaInfo?.placeHolder ?? '0'"
      :is-negative-numbers-allowed="isNegativeNumbersAllowed"
      :tset-table-editor="tsetTableEditor"
      :current-decimal-precision="
        userStore.userSettings.formatting.decimalPrecision
      "
      @blur="updateValue"
      @input="setCurrentValue"
      @focusin="focusIn"
      @focusout="focusOut"
      @report-error="emit('report-error', $event)"
      @clear-error="emit('clear-error')"
    >
      <template #unit>
        <slot name="unit" />
        <IconWarningCurrency
          v-if="showDifferentCurrencyIcon"
          v-tooltip="tooltipOptions"
          data-test="different-currency-icon"
          class="absolute -right-8 -top-5 z-999 h-16 w-16 cursor-help text-primary-default"
        />
      </template>
    </TsetInputNumeric>
    <slot name="system-value" :focused="focused" :float="true">
      <div
        v-if="isManuallyOverridden"
        v-data-test:nu-field-system-value-wrapper
        @mousedown="restore"
      >
        <NuFieldSystemValue
          :wrapper-id="componentId"
          :field="field"
          :unit="unit"
          :tset-table-editor-last="tsetTableEditorLast"
          :is-parent-focused="focused"
          :object-base-currency="objectBaseCurrency"
          :base-system-currency="baseCurrencyUnitFormatted"
          :different-currency="displayCurrencyIsDifferent"
        />
      </div>
    </slot>
  </div>
</template>

<script setup lang="ts">
import { userStore } from '@domain/usertset/data/user.store'
import type { FieldUnit } from '@shared/field-types'
import { $t } from '@shared/translation/nuTranslation'
import { useDisplayCurrency } from '@tset/shared-utils/api/useDisplayCurrency'
import { calculateBaseCurrencyValue } from '@tset/shared-utils/helpers/field'
import {
  generateUuid,
  isNotNullOrUndefined,
} from '@tset/shared-utils/helpers/general'
import { getValue } from '@tset/shared-utils/helpers/getValue'
import { isNullOrUndefined } from '@tset/shared-utils/helpers/isNullOrUndefined'
import { getField } from '@tset/shared-utils/helpers/manufacturing'
import { toNumber } from 'lodash'
import type { ComputedRef } from 'vue'
import { computed, inject, ref } from 'vue'
import NuFieldSystemValue from './NuFieldSystemValue.vue'
import TsetInputNumeric from './TsetInputNumeric/TsetInputNumeric.vue'

//#region COMPOSABLES
const { displayCurrency } = useDisplayCurrency()
//#endregion COMPOSABLES

//#region PROPS
const props = withDefaults(
  defineProps<{
    field: ResultField
    unit?: Nullable<FieldUnit>
    isManuallyOverridden: boolean
    parentEntity?: Nullable<ManufacturingDTO>
    showCurrencyIcon?: boolean
    shouldValueBeFormatted?: boolean
    rowHovered?: boolean
    editableTableCell?: boolean
    tsetTableEditor?: boolean
    tsetTableEditorLast?: boolean
    disableSubValue?: boolean
    currency?: Currency
    hasError?: boolean
  }>(),
  {
    unit: null,
    parentEntity: null,
    showCurrencyIcon: true,
    shouldValueBeFormatted: true,
    currency: undefined,
    hasError: false,
  }
)
//endregion PROPS

//#region INJECT
// ExchangeRates comes as two types of objects: Either a computed from the Options API or a simple Partial<Record<Currency, number>>
// from the new ones. We have to receive as an any due to this reason and handle it on the functions.
const exchangeRates: any = inject('exchangeRates', null)
const calculationBaseCurrency: ComputedRef<Nullable<Currency>> = inject(
  'calculationBaseCurrency',
  computed(() => null)
)
const context: ComputedRef<Nullable<string>> = inject(
  'context',
  computed(() => null)
)
const manualEntryBaseCurrency: ComputedRef<Nullable<Currency>> = inject(
  'objectBaseCurrency',
  computed(() => null)
)
//#endregion INJECT

//#region EMITS
const emit = defineEmits<{
  (e: 'focusin'): void
  (e: 'focusout'): void
  (e: 'restore'): void
  (e: 'change', payload: ResultField): void
  (e: 'report-error', message: string): void
  (e: 'clear-error'): void
}>()

function focusIn(): void {
  focused.value = true
  emit('focusin')
}

function focusOut(): void {
  focused.value = false
  emit('focusout')
}

async function change(value: string | number) {
  emit('change', {
    ...props.field,
    value: Number(value),
    source: 'I',
  })
}

function restore() {
  focusOut()
  emit('restore')
}

function emitClampedValue(value: string) {
  let clampedValue = Number(value)
  if (props.field.metaInfo?.min) {
    clampedValue = Math.max(props.field.metaInfo.min, clampedValue)
  }
  if (props.field.metaInfo?.max) {
    clampedValue = Math.min(props.field.metaInfo.max, clampedValue)
  }
  change(clampedValue)
}
//#endregion EMITS

//#region DATA
const focused = ref(false)
const componentId = generateUuid(props.field.name)
//#endregion DATA

//#region COMPUTED
const isNegativeNumbersAllowed = computed(
  () => !!props.field.metaInfo?.allowNegative
)
//#endregion COMPUTED

//#region INPUT INTERACTIONS
const localValue = computed(() => {
  const value = getValue<Nullable<number>>(
    props.field,
    currentDisplayCurrency.value
  )
  if (isNullOrUndefined(value)) {
    return undefined
  }
  if (isNullOrUndefined(currentValue.value)) {
    setCurrentValue(value.toString())
  }
  return value
})
// This value exists to receive the update:modelValue from the TsetInputNumeric
// to enable the update of the subValue on update of data by the user
const currentValue = ref<number | undefined>(undefined)

function setCurrentValue(value: string | undefined) {
  currentValue.value = isNotNullOrUndefined(value) ? toNumber(value) : 0
}

/**
 * creates and returns the options-object for the v-tooltip
 *
 * @returns VTooltipOptions (the options-object for the v-tooltip)
 */
const tooltipOptions = computed<VTooltipOptions>(() => ({
  content: $t('statics.differentCurrency'),
  placement: 'right',
  classes: ['currency-tooltip'],
}))

const subValue = computed<Nullable<string>>(() => {
  if (
    displayCurrencyIsDifferent.value &&
    !props.disableSubValue &&
    exchangeRates &&
    isNotNullOrUndefined(currentValue.value)
  ) {
    return (
      calculateBaseCurrencyValue({
        value: currentValue.value,
        exchangeRates: exchangeRates.value
          ? exchangeRates.value
          : exchangeRates,
        displayCurrency: currentDisplayCurrency.value,
        baseCurrency: objectBaseCurrency.value,
      }) +
      ' ' +
      baseCurrencyUnitFormatted.value
    )
  }
  return null
})

const allowEmptyValue = computed(() => props.field.metaInfo?.optional ?? false)

function updateValue(value: string): void {
  if (allowEmptyValue.value && value === '' && props.field.value) {
    emit('change', {
      ...props.field,
      value: null,
      source: 'I',
    })
  } else if (props.field.metaInfo?.min || props.field.metaInfo?.max) {
    emitClampedValue(value)
  } else {
    change(value)
  }

  focusOut()
}
//#endregion INPUT INTERACTIONS

//#region CURRENCIES
const currentDisplayCurrency = computed<Currency>(
  () => props.currency ?? displayCurrency.value
)

const calculationCurrencyIsDifferent = computed<boolean>(() => {
  const baseCurrency = calculationBaseCurrency?.value
  if (!baseCurrency) {
    return false
  }
  return hasDifferentCurrency(baseCurrency)
})

const displayCurrencyIsDifferent = computed<boolean>(() =>
  hasDifferentCurrency(currentDisplayCurrency.value)
)

const hasDifferentCurrency = (currency: Currency) =>
  !!objectBaseCurrency.value && currency !== objectBaseCurrency.value

const objectBaseCurrency = computed<Nullable<Currency>>(() => {
  if (props.parentEntity) {
    const fieldname: string =
      context?.value === 'calculation'
        ? 'baseCurrency'
        : 'masterdataBaseCurrency'
    return getField(props.parentEntity, fieldname)?.value as Currency
  } else if (manualEntryBaseCurrency?.value) {
    return manualEntryBaseCurrency.value
  } else {
    return null
  }
})

const baseCurrencyUnitFormatted = computed(() => objectBaseCurrency.value ?? '')

const showDifferentCurrencyIcon = computed<boolean>(() => {
  return !!(
    calculationCurrencyIsDifferent.value && context?.value === 'calculation'
  )
})
//#endregion CURRENCIES
</script>

<script lang="ts">
export default {
  name: 'NuInputMoney',
  components: {
    NuFieldSystemValue,
    TsetInputNumeric,
  },
}
</script>
