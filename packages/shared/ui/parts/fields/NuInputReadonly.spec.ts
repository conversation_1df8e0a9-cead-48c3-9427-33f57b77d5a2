import { selectablesStore } from '@/store'
import type { FieldType, FieldUnit } from '@shared/field-types'
import * as formatField from '@shared/format/formatField'
import NuInputReadonly from '@tset/shared-ui/parts/fields/NuInputReadonly.vue'
import {
  gManufacturingEntity,
  initManufacturingDbWithManufacturing,
} from '@tset/shared-utils/tests/generators/manufacturingEntity'
import { gResultField } from '@tset/shared-utils/tests/generators/resultField'
import { mount } from '@vue/test-utils'
import { nextTick, ref } from 'vue'

vi.mock('@/store', async () => ({
  userStore: { displayCurrency: 'EUR' },
  selectablesStore: {
    isFetchingNeeded: () => true,
    isPending: () => true,
    fetchEntities: () => [],
    selectables: () => [],
  },
}))

const veryLongTextareaField = {
  value: Array(180).fill('a').join(''),
  metaInfo: { textArea: { rows: 3 } },
}

describe('NuInputReadonly.vue', () => {
  describe('Snapshots', () => {
    it('should mount properly', async () => {
      const { wrapper } = await setup({ field: veryLongTextareaField })
      expect(wrapper).toMatchSnapshot()
    })
  })

  describe('when mounted', () => {
    it('- show the value', async () => {
      const { getDisplayedValue } = await setup({})
      expect(getDisplayedValue()).toBe('some-value')
    })
  })

  describe('when the value is null', () => {
    it('- show "-"', async () => {
      const { getDisplayedValue } = await setup({ field: { value: null } })
      expect(getDisplayedValue()).toBe('-')
    })
  })

  describe('when the value is 0', () => {
    it('- show "0"', async () => {
      const { getDisplayedValue } = await setup({ field: { value: 0 } })
      expect(getDisplayedValue()).toBe('0')
    })
  })

  describe('when the field has type:', () => {
    describe('Number', () => {
      it('- show field.value', async () => {
        const { getDisplayedValue } = await setup({
          field: { type: 'Number', value: 123 },
        })
        expect(getDisplayedValue()).toBe('123.00')
      })
    })

    describe('Area', () => {
      it('- show field.value', async () => {
        const { getDisplayedValue } = await setup({
          field: { type: 'Area', value: 456 },
        })
        expect(getDisplayedValue()).toBe('456.00')
      })
    })

    describe('Money', () => {
      it('- show field.value', async () => {
        const { getDisplayedValue } = await setup({
          field: { type: 'Money', value: 789 },
        })
        expect(getDisplayedValue()).toBe('789.00')
      })
    })

    describe('Power', () => {
      it('- display field.value', async () => {
        const { getDisplayedValue } = await setup({
          field: { type: 'Power', value: 2.34 },
        })
        expect(getDisplayedValue()).toBe('2.34')
      })
    })

    describe('Rate', () => {
      // looks like it's formatted as percents at the NuField level
      // so here it just rounded
      it('- display field.value ', async () => {
        const { getDisplayedValue } = await setup({
          field: { type: 'Rate', value: 12.123 },
        })
        expect(getDisplayedValue()).toBe('12.12')
      })
    })

    describe('Text', () => {
      it('- display field.label (instead of .value)', async () => {
        const { getDisplayedValue } = await setup({
          field: { type: 'Text', value: 'some-value', label: 'some-label' },
        })
        expect(getDisplayedValue()).toBe('some-label')
      })

      describe('if no .label', () => {
        it('- display field.value', async () => {
          const { getDisplayedValue } = await setup({
            field: { type: 'Text', value: 'some-value' },
          })
          expect(getDisplayedValue()).toBe('some-value')
        })
      })
    })

    describe('Date', () => {
      it('- uses formatField for date', async () => {
        const { $formatFieldMock } = await setup({
          field: { type: 'Date', value: '2024-11-03' },
        })
        expect($formatFieldMock).toHaveBeenCalledWith({
          name: 'some-field',
          source: 'I',
          type: 'Date',
          value: '2024-11-03',
        })
      })
    })

    describe('Weight', () => {
      it('- display field.value', async () => {
        const { getDisplayedValue } = await setup({
          field: {
            type: 'Weight',
            value: 456,
          },
        })
        expect(getDisplayedValue()).toBe('456.00')
      })
    })

    describe('EntityRef', () => {
      const entityRefField = gResultField({
        type: 'EntityRef',
        value: 'entity-2',
        metaInfo: { path: '/some/path' },
      })
      const manufacturing = gManufacturingEntity({
        children: [
          gManufacturingEntity({
            id: 'entity-2',
            type: 'BOM_ENTRY',
            fields: [
              gResultField({
                name: 'displayDesignation',
                value: 'entity-2-disp-desig',
              }),
            ],
          }),
        ],
      })
      describe('when manufacturing is provided', () => {
        it('- uses entity from manufacturing', async () => {
          const { getDisplayedValue } = await setup({
            field: entityRefField,
            manufacturing,
          })
          expect(getDisplayedValue()).toBe('entity-2-disp-desig')
        })
      })

      describe('fallback to metainfo.path if there is no provided manufacturing:', () => {
        it('- show "loading..."', async () => {
          const { getDisplayedValue } = await setup({
            field: entityRefField,
            withLongSelectablesLoading: true,
          })

          expect(getDisplayedValue()).toBe('statics.loading')
        })

        it('- load the real value from metaInfo.path', async () => {
          const fetchSpy = vi.spyOn(selectablesStore, 'fetchEntities')

          await setup({ field: entityRefField })

          expect(fetchSpy).toHaveBeenCalled()
        })

        it('- show the loaded value', async () => {
          const { getDisplayedValue } = await setup({
            field: entityRefField,
            withLoadedSelectables: true,
          })
          await Promise.resolve()

          expect(getDisplayedValue()).toBe('name-2')
        })
      })
    })
  })

  describe('whe metaInfo has textArea option', () => {
    it('- show as text', async () => {
      const { getDisplayedValue } = await setup({
        field: {
          value: 'some text',
          metaInfo: { textArea: { rows: 3 } },
        },
      })

      expect(getDisplayedValue()).toContain('some text')
    })

    describe('if the text is too long', () => {
      it('- show "show more" button', async () => {
        const { isShowMoreVisible } = await setup({
          field: veryLongTextareaField,
        })

        expect(isShowMoreVisible()).toBeTruthy()
      })
    })

    describe('if the text contains very long words', () => {
      it('- split them with space', async () => {
        const { getDisplayedValue } = await setup({
          field: {
            value: 'aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa',
            metaInfo: { textArea: { rows: 3 } },
          },
        })

        expect(getDisplayedValue()).toBe(
          'aaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaaa aaaaa'
        )
      })
    })
  })

  describe('when the field has no unit', () => {
    describe('and has ..valueInDefaultUnit', () => {
      describe('if the field type is "Text" or "Hierarchy"', () => {
        it('- display field.label', async () => {
          const { getDisplayedValue } = await setup({
            field: {
              type: 'Text',
              value: 123,
              label: 'some-label',
            },
          })
          expect(getDisplayedValue()).toBe('some-label')
        })

        describe('else', () => {
          it('- display field.valueInDefaultUnit', async () => {
            const { getDisplayedValue } = await setup({
              field: {
                type: 'Number',
                value: 123,
                valueInDefaultUnit: 456,
              },
            })
            expect(getDisplayedValue()).toBe('456.00')
          })
        })
      })
    })
  })

  describe('when dataType has selectables', () => {
    describe('for single value field', () => {
      it('- show field.label', async () => {
        const { getDisplayedValue } = await setup({
          field: {
            type: 'SomeType',
            value: 'some-value',
            label: 'some-label',
          },
          dataType: {
            type: 'NotImportant',
            selectables: [],
          },
        })

        expect(getDisplayedValue()).toBe('some-label')
      })
    })
  })

  describe('when the field.value is an array', () => {
    describe('if contains single value', () => {
      it('- show the value without counter', async () => {
        const { getDisplayedValue } = await setup({
          field: {
            type: 'Text',
            value: ['the-only-value'],
          },
        })

        expect(getDisplayedValue()).toBe('the-only-value')
      })
    })

    describe('if contains multiple values', () => {
      it('- show 1st value', async () => {
        const { getDisplayedValue } = await setup({
          field: {
            type: 'Text',
            value: ['some-text', 'some-other-text', 'some-more-text'],
          },
        })

        expect(getDisplayedValue()).toContain('some-text')
      })

      it('- show "+ N-1" counter', async () => {
        const { getDisplayedValue } = await setup({
          field: {
            type: 'Text',
            value: ['some-text', 'some-other-text', 'some-more-text'],
          },
        })

        expect(getDisplayedValue()).toContain('+ 2')
        expect(getDisplayedValue()).toBe('some-text+ 2')
      })
    })
  })

  describe('when props.unit updated', () => {
    describe('if field has no unit', () => {
      it('- show field.value', async () => {
        const { updateUnitProp, getDisplayedValue } = await setup({
          field: { value: 123.12345 },
        })

        await updateUnitProp({ unit: 'Meter', factor: 1 })

        expect(getDisplayedValue()).toBe('123.12345')
      })
    })

    describe('if the new unit is empty', () => {
      it('- show field.value', async () => {
        const { updateUnitProp, getDisplayedValue } = await setup({
          field: { value: 123.12345 },
        })

        await updateUnitProp(null)
        expect(getDisplayedValue()).toBe('123.12345')
        await updateUnitProp(undefined)
        expect(getDisplayedValue()).toBe('123.12345')
        await updateUnitProp(null)
        expect(getDisplayedValue()).toBe('123.12345')
      })
    })

    describe('if new props.unit is the same as field.unit ', () => {
      it('- show field.valueInDefaultUnit with unit', async () => {
        const FIELD_UNIT = 'M'

        const { updateUnitProp, getDisplayedValue } = await setup({
          field: { value: 123, valueInDefaultUnit: 456, unit: FIELD_UNIT },
        })

        await updateUnitProp({ unit: FIELD_UNIT, factor: 1 })

        expect(getDisplayedValue()).toBe('456 M')
      })
    })

    describe('if new props.unit is different than field.unit ', async () => {
      const FIELD_UNIT = 'METER'
      const { updateUnitProp, getDisplayedValue, getTooltipText } = await setup(
        {
          field: {
            value: 45.123321,
            valueInDefaultUnit: 0.45123,
            type: 'Length',
            unit: FIELD_UNIT,
          },
        }
      )
      await updateUnitProp({ unit: 'CENTIMETER', factor: 100 })

      it('- show formatted field.value', async () => {
        expect(getDisplayedValue()).toBe('4 512.33')
      })

      it('- show unformatted tooltip', async () => {
        expect(getTooltipText()).toBe('4 512.3321')
      })
    })

    describe('if new props.unit is the same as field.metaInfo.defaultUnit', () => {
      it('- display the field.valueInDefaultUnit', async () => {
        const DEFAULT_UNIT = 'KG'
        const FIELD_UNIT = 'G'

        const { updateUnitProp, getDisplayedValue } = await setup({
          field: {
            type: 'Text',
            value: 123,
            unit: FIELD_UNIT,
            valueInDefaultUnit: 456,

            metaInfo: {
              defaultUnit: {
                unit: DEFAULT_UNIT,
                isFixed: false,
              },
            },
          },
        })

        await updateUnitProp({ unit: DEFAULT_UNIT, factor: 1 })

        expect(getDisplayedValue()).toBe('456.00')
      })
    })

    describe('if new props.unit not found in the field units', () => {
      it('- show properly fields value formatted', async () => {
        const { updateUnitProp, getDisplayedValue } = await setup({
          field: {
            type: 'Length',
            value: 45.123,
            unit: 'METER',
            valueInDefaultUnit: 456,

            metaInfo: {
              defaultUnit: {
                unit: 'CENTIMETER',
                isFixed: false,
              },
            },
          },
        })

        await updateUnitProp({ unit: 'NONEXISTENT', factor: 1000 })

        expect(getDisplayedValue()).toBe('45.12')
      })
    })
  })

  describe('display tooltip', () => {
    describe('when field has .label', () => {
      it('- show field.label', async () => {
        const { getTooltipText } = await setup({
          field: { label: 'field-label' },
        })

        expect(getTooltipText()).toBe('field-label')
      })
    })

    describe('when props.unit is the same as metaInfo.defaultUnit', () => {
      describe('and field has .valueInDefaultUnit', () => {
        it('- show field.valueInDefaultUnit', async () => {
          const { getTooltipText } = await setup({
            field: {
              type: 'Number',
              value: 123.123456,
              unit: 'KG',
              valueInDefaultUnit: 456.123456,

              metaInfo: {
                defaultUnit: {
                  unit: 'KG',
                  isFixed: false,
                },
              },
            },
            unit: { unit: 'KG', factor: 1 },
          })

          expect(getTooltipText()).toBe('456.123456')
        })
      })
    })

    describe('for Money type', () => {
      it('- show value with 4 decimal digits precision', async () => {
        const { getTooltipText } = await setup({
          field: {
            value: 77,
            type: 'Money',
            currencyInfo: {
              EUR: 123.131302568,
              GBP: 234.222203774,
            },

            valueInDefaultUnit: null,
          },
          dataType: { type: 'Money' },
        })

        expect(getTooltipText()).toBe('123.131302568')
      })
    })

    describe('for Text type', () => {
      it('- show formatted text', async () => {
        const { getTooltipText } = await setup({
          field: {
            value: 'some text',
            type: 'Text',
          },
        })

        expect(getTooltipText()).toBe('some text')
      })
    })
  })

  describe('.formatText()', () => {
    it('- pass the field to $formatField', async () => {
      const { formatText, $formatFieldMock } = await setup({})

      formatText({
        type: 'Text',
        value: 'some-value',
      } as ResultField)

      expect($formatFieldMock).toHaveBeenCalledWith({
        type: 'Text',
        value: 'some-value',
      })
    })

    it('- return the formatted value', async () => {
      const { formatText } = await setup({})

      const result = formatText(
        gResultField({
          type: 'Text',
          value: 'some-value',
        })
      )

      expect(result).toBe('some-value')
    })

    describe('if the field has label', () => {
      it('- pass field.label instead of field.value', async () => {
        const { formatText, $formatFieldMock } = await setup({})

        const field = gResultField({
          type: 'Text',
          value: 'some-value',
          label: 'some-label',
        })

        formatText(field)

        expect($formatFieldMock).toHaveBeenCalledWith({
          ...field,
          type: 'Text',
          value: 'some-label',
          label: 'some-label',
        })
      })
    })
  })
})

type SetupOptions = {
  field?: Partial<ResultField>
  dataType?: FieldType
  unit?: Nullable<FieldUnit>
  withLongSelectablesLoading?: boolean
  withLoadedSelectables?: boolean
  manufacturing?: ManufacturingDTO
}
async function setup({
  field = {},
  dataType = { type: 'Text' },
  unit = null,
  withLongSelectablesLoading = false,
  withLoadedSelectables,
  manufacturing,
}: SetupOptions) {
  if (withLongSelectablesLoading) {
    vi.spyOn(selectablesStore, 'fetchEntities').mockReturnValueOnce(
      new Promise((res) => setTimeout(res, 0))
    )
  }

  if (withLoadedSelectables) {
    vi.spyOn(selectablesStore, 'selectables').mockReturnValue([
      { name: 'name-1', key: 'entity-1' },
      { name: 'name-2', key: 'entity-2' },
    ])
  }

  const mountOptions = {
    props: {
      dataType,
      field: gResultField({
        type: 'Text',
        value: 'some-value',
        name: 'some-field',
        ...field,
      }),
      unit,
    },
    global: {
      provide: {
        loadedManufacturing: manufacturing
          ? ref(initManufacturingDbWithManufacturing(manufacturing))
          : null,
      },
    },
  }

  const $formatFieldMock = vi.spyOn(formatField, '$formatField')

  const wrapper = mount(NuInputReadonly, mountOptions)

  await nextTick()

  const updateUnitProp = async (unit: Nullable<FieldUnit> | undefined) =>
    wrapper.setProps({ unit })

  const getDisplayedValue = () =>
    wrapper.find('[data-test=nu-input-readonly]').text()
  const isShowMoreVisible = () => wrapper.find('.textarea-show-more').exists()

  const getTooltipText = () => wrapper.vm.tooltipValue

  const formatText = (field: ResultField) => wrapper.vm.formatText(field)

  return {
    wrapper,
    getDisplayedValue,
    updateUnitProp,
    getTooltipText,
    $formatFieldMock,
    formatText,
    isShowMoreVisible,
  }
}
