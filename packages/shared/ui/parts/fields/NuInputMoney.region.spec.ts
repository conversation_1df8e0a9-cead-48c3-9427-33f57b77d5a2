import type { FieldUnit } from '@shared/field-types'
import TsetInput from '@tset/design/atoms/TsetInput'
import {
  getRegions,
  selectRegion,
  type Region,
} from '@tset/shared-utils/helpers/region/region'
import { useNumbro } from '@tset/shared-utils/plugins/numbro'
import { gResultField } from '@tset/shared-utils/tests/generators/resultField'
import { useTestBaseSetupComponentFunctions } from '@tset/shared-utils/tests/setupFactory/setupFactory.utils'
import { mount } from '@vue/test-utils'
import { computed, nextTick } from 'vue'
import NuInputMoney from './NuInputMoney.vue'
//#region LOCALVUE

//#region SETUP FACTORY
type SetupOptions = {
  // Props
  field?: ResultField
  unit?: Nullable<FieldUnit>
  disabled?: boolean
  isManuallyOverridden?: boolean
  parentEntity?: Nullable<ManufacturingDTO>
  showCurrencyIcon?: boolean
  shouldValueBeFormatted?: boolean
  rowHovered?: boolean
  editableTableCell?: boolean
  tsetTableEditor?: boolean
  tsetTableEditorLast?: boolean
  disableSubValue?: boolean
  currency?: Currency
  // Injections
  exchangeRates?: Partial<Record<Currency, number>>
  calculationBaseCurrency?: Currency
  context?: ApplicationContext
  objectBaseCurrency?: Currency
}

const defaultField = gResultField({
  type: 'Money',
  value: '2222',
})
const given = ({
  field = defaultField,
  unit = null,
  disabled = false,
  isManuallyOverridden = false,
  parentEntity = null,
  showCurrencyIcon = true,
  shouldValueBeFormatted = true,
  rowHovered = false,
  editableTableCell = false,
  tsetTableEditor = false,
  tsetTableEditorLast = false,
  disableSubValue = false,
  currency = 'EUR',
  exchangeRates = { EUR: 1 },
  calculationBaseCurrency = 'EUR',
  context = 'calculation',
  objectBaseCurrency = 'EUR',
}: SetupOptions = {}) => {
  const mountOptions = {
    props: {
      field,
      unit,
      disabled,
      isManuallyOverridden,
      parentEntity,
      showCurrencyIcon,
      shouldValueBeFormatted,
      rowHovered,
      editableTableCell,
      tsetTableEditor,
      tsetTableEditorLast,
      disableSubValue,
      currency,
    },
    global: {
      stubs: ['IconWarningCurrency'],
      provide: {
        originalUnit: {},
        exchangeRates,
        calculationBaseCurrency: computed(() => calculationBaseCurrency),
        context: computed(() => context),
        objectBaseCurrency: computed(() => objectBaseCurrency),
      },
    },
  }

  const wrapper = mount(NuInputMoney, mountOptions)
  const focusIn = async () => {
    await getTsetInput().findByDataTest('input-element').trigger('focusin')
  }
  const inputFromKeyboard = async (value: string) => {
    await getTsetInput().findByDataTest('input-element').setValue(value)
    await nextTick()
  }
  const focusOut = async () => {
    await getTsetInput().findByDataTest('input-element').trigger('blur')
    await nextTick()
  }
  const getTsetInput = () => wrapper.findComponent(TsetInput)
  const { getProp: getTsetInputProp } =
    useTestBaseSetupComponentFunctions(getTsetInput())
  const getUnformattedValue = () => getTsetInputProp('unformattedValue')
  const getFormattedValue = () => getTsetInputProp('formattedValue')

  const changeValue = async (value: string) => {
    await focusIn()
    await inputFromKeyboard(value)
    await focusOut()
    const changedValue = wrapper
      .emitted<ResultField[]>('change')
      ?.at(-1)?.[0]?.value
    if (changedValue) {
      wrapper.setProps({ field: gResultField({ value: changedValue }) })
    }
  }

  return {
    when: {
      changeValue,
    },
    then: {
      getFormattedValue,
      getUnformattedValue,
    },
  }
}
//#endregion SETUP FACTORY

//#region TESTS
// TODO: BRING THIS BACK ONCE NEW MONEY INPUT COMPONENT IS MADE. DO NOT DELETE!!!
describe.skip('numbers formatted correctly for each region', async () => {
  it.each(getRegions())(
    'for % formats rawNumber and interactiveValue',
    async (region) => {
      selectRegion(region as Region)
      const { then } = given({ field: gResultField({ value: 1200.32 }) })

      const { decimal, thousands } = useNumbro().languageData().delimiters
      await nextTick()
      expect(`${region}: ${then.getFormattedValue()}`).toBe(
        `${region}: 1${thousands}200${decimal}32`
      )
      expect(`${region}: ${then.getUnformattedValue()}`).toBe(
        `${region}: 1200${decimal}32`
      )
    }
  )
})
// TODO: BRING THIS BACK ONCE NEW MONEY INPUT COMPONENT IS MADE. DO NOT DELETE!!!
describe.skip('thousands formatted correctly for each region when inputting values', async () => {
  it.each(getRegions())(
    'returns correctly formatted number for %',
    async (region) => {
      selectRegion(region as Region)
      const { when, then } = given({ field: gResultField({ value: 12200.45 }) })
      const { decimal, thousands } = useNumbro().languageData().delimiters
      expect(`${region}: ${then.getFormattedValue()}`).toBe(
        `${region}: 12${thousands}200${decimal}45`
      )
      await when.changeValue(`1112${decimal}2223`)

      expect(`${region}: ${then.getUnformattedValue()}`).toBe(
        `${region}: 1112${decimal}2223`
      )
      expect(`${region}: ${then.getFormattedValue()}`).toBe(
        `${region}: 1${thousands}112${decimal}22`
      )
    }
  )
})
// TODO: BRING THIS BACK ONCE NEW MONEY INPUT COMPONENT IS MADE. DO NOT DELETE!!!
describe.skip('accepts paste of correctly formatted number for regions', async () => {
  it.each(getRegions())(
    'pasted number format matches format for region %',
    async (region) => {
      selectRegion(region as Region)
      const { when, then } = await given({
        field: gResultField({ value: 120 }),
      })

      const { decimal, thousands } = useNumbro().languageData().delimiters
      await when.changeValue(`678${thousands}123${thousands}234${decimal}56432`)
      expect(`${region}: ${then.getUnformattedValue()}`).toBe(
        `${region}: 678123234${decimal}56432`
      )
      expect(`${region}: ${then.getFormattedValue()}`).toBe(
        `${region}: 678${thousands}123${thousands}234${decimal}56`
      )
    }
  )
})
// TODO: BRING THIS BACK ONCE NEW MONEY INPUT COMPONENT IS MADE. DO NOT DELETE!!!
describe.skip('does not accept paste of incorrectly formatted numbers for regions', async () => {
  it.each(getRegions())(
    'pasted number format does not match format for region %',
    async (region) => {
      selectRegion(region as Region)
      const { when, then } = await given({
        field: gResultField({ value: 120 }),
      })

      const { decimal } = useNumbro().languageData().delimiters
      await when.changeValue(`678-123-234.56432`)
      expect(`${region}: ${then.getUnformattedValue()}`).toBe(`${region}: 120`)
      expect(`${region}: ${then.getFormattedValue()}`).toBe(
        `${region}: 120${decimal}00`
      )
    }
  )
})
//#endregion TESTS
