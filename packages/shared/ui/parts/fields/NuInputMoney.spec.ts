import type { FieldUnit } from '@shared/field-types'
import type * as formatNumber from '@shared/format/formatNumber'
import { gResultField } from '@tset/shared-utils/tests/generators/resultField'
import { useTestBaseSetupComponentFunctions } from '@tset/shared-utils/tests/setupFactory/setupFactory.utils'
import { mount } from '@vue/test-utils'
import { computed, nextTick } from 'vue'
import NuFieldSystemValue from './NuFieldSystemValue.vue'
import NuInputMoney from './NuInputMoney.vue'
import TsetInputNumeric from './TsetInputNumeric/TsetInputNumeric.vue'
vi.mock('@tset/shared-utils/formatters/formatField', () => ({
  $formatField: (v: string) => `${v}`,
}))
vi.mock(
  '@tset/shared-utils/formatters/formatNumber',
  async (importOriginal) => {
    const actual = await importOriginal<typeof formatNumber>()
    return {
      ...actual,
      $formatNumber: (v: string) => `${v}`,
      $formatValueToBig: (v: string) => `${v}`,
    }
  }
)

//#region MOCKS

const defaultField = gResultField({
  type: 'Money',
  value: 2222,
})

const differentCurrencyOptions: SetupOptions = {
  currency: 'EUR',
  calculationBaseCurrency: 'CAD',
  objectBaseCurrency: 'CHF',
  exchangeRates: { EUR: 1, CAD: 2, CHF: 3 },
}
//#endregion MOCKS

//#region SETUP FACTORY
type SetupOptions = {
  // Props
  field?: ResultField
  unit?: Nullable<FieldUnit>
  disabled?: boolean
  isManuallyOverridden?: boolean
  parentEntity?: Nullable<ManufacturingDTO>
  showCurrencyIcon?: boolean
  shouldValueBeFormatted?: boolean
  rowHovered?: boolean
  editableTableCell?: boolean
  tsetTableEditor?: boolean
  tsetTableEditorLast?: boolean
  disableSubValue?: boolean
  currency?: Currency
  // Injections
  exchangeRates?: Partial<Record<Currency, number>>
  calculationBaseCurrency?: Currency
  context?: ApplicationContext
  objectBaseCurrency?: Currency
}

const setup = ({
  field = defaultField,
  unit = null,
  disabled = false,
  isManuallyOverridden = false,
  parentEntity = null,
  showCurrencyIcon = true,
  shouldValueBeFormatted = true,
  rowHovered = false,
  editableTableCell = false,
  tsetTableEditor = false,
  tsetTableEditorLast = false,
  disableSubValue = false,
  currency = 'EUR',
  exchangeRates = { EUR: 1 },
  calculationBaseCurrency = 'EUR',
  context = 'calculation',
  objectBaseCurrency = 'EUR',
}: SetupOptions = {}) => {
  const mountOptions = {
    props: {
      field,
      unit,
      disabled,
      isManuallyOverridden,
      parentEntity,
      showCurrencyIcon,
      shouldValueBeFormatted,
      rowHovered,
      editableTableCell,
      tsetTableEditor,
      tsetTableEditorLast,
      disableSubValue,
      currency,
    },
    global: {
      stubs: ['IconWarningCurrency'],
      provide: {
        originalUnit: {},
        exchangeRates,
        calculationBaseCurrency: computed(() => calculationBaseCurrency),
        context: computed(() => context),
        objectBaseCurrency: computed(() => objectBaseCurrency),
      },
    },
  }

  const wrapper = mount(NuInputMoney, mountOptions)

  const getCurrentField = () => field
  const {
    getEmittedEvent: getNuInputMoneyEmittedEvent,
    triggerEvent: triggerNuInputMoneyEvent,
    getProp: getNuInputMoneyProp,
    itExists: getNuInputMoneyExists,
  } = useTestBaseSetupComponentFunctions(wrapper)

  const isIconWarningCurrencyExists = () =>
    wrapper.findByDataTest('different-currency-icon').exists()

  const getNuFieldSystemValue = () => wrapper.findComponent(NuFieldSystemValue)
  const {
    getProp: getNuFieldSystemValueProp,
    itExists: getNuFieldSystemValueExists,
    isItVisible: getNuFieldSystemValueIsVisible,
  } = useTestBaseSetupComponentFunctions(getNuFieldSystemValue())
  const getNuFieldSystemValueWrapper = () =>
    wrapper.findByDataTest('nu-input-money-nu-field-system-value-wrapper')

  const getTsetInputNumeric = () => wrapper.findComponent(TsetInputNumeric)
  const {
    getEmittedEvent: getTsetInputEmittedEvent,
    triggerEvent: triggerTsetInputEvent,
    getProp: getTsetInputProp,
    itExists: getTsetInputExists,
  } = useTestBaseSetupComponentFunctions(getTsetInputNumeric())
  const focusIn = async () => {
    await getTsetInputNumeric()
      .findByDataTest('input-element')
      .trigger('focusin')
  }
  const inputFromKeyboard = async (value: string) => {
    await getTsetInputNumeric().findByDataTest('input-element').setValue(value)
  }
  const focusOut = async () => {
    await getTsetInputNumeric().findByDataTest('input-element').trigger('blur')
    await nextTick()
  }
  const getFormattedValueTsetInput = () => getTsetInputProp('formattedValue')
  const getUnformattedValueTsetInput = () =>
    getTsetInputProp('unformattedValue')
  const isTsetInputSubValueExists = () =>
    wrapper.findByDataTest('tset-input-sub-value').exists()

  return {
    wrapper,
    getCurrentField,
    getNuInputMoneyEmittedEvent,
    triggerNuInputMoneyEvent,
    getNuInputMoneyProp,
    getNuInputMoneyExists,
    getNuFieldSystemValueProp,
    getNuFieldSystemValueExists,
    isIconWarningCurrencyExists,
    triggerTsetInputEvent,
    getTsetInputProp,
    getTsetInputExists,
    focusIn,
    inputFromKeyboard,
    focusOut,
    getFormattedValueTsetInput,
    getUnformattedValueTsetInput,
    getTsetInputEmittedEvent,
    getNuFieldSystemValueWrapper,
    getNuFieldSystemValueIsVisible,
    isTsetInputSubValueExists,
  }
}
//#endregion SETUP FACTORY

//#region TESTS
describe('NuInputMoney tests', () => {
  describe('When rendered', () => {
    it('- shows TsetInput', () => {
      const { getTsetInputExists } = setup()
      expect(getTsetInputExists()).toBe(true)
    })
  })

  describe('Currencies', () => {
    describe('When the field has the same currency as the calculation', () => {
      it('- subValue should be hidden', () => {
        const { isTsetInputSubValueExists } = setup()
        expect(isTsetInputSubValueExists()).toBeFalsy()
      })

      it('- hide different currency icon', () => {
        const { isIconWarningCurrencyExists } = setup()
        expect(isIconWarningCurrencyExists()).toBeFalsy()
      })
    })

    describe('When it has different currency as the calculation', () => {
      it('- show different currency icon', () => {
        const { isIconWarningCurrencyExists } = setup(differentCurrencyOptions)
        expect(isIconWarningCurrencyExists()).toBeTruthy()
      })

      it('- shows base currency and subvalue', async () => {
        const { getTsetInputProp, focusIn, inputFromKeyboard } = setup(
          differentCurrencyOptions
        )
        await focusIn()
        await inputFromKeyboard('220')
        // setTimeout cuz TsetInput debounces input emit
        await new Promise((resolve) => setTimeout(resolve, 301))
        expect(getTsetInputProp('subValue')).toBe('660.00 CHF')
      })
    })
  })

  describe('When value has been manually updated', () => {
    it('- show system value', async () => {
      const { focusIn, getNuFieldSystemValueExists } = setup({
        isManuallyOverridden: true,
      })

      await focusIn()
      expect(getNuFieldSystemValueExists()).toBeTruthy()
    })

    describe('when user clicked on system value', () => {
      const systemValueOptions: SetupOptions = {
        field: gResultField({
          type: 'Money',
          value: 220,
          systemValue: 22,
        }),
        isManuallyOverridden: true,
      }
      const validateNuFieldSystemValueTriggersEmits = async (event: string) => {
        const {
          focusIn,
          getNuFieldSystemValueWrapper,
          getNuInputMoneyEmittedEvent,
        } = setup(systemValueOptions)

        await focusIn()
        await getNuFieldSystemValueWrapper().trigger('mousedown')
        expect(getNuInputMoneyEmittedEvent(event)?.length).toBe(1)
      }

      it('- emits @restore', async () => {
        await validateNuFieldSystemValueTriggersEmits('restore')
      })

      it('- emits @focusout', async () => {
        await validateNuFieldSystemValueTriggersEmits('focusout')
      })

      it('- hide system value', async () => {
        const {
          focusIn,
          getNuFieldSystemValueWrapper,
          getNuFieldSystemValueIsVisible,
        } = setup(systemValueOptions)
        await focusIn()
        expect(getNuFieldSystemValueIsVisible()).toBeTruthy()
        await getNuFieldSystemValueWrapper().trigger('mousedown')
        expect(getNuFieldSystemValueIsVisible()).toBeFalsy()
      })
    })

    describe('When it has different currency as the calculation', () => {
      it('- passes "differentCurrency" prop as true', async () => {
        const { getNuFieldSystemValueProp, focusIn } = setup({
          ...differentCurrencyOptions,
          isManuallyOverridden: true,
        })
        await focusIn()
        expect(getNuFieldSystemValueProp('differentCurrency')).toBeTruthy()
      })
    })
  })

  describe('When the input is blurred', () => {
    describe('if input value was changed', () => {
      describe('by default', () => {
        it('- emits "change" event with the inputed value as the field value', async () => {
          const value = '2234'
          const {
            focusIn,
            inputFromKeyboard,
            focusOut,
            getNuInputMoneyEmittedEvent,
            getCurrentField,
          } = setup()
          await focusIn()
          await inputFromKeyboard(value)
          await focusOut()
          expect(getNuInputMoneyEmittedEvent('change')![0][0]).toMatchObject({
            ...getCurrentField(),
            value: Number(value),
            source: 'I',
          })
        })
      })

      describe('When empty values are allowed', () => {
        it('- does not emits @change with null payload', async () => {
          const {
            focusIn,
            inputFromKeyboard,
            focusOut,
            getNuInputMoneyEmittedEvent,
          } = setup({
            field: gResultField({
              type: 'Money',
              value: 22,
              metaInfo: { optional: true },
            }),
          })

          await focusIn()
          await inputFromKeyboard('')
          await focusOut()
          expect(getNuInputMoneyEmittedEvent('change')).toBeUndefined()
        })
      })

      describe('When field has a min value', () => {
        describe('When new value entered is bigger than the min', () => {
          it('emits @change with the new value', async () => {
            await testFieldWithMinMaxValues('50', 'value', 20)
          })
        })

        describe('When new value entered is lower than the min', () => {
          it('emits @change with the min value', async () => {
            await testFieldWithMinMaxValues('30', 'min', 50)
          })
        })
      })

      describe('When field has a max value', () => {
        describe('When new value entered is bigger than the max', () => {
          it('emits @change with the max value', async () => {
            await testFieldWithMinMaxValues('50', 'max', undefined, 20)
          })
        })

        describe('When new value entered is lower than the max', () => {
          it('emits @change with the new value', async () => {
            await testFieldWithMinMaxValues('30', 'value', undefined, 50)
          })
        })
      })
    })

    it('- emits "focusout" event at the end', async () => {
      const value = '2234'
      const {
        focusIn,
        inputFromKeyboard,
        focusOut,
        getNuInputMoneyEmittedEvent,
      } = setup()
      await focusIn()
      await inputFromKeyboard(value)
      await focusOut()
      expect(getNuInputMoneyEmittedEvent('focusout')?.length).toBe(2)
    })
  })
  //#endregion TESTS
})

//#region TEST HELPERS

const testFieldWithMinMaxValues = async (
  newValue: string,
  expected: 'min' | 'max' | 'value',
  min?: number,
  max?: number
) => {
  const {
    focusIn,
    inputFromKeyboard,
    focusOut,
    getNuInputMoneyEmittedEvent,
    getCurrentField,
  } = setup({
    field: gResultField({
      type: 'Money',
      value: 22,
      metaInfo: { min, max },
    }),
  })

  await focusIn()
  await inputFromKeyboard(newValue)
  await focusOut()
  expect(getNuInputMoneyEmittedEvent('change')![0][0]).toMatchObject({
    ...getCurrentField(),
    value:
      expected === 'min' ? min : expected === 'max' ? max : Number(newValue),
    source: 'I',
  })
}
//#endregion TEST HELPERS
