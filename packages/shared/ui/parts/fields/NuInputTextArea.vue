<template>
  <div :id="componentId" v-data-test class="relative h-full w-full">
    <TsetTextArea
      :model-value="term"
      :has-error="hasError"
      :rows="rows"
      :max-characters="maxCharacters"
      :placeholder="placeholder"
      :max-characters-label="$t('statics.exceedingMaxAllowedLengthOf')"
      :characters-left-label="$t('statics.charactersLeft')"
      @update:model-value="updateTerm"
      @esc="revertValue"
      @focusin="focusIn"
      @blur="updateValue"
    />
    <slot name="system-value" :focused="isFocused" :float="true">
      <div
        v-if="isManuallyOverridden"
        data-test="nu-field-system-value-wrapper"
        @mousedown="restoreValue"
      >
        <NuFieldSystemValue
          :wrapper-id="componentId"
          :field="field"
          :is-parent-focused="isFocused"
          :is-text-area="true"
        />
      </div>
    </slot>
  </div>
</template>

<script setup lang="ts">
import type { FieldType } from '@shared/field-types'
import { $nutTextField } from '@shared/translation/nuTranslation'
import { generateUuid } from '@tset/shared-utils/helpers/general'
import { computed, onMounted, ref, watch } from 'vue'
import NuFieldSystemValue from './NuFieldSystemValue.vue'
import TsetTextArea from '@tset/design/atoms/TsetTextArea/TsetTextArea.vue'

//#region PROPS
const props = withDefaults(
  defineProps<{
    field: ResultField<string>
    dataType?: Nullable<FieldType>
    isManuallyOverridden?: boolean
    hasError?: boolean
  }>(),
  { dataType: null, isManuallyOverridden: false }
)
//#endregion PROPS

//#region DATA
const isFocused = ref<boolean>(false)
const initValue = ref<string>('')
const term = ref<string>('')
const keyCode = ref<Nullable<KeyboardEvent['code']>>(null)
const componentId = generateUuid(props.field.name)
//#endregion

//#region EMITS
const emit = defineEmits<{
  (e: 'focusin'): void
  (e: 'focusout'): void
  (e: 'change', payload: ResultField<string>): void
  (e: 'restore'): void
}>()

function focusIn() {
  isFocused.value = true
  initValue.value = $nutTextField(props.field)
  emit('focusin')
}

function update(value: ResultField['value']) {
  emit('change', {
    ...props.field,
    value: String(value),
    unit: props.field.unit ?? props.dataType?.defaultUnit ?? null,
    source: 'I',
  })
}

function updateTerm(value: string) {
  term.value = value
}

//#endregion

//#region LIFECYCLE
onMounted(() => {
  initField()
})
//#endregion

//#region GETTERS/SETTERS
const placeholder = computed<string | undefined>(
  () => props.field.metaInfo?.placeHolder
)

const maxCharacters = computed<number>(
  () => props.field.metaInfo?.maxLength ?? 1800
)

const rows = computed<number>(() => props.field.metaInfo?.textArea?.rows ?? 6)
//#endregion

//#region WATCHERS
watch(
  () => props.field.value,
  () => {
    initField()
  }
)
//#endregion

//#region METHODS
function restoreValue() {
  // !Note restore is delayed here to allow the input blur to fire first
  setTimeout(() => {
    emit('restore')
  }, 0)
}

function initField() {
  if (!isFocused.value) {
    initValue.value = props.field.value ?? ''
    term.value = $nutTextField(props.field)
  }
}

function revertValue(e: Nullable<KeyboardEvent> = null) {
  keyCode.value = e?.code ?? null
  term.value = initValue.value
  ;(e?.target as HTMLElement).blur()
}

function updateValue() {
  isFocused.value = false
  emit('focusout')
  if (keyCode.value === 'Escape') {
    keyCode.value = null
    return
  }
  if (props.field.metaInfo?.mandatory && term.value === '') {
    revertValue(null)
    return
  }
  if (term.value !== props.field.value) {
    update(term.value)
  }
}
//#endregion
</script>

<script lang="ts">
export default {
  name: 'NuInputTextArea',
  components: {
    NuFieldSystemValue,
  },
}
</script>
