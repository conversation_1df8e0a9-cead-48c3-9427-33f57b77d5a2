import NuInputTextArea from '@tset/shared-ui/parts/fields/NuInputTextArea.vue'
import { gResultField } from '@tset/shared-utils/tests/generators/resultField'
import { mount } from '@vue/test-utils'
import { nextTick } from 'vue'

//#region MOCKS
const initialField = gResultField<string>({
  name: 'inputTextArea',
  value: 'Input TextArea value',
})
//#endregion MOCKS

//#region SETUP FACTORY
const setup = ({
  field = initialField,
  isManuallyOverridden = false,
}: {
  field?: ResultField<string>
  isManuallyOverridden?: boolean
  hasGlobalUnit?: boolean
} = {}) => {
  const mountOptions = {
    props: {
      field,
      isManuallyOverridden,
    },
  }
  const wrapper = mount(NuInputTextArea, mountOptions)

  const getTextarea = () =>
    wrapper.find<HTMLTextAreaElement>('[data-test="text-area-element"]')
  const getTextareaText = () => getTextarea().element.value
  const setTextareaText = (value: string) => getTextarea().setValue(value)
  const getEmittedEvent = (emittedEvent: string) =>
    wrapper.emitted(emittedEvent)
  const getCurrentField = () => field
  const getCurrentFieldValue = () => field.value
  const triggerEvent = (event: string, payload?: any) =>
    getTextarea().trigger(event, payload)
  const focusTextarea = () => triggerEvent('focusin')
  const unFocusTextarea = () => triggerEvent('blur')
  const pressEscOnTextArea = () => triggerEvent('keyup.esc')
  const pressCtrlEnterOnTextArea = () => triggerEvent('keyup.ctrl.enter')
  const getTextareaAttributes = (att: string) =>
    getTextarea().element.getAttribute(att)
  const getTextareaPlaceholder = () => getTextareaAttributes('placeholder')
  const getTextareaMaxlength = () => getTextareaAttributes('maxlength')
  const getCharactersLeftElement = () =>
    wrapper.find('[data-test="textarea-characters-left"]')
  const getCharactersLeftText = () => getCharactersLeftElement().text()

  return {
    focusTextarea,
    getCharactersLeftElement,
    getCharactersLeftText,
    getCurrentField,
    getCurrentFieldValue,
    getEmittedEvent,
    getTextarea,
    getTextareaText,
    pressCtrlEnterOnTextArea,
    pressEscOnTextArea,
    setTextareaText,
    triggerEvent,
    unFocusTextarea,
    getTextareaAttributes,
    getTextareaPlaceholder,
    getTextareaMaxlength,
  }
}
//#endregion SETUP FACTORY

//#region TESTS
describe('NuInputTextArea Component', () => {
  describe('when user clicks on the input (focused state)', () => {
    it('- emits "focusin"', async () => {
      const { getEmittedEvent, focusTextarea } = setup()
      await focusTextarea()
      expect(getEmittedEvent('focusin')?.length).toBe(1)
    })
  })

  describe('when user clicks outside the input (unfocused state)', () => {
    it('- emits "focusin"', async () => {
      const { getEmittedEvent, unFocusTextarea } = setup()
      await unFocusTextarea()
      expect(getEmittedEvent('focusout')?.length).toBe(1)
    })
  })

  describe('when user inputs a new value', () => {
    const newTextareaValue = 'New Text Value'
    it('- updates textarea value', async () => {
      const { getTextareaText, setTextareaText, getCurrentFieldValue } = setup()
      // Waits until the original field is procesed and rendered
      await nextTick()
      expect(getTextareaText()).toBe(getCurrentFieldValue())
      await setTextareaText(newTextareaValue)
      expect(getTextareaText()).toBe(newTextareaValue)
    })

    it('- emits "change"', async () => {
      const {
        getCurrentField,
        setTextareaText,
        unFocusTextarea,
        getEmittedEvent,
      } = setup()
      await setTextareaText(newTextareaValue)
      await unFocusTextarea()
      expect(getEmittedEvent('change')?.length).toBe(1)
      expect(getEmittedEvent('change')![0][0]).toMatchObject({
        ...getCurrentField,
        value: newTextareaValue,
      })
    })
  })

  describe('when user presses special key', () => {
    const newTextareaValue = 'New value'
    it('- when "esc" is pressed, should revert to previous value', async () => {
      const {
        getTextareaText,
        setTextareaText,
        getCurrentFieldValue,
        pressEscOnTextArea,
      } = setup()
      await setTextareaText(newTextareaValue)
      expect(getTextareaText()).not.toBe(getCurrentFieldValue())
      expect(getTextareaText()).toBe(newTextareaValue)
      await pressEscOnTextArea()
      expect(getTextareaText()).toBe(getCurrentFieldValue())
    })
  })

  describe('when component has custom metainfo properties', () => {
    it('- metainfo.placeHolder', () => {
      const placeholder = 'Placeholder value TextArea'
      const { getTextareaPlaceholder } = setup({
        field: gResultField({ metaInfo: { placeHolder: placeholder } }),
      })
      expect(getTextareaPlaceholder()).toBe(placeholder)
    })

    it('- metainfo.maxLength', () => {
      const maxLength = 40
      const { getTextareaMaxlength } = setup({
        field: gResultField({ metaInfo: { maxLength } }),
      })
      expect(getTextareaMaxlength()).toBe(String(maxLength))
    })
  })

  describe('when a user starts writing', () => {
    it('- has "X characters left" displayed', () => {
      const { getCharactersLeftElement } = setup()
      expect(getCharactersLeftElement().exists()).toBe(true)
    })

    it('- has a default max length of 1800 characters', () => {
      const { getTextareaMaxlength } = setup()
      expect(getTextareaMaxlength()).toBe('1800')
    })

    it('- displays correctly the number of characters left', async () => {
      const maxLength = 40

      const { getCharactersLeftText, setTextareaText } = setup({
        field: gResultField({ metaInfo: { maxLength } }),
      })
      const textContent = 'a'.repeat(25)
      await setTextareaText(textContent)
      expect(getCharactersLeftText()).toContain(String(40 - textContent.length))
    })
  })
})
//#endregion TESTS
