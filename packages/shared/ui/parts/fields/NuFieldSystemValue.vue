<template>
  <div
    v-show="isSystemValueShown && formattedValue"
    ref="systemValue"
    class="nu-system-value"
    :class="{
      'tset-table-editor-last': tsetTableEditorLast,
    }"
    data-test="system-value-field"
  >
    <div
      class="inline-wrapper flex w-full flex-row items-center justify-between gap-x-8 gap-y-2"
      data-test="restore-from-sys"
    >
      <div class="flex flex-col">
        <span
          class="pt-1 text-black-default"
          :class="{ 'text-area-truncate': isTextArea }"
          data-test="formatted-value-text"
        >
          {{ formattedValue }}
        </span>

        <div
          v-if="differentCurrency && baseSystemValue"
          class="text-small-light text-black-default"
        >
          <span class="pr-4">{{ baseSystemValue }}</span>
          <span>{{ baseSystemCurrency }}</span>
        </div>
      </div>
      <span class="fake-tset-badge pt-1">{{ displaySystemValue }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { convert, type FieldUnit } from '@shared/field-types'
import { $formatField } from '@shared/format/formatField'
import { $formatNumber } from '@shared/format/formatNumber'
import { $nutUnit, $t } from '@shared/translation/nuTranslation'
import { capitalized } from '@tset/design/tokens/Typography/capitalized'
import { handleFloatUiOpen } from '@tset/design/utils/designHelpers'
import { useDisplayCurrency } from '@tset/shared-utils/api/useDisplayCurrency'
import { getValue } from '@tset/shared-utils/helpers/getValue'
import type { Ref } from 'vue'
import { computed, inject, ref, watch } from 'vue'
//#region PROPS
const props = withDefaults(
  defineProps<{
    field: ResultField
    isParentFocused: boolean
    wrapperId: string
    unit?: Nullable<FieldUnit>
    objectBaseCurrency?: Nullable<Currency>
    differentCurrency?: boolean
    baseSystemCurrency?: Nullable<string>
    isTextArea?: boolean
    tsetTableEditorLast?: boolean
  }>(),
  {
    unit: null,
    objectBaseCurrency: null,
    differentCurrency: false,
    baseSystemCurrency: null,
    isTextArea: false,
    tsetTableEditorLast: false,
  }
)
//#endregion PROPS

//#region DATA
const systemValue = ref<HTMLDivElement>()
const formattedValue = ref<ResultField['value']>('')
const cleanup = ref<Nullable<() => void>>(null)
const { displayCurrency } = useDisplayCurrency()
//#endregion DATA

//#region INJECT
const originalUnit: Ref<Nullable<string>> = ref(
  inject<Nullable<string>>('originalUnit', null)
)
//#endregion INJECT

//#region DISPLAY
const isSystemValueShown = computed(() => {
  return props.field?.systemValue != null && props.isParentFocused
})

const displaySystemValue = computed(() => capitalized($t('statics.fromSystem')))

const baseSystemValue = computed((): Nullable<number | string> => {
  return props.field.systemValueCurrencyInfo && props.objectBaseCurrency
    ? $formatNumber(
        props.field.systemValueCurrencyInfo[props.objectBaseCurrency] ?? 0
      )
    : null
})
//#endregion DISPLAY

//#region WATCHERS
watch(
  () => props.isParentFocused,
  () => props.isParentFocused && setValue()
)
//#endregion WATCHERS

//#region VALUE
async function setValue(): Promise<void> {
  if (props.field.systemValue == null) {
    return
  }
  if (props.field.type === 'Rate' && props.field.systemValue) {
    formattedValue.value = Number(props.field.systemValue) * 100
  } else if (props.field.systemValueCurrencyInfo && displayCurrency.value) {
    // Only for NuInputMoney -> get the formatted value with displayCurrency and field.systemValueCurrencyInfo
    formattedValue.value = `${$formatNumber(
      props.field.systemValueCurrencyInfo[displayCurrency.value] ?? 0
    )} ${displayCurrency.value}`
  } else if (!props.unit || !props.field.unit) {
    formattedValue.value = $formatField(
      {
        ...props.field,
        value: props.field.systemValue ?? null,
      },
      { formatSystemValue: true }
    )
  } else {
    const e = convert({
      type: props.field.type,
      from: originalUnit.value ?? props.field.unit,
      to: props.unit.unit,
      value: props.field.systemValue as number,
    })

    const convertedField: ResultField = {
      ...props.field,
      // Note: we don't need the defaultUnit and have to use the unit from props
      metaInfo: { ...props.field.metaInfo, defaultUnit: undefined },
      value: e,
      unit: props.unit?.unit,
      valueInDefaultUnit: e,
    }
    formattedValue.value = `${$formatNumber(
      getValue(convertedField, displayCurrency.value)
    )} ${$nutUnit(convertedField)}`
  }
  cleanup.value?.()
  cleanup.value = handleFloatUiOpen(props.wrapperId, systemValue.value) || null
}
</script>
<script lang="ts">
export default {
  name: 'NuFieldSystemValue',
}
</script>

<style scoped lang="postcss">
.tset-table-editor-last.nu-system-value {
  @apply left-auto right-0;
}
.nu-system-value {
  @apply fixed box-border h-32
    w-auto
    min-w-max
    whitespace-nowrap
    rounded-lg bg-white-default shadow-systemValue;

  z-index: 9998; /* 8 - to prevent TsetDatePicker covering */

  & .inline-wrapper {
    @apply text-caption-light flex
      min-h-30 w-full
      cursor-pointer flex-row
      items-center justify-between
      rounded-lg bg-white
      px-8
      py-8 hover:bg-primary-veryLight;
  }
}

.fake-tset-badge {
  @apply text-small-light
    flex
    h-16
    items-center
    justify-center rounded-lg
    border
    border-gray-light bg-gray-lightest
    px-6 text-gray-dark;
}

.text-area-truncate {
  overflow: hidden;
  max-width: 322px;
  text-overflow: ellipsis;
  white-space: nowrap;
}
</style>
