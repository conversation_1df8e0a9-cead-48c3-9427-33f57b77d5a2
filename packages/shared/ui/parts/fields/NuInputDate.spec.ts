import TsetDatepicker from '@tset/design/organisms/TsetDatepicker/TsetDatepicker.vue'
import NuInputDate from '@tset/shared-ui/parts/fields/NuInputDate.vue'
import { gResultField } from '@tset/shared-utils/tests/generators/resultField'
import { mount } from '@vue/test-utils'
import { nextTick } from 'vue'

const setup = ({ props }: { props: { field: ResultField<string | null> } }) => {
  const wrapper = mount(NuInputDate, {
    props,
    attachTo: document.body,
    global: {
      stubs: {
        TsetDatepicker: true,
      },
    },
  })

  const getStubbedDatepicker = () => wrapper.findComponent(TsetDatepicker)

  return {
    wrapper,
    getStubbedDatepicker,
  }
}

describe('NuInputDate', () => {
  it('passes the field value to the TsetDatepicker component', () => {
    const value = '2000-02-01'
    const { getStubbedDatepicker } = setup({
      props: {
        field: gResultField({ value }),
      },
    })

    expect(getStubbedDatepicker().props().value).toEqual(value)
  })

  it('passes the updated value to the TsetDatepicker if the value changed', async () => {
    const value = '2000-02-01'
    const { getStubbedDatepicker } = setup({
      props: {
        field: gResultField({ value }),
      },
    })
    const stubbedDatepicker = getStubbedDatepicker()

    expect(stubbedDatepicker.props().value).toEqual(value)

    const newDate = '2024-03-23'
    stubbedDatepicker.vm.$emit('input', newDate)
    await nextTick()

    expect(stubbedDatepicker.props().value).toEqual(newDate)
  })

  it('reverts the value to the initial value when the escape key is pressed', async () => {
    const value = '2000-02-01'
    const { getStubbedDatepicker } = setup({
      props: {
        field: gResultField({ value }),
      },
    })
    const stubbedDatepicker = getStubbedDatepicker()

    expect(stubbedDatepicker.props().value).toEqual(value)

    const newDate = '2024-03-23'
    stubbedDatepicker.vm.$emit('input', newDate)
    await nextTick()

    expect(stubbedDatepicker.props().value).toEqual(newDate)

    await stubbedDatepicker.trigger('esc')

    expect(stubbedDatepicker.props().value).toEqual(value)
  })

  it('emits the current value on blur', async () => {
    const { wrapper, getStubbedDatepicker } = setup({
      props: {
        field: gResultField({ value: '2000-02-01' }),
      },
    })
    const stubbedDatepicker = getStubbedDatepicker()

    const newDate = '2024-03-23'
    stubbedDatepicker.vm.$emit('input', newDate)
    await nextTick()
    await stubbedDatepicker.trigger('blur')

    expect(wrapper.emitted<ResultField[]>().change.at(0)?.at(0)?.value).toEqual(
      newDate
    )
  })
})
