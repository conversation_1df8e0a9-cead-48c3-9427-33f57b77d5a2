<template>
  <FieldValidator :field="field">
    <template
      #default="{ hasError, fieldError, onBlur, onChange, inFormContext }"
    >
      <TsetFieldLayout
        :label="fieldLabel(field)"
        :data-test="`field-${field && field.name}-${
          parentEntity && parentEntity.type
        }`"
        :editable="isFieldEditable"
        :hide-label="field.metaInfo?.hideLabel"
        :type="field.metaInfo?.fieldLayoutType"
        v-bind="$attrs"
      >
        <template #label>
          {{ fieldLabel(field)
          }}<span
            v-if="inFormContext && isFieldOptional && isFieldEditable"
            class="text-body-light ml-3 text-gray-dark"
          >
            {{ $t('validation.optional_label') }}
          </span>
        </template>
        <template v-if="inFormContext" #error>
          <FieldErrorArea :error="fieldError" />
        </template>
        <template v-if="hasIconBeforeLabel" #icon-before-label>
          <component :is="iconBeforeLabel" data-test="icon-before-label" />
        </template>
        <template #left-icon>
          <IconInputMissing
            v-if="errorText"
            v-tooltip="{ content: errorText, html: true }"
            data-test="icon-input-missing"
            tabindex="-1"
          />
          <IconUnlink
            v-else-if="isManuallyOverridden"
            data-test="icon-unlink"
          />
          <TsetSpinner
            v-else-if="isOptimistic"
            v-data-test:optimistic-spinner
            inverted
          />
        </template>
        <template #input>
          <div :id="field.name" class="text-body-light">
            <component
              :is="component"
              :with-system-value="withSystemValue"
              :field="mappedField"
              :unit="localUnit"
              :data-type="dataType"
              :is-manually-overridden="isManuallyOverridden || isOptimistic"
              :is-object="isObject"
              :multiple="multiple"
              :parent-entity="parentEntity"
              :currency="currency"
              :has-error="hasError || !!reportedError"
              :has-warning="hasWarning"
              @change="pipeArgs($event, update, onChange)"
              @restore="triggerEmit('restore')"
              @focusin="focusHandler(true)"
              @focusout="pipeArgs($event, onBlur, () => focusHandler(false))"
              @report-error="setError"
              @clear-error="clearError"
            >
              <template #unit>
                <NuSideUnits
                  v-if="hasUnitDropdown"
                  :key="fieldTrigger"
                  :unit="localUnit"
                  :field="field"
                  :currency="currency"
                  @change="changeLocalUnitManually"
                />
              </template>
              <template #system-value="{ focused, float, close }">
                <slot
                  name="system-value"
                  :focused="focused"
                  :float="float ?? true"
                  :close="close"
                />
              </template>
            </component>
          </div>
        </template>
        <template v-if="hasDetails" #details>
          <DetailsLabel
            v-data-test:details
            :severity="field.metaInfo?.hasMissingDetails ? 'warning' : 'info'"
            :label="detailsLabel"
            :is-editable="isFieldEditable"
            @click="onDetailsClicked"
          />
        </template>
        <template v-if="hasFieldInfo(field)" #info>
          <TsetFieldInfo :message="getFieldInfo(field)">
            <IconInfoDetails
              class="h-14 w-14 text-black-light hover:text-black-default"
            />
          </TsetFieldInfo>
        </template>
      </TsetFieldLayout>
    </template>
  </FieldValidator>
</template>

<script setup lang="ts" generic="T extends ResultFieldValueType">
import { isProjectManufacturingStepPage } from '@/router/router-tools'
import { getType, type FieldUnit } from '@shared/field-types'
import { FieldValidator } from '@shared/result-field-validation'
import { $nutInfo, $nutLabel, $t } from '@shared/translation/nuTranslation'
import DetailsLabel from '@tset/design/atoms/TsetFieldLayout/DetailsLabel.vue'
import FieldErrorArea from '@tset/design/atoms/TsetFieldLayout/FieldErrorArea.vue'
import TsetFieldLayout from '@tset/design/atoms/TsetFieldLayout/TsetFieldLayout.vue'
import TsetSpinner from '@tset/design/atoms/TsetSpinner/TsetSpinner.vue'
import NuSideUnits from '@tset/shared-ui/parts/fields/NuSideUnits.vue'
import TsetFieldInfo from '@tset/shared-ui/parts/fields/TsetFieldInfo/TsetFieldInfo.vue'
import { useDisplayCurrency } from '@tset/shared-utils/api/useDisplayCurrency'
import {
  fieldComponent,
  fieldHasDetails,
  fieldHasGlobalUnit,
  getRateFieldValue,
  isUnitUpdateRequired,
  mapFormattedField,
} from '@tset/shared-utils/helpers/field'
import { allFieldComponents } from '@tset/shared-utils/helpers/fieldComponents'
import { pipeArgs } from '@tset/shared-utils/helpers/general'
import {
  getField,
  getIconNameFromField,
} from '@tset/shared-utils/helpers/manufacturing'
import { isFieldManuallyOverridden } from '@tset/shared-utils/helpers/overriden'
import type { ComputedRef } from 'vue'
import { computed, inject, onMounted, ref, useAttrs, watch } from 'vue'
import { useRoute } from 'vue-router'
//#region CUSTOM TYPES
type TsetResultField = ResultField<T>
//#endregion CUSTOM TYPES

//#region COMPOSABLES
const { displayCurrency } = useDisplayCurrency()
const route = useRoute()
//#endregion COMPOSABLES
const $attrs = useAttrs()
//#region PROPS
const props = withDefaults(
  defineProps<{
    field: TsetResultField
    parentEntity?: Nullable<ManufacturingDTO>
    isObject?: boolean
    multiple?: Nullable<boolean | number>
    withSystemValue?: boolean
    disableLocalUnitPriority?: boolean
    fieldCurrency?: Currency
    errorText?: string
  }>(),
  {
    parentEntity: null,
    isObject: false,
    multiple: null,
    withSystemValue: true,
    disableLocalUnitPriority: false,
    errorText: '',
  }
)
//#endregion PROPS

//#region INJECT
const context: ComputedRef<ApplicationContext> =
  inject<ComputedRef<ApplicationContext>>('context')!
//#endregion INJECT

//#region LIFECYCLE
onMounted(() => {
  setLocalUnit()
})
//#endregion LIFECYCLE

//#region EMITS
const emit = defineEmits<{
  (e: 'restore', payload: TsetResultField): void
  (e: 'change', payload: TsetResultField): void
  (e: 'changed-local-unit', payload: Nullable<FieldUnit>): void
  (e: 'convert-units', payload: TsetResultField): void
  (e: 'focus', payload: boolean): void
  (e: 'open-details', payload: TsetResultField): void
}>()
//#endregion EMITS

const hasWarning = computed(() => !!props.errorText)
const isFieldOptional = computed(() => props.field.metaInfo?.optional)
const isFieldEditable = computed(() => component.value !== 'NuInputReadonly')
//#region UNITS & CURRENCY
const localUnit = ref<Nullable<FieldUnit>>(null)
// Manually set local unit should always take precedence
const localUnitBeenSetManually = ref<boolean>(false)
const currency = computed(() => {
  // TODO extract into helper
  if (context.value === 'masterdata') {
    return getField(props.parentEntity, 'masterdataBaseCurrency').value as
      | Currency
      | ''
  }

  return props.fieldCurrency ?? displayCurrency.value
})

const hasUnitDropdown = computed<boolean>(
  () =>
    (dataType.value && !!dataType.value.units) ||
    fieldHasGlobalUnit(props.field)
)

function updateUnit(unit: Nullable<FieldUnit>): void {
  localUnit.value = unit
  emit('changed-local-unit', unit)
}

/** (re)sets the localUnit to the proper value */
function setLocalUnit(): void {
  const fromDataType = dataType.value?.units?.find(
    (u) =>
      u.unit === (props.field.metaInfo?.defaultUnit?.unit ?? props.field.unit)
  )

  updateUnit(fromDataType ?? localUnit.value ?? null)
}

function changeLocalUnitManually(unit: Nullable<FieldUnit>): void {
  if (!props.disableLocalUnitPriority) {
    localUnitBeenSetManually.value = true
  }
  updateUnit(unit)
}
//#endregion UNITS & CURRENCY

//#region DETAILS POPUP/OBJECT VIEW
const fieldViewMode = inject<Nullable<'detail'>>('field-view-mode', null)
const hasDetails = computed(
  () => fieldViewMode !== 'detail' && fieldHasDetails(props.field)
)
const detailsLabel = computed(() =>
  $t(`fieldDetails.${props.field.metaInfo?.detailsLabel ?? 'details'}`)
)
function onDetailsClicked() {
  emit('open-details', props.field)
}
//#endregion DETAILS POPUP/OBJECT VIEW

//#region FOCUS INTERACTIONS
const focused = ref<boolean>(false)

function focusHandler(value: boolean) {
  focused.value = value
  emit('focus', focused.value)
}
//#endregion FOCUS INTERACTIONS

//#region FIELD RENDERING
// NOTE: moved to helper functions cuz of base table inline edit that reuses this piece of code
const component = computed<string>(() =>
  fieldComponent(props.field, dataType.value)
)

//TODO https://tsetplatform.atlassian.net/browse/COST-48938
const hasIconBeforeLabel = computed(
  () => props.field.type === 'EntityRef' && iconBeforeLabel
)
//TODO https://tsetplatform.atlassian.net/browse/COST-48938
const iconBeforeLabel = computed(() => getIconNameFromField(props.field))

/**
 * returns a :key property to re-render the component when the field has changed
 *
 * @returns {string} :key property to re-render the component when the field has changed
 */
const fieldTrigger = computed<string>(() => JSON.stringify(props.field))

function fieldLabel(field: TsetResultField): string {
  const label = $nutLabel(field)
  if (
    checkIsStep() &&
    (field.name === 'n/a' || field.name === 'templateName')
  ) {
    return $t('Configuration')
  }
  return label
}

function checkIsStep(): boolean {
  return isProjectManufacturingStepPage()
}

function hasFieldInfo(field: TsetResultField): boolean {
  return !!$nutInfo(field)
}

function getFieldInfo(field: TsetResultField): string {
  return $nutInfo(field) ?? ''
}
//#endregion FIELD RENDERING

//#region FIELD INTERACTIONS
const isOptimistic = computed(() => props.field.source === 'O')
const dataType = computed(() => getType(props.field.type))
// NOTE: moved to helper functions cuz of base table inline edit that reuses this piece of code
const isManuallyOverridden = computed<boolean>(() =>
  isFieldManuallyOverridden(props.field, context.value)
)
// NOTE: moved to helper functions cuz of base table inline edit that reuses this piece of code
const mappedField = computed(() => mapFormattedField(props.field, isRate.value))
const isRate = computed<boolean>(() => dataType.value?.type === 'Rate')

function update(field: TsetResultField): void {
  emit('convert-units', field)
  triggerEmit('change', field)
}

function triggerEmit(
  type: 'restore' | 'change',
  updateField: Nullable<TsetResultField> = null
): void {
  const field = updateField ?? props.field
  let unit = field.unit
  if (!unit) {
    unit = dataType.value ? dataType.value.defaultUnit : ''
  }

  const builtField: TsetResultField = {
    ...field,
    unit: unit,
    value: getRateFieldValue(isRate.value, field) as T, // we have the value in displayCurrency in the value
  }
  // Needed to it this way due to typing issues against the emit
  switch (type) {
    case 'change':
      emit('change', builtField)
      break
    case 'restore':
      emit('restore', builtField)
      break
  }
}
//#endregion FIELD INTERACTIONS

//#region ERROR HANDLING
const reportedError = ref<string | undefined>()
function setError(errorMessage: string) {
  reportedError.value = errorMessage
}
function clearError() {
  reportedError.value = undefined
}
//#endregion ERROR HANDLING

//#region WATCHERS
// TODO: Reactivity being triggered when both cases are undefined
/** watch `field.metaInfo.defaultUnit` because it might be used for the `localUnit`
 * Local Unit should take precedence over all cases
 */
watch(
  () => props.field.metaInfo?.defaultUnit?.unit,
  (newValue: string | undefined, oldValue: string | undefined) => {
    handleUnitWatchers(newValue, oldValue)
  },
  { deep: true }
)
// TODO: Reactivity being triggered when both cases are undefined
/** watch `field.unit` because it might be used for the `localUnit`
 * Local Unit should take precedence over all cases
 */
watch(
  () => props.field.unit,
  (
    newValue: Nullable<string> | undefined,
    oldValue: Nullable<string> | undefined
  ) => {
    handleUnitWatchers(newValue, oldValue)
  },
  { deep: true }
)

function handleUnitWatchers(
  newValue: Nullable<string> | undefined,
  oldValue: Nullable<string> | undefined
) {
  if (
    !isUnitUpdateRequired(
      newValue ?? null,
      oldValue ?? null,
      localUnitBeenSetManually.value,
      localUnit.value
    )
  ) {
    return
  }

  setLocalUnit()
}
//#endregion WATCHERS
</script>

<script lang="ts">
export default {
  name: 'NuField',
  components: { ...allFieldComponents() },
}
</script>
