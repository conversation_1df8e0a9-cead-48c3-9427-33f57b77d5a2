import { navigationStore } from '@/store'
import { manufacturingStore } from '@domain/calculation/manufacturing.store'
import type { FieldType, FieldUnit } from '@shared/field-types'
import { i18n } from '@shared/translation/nuTranslation'
import { PAGE } from '@tset/shared-model/navigation/navigation'
import TsetFieldInfo from '@tset/shared-ui/parts/fields/TsetFieldInfo/TsetFieldInfo.vue'
import { THIN_SPACE } from '@tset/shared-utils/helpers/THIN_SPACE'
import * as objectHelpers from '@tset/shared-utils/helpers/object'
import { gBomNodeEntity } from '@tset/shared-utils/tests/generators/bomNodeEntity'
import { gManuCurrentKpi } from '@tset/shared-utils/tests/generators/manuCurrentKpi'
import { gManufacturing } from '@tset/shared-utils/tests/generators/manufacturing'
import { gResultField } from '@tset/shared-utils/tests/generators/resultField'
import type { VueWrapper } from '@vue/test-utils'
import { mount } from '@vue/test-utils'
import { nextTick, ref } from 'vue'
import { createRouter, createWebHistory } from 'vue-router'
import NuField from './NuField.vue'

//#region SETUP DATA
const router = createRouter({
  history: createWebHistory(),
  routes: [
    {
      path: '/',
      name: PAGE.MANU_PART,
      component: {},
    },
  ],
})
router.currentRoute.value.path = '/project/'

// TESTTIP How to mock translations the right way! (note how we add them to the i18n plugin with setLocaleMessage)
const translations = {
  fields: {
    normalField: 'Normal Field',
    withObject: {
      label: 'Object label',
      info: 'Info label',
    },
    withObjectWithoutInfo: {
      label: 'Object label',
    },
  },
}
// NOTE: we set the messages on the i18n now because of the changes from COST-21999
i18n.setLocaleMessage('en', translations)
//#endregion SETUP DATA

//#region MOCKS

const baseField = gResultField({
  name: 'DefaultField',
  label: 'field label',
})

vi.mock('@tset/shared-utils/form/useField', () => ({
  useTsetField: vi.fn(() => ({
    error: '',
    handleChange: vi.fn(),
    handleBlur: vi.fn(),
    isInForm: false,
  })),
}))
//#endregion MOCKS

//#region TYPES
type SetupOptions = {
  field?: ResultField
  parentEntity?: ManufacturingDTO
  stubInputs?: boolean
  dataType?: FieldType
  focused?: boolean
  hasLocalUnitBeenSetManually?: boolean
  localUnit?: FieldUnit
  extraDataTypes?: FieldType[]
  isCalculationBroken?: boolean
  errorText?: string
}

type NuFieldTestComponentTypes =
  | 'NuClassSelector'
  | 'NuCurrencyPicker'
  | 'NuInputMoney'
  | 'NuInputNumber'
  | 'NuInputRadio'
  | 'NuInputReadonly'
  | 'NuInputSelect'
  | 'NuInputText'
  | 'NuSideUnits'
  | 'NuToggleField'
//#endregion TYPES

//#region SETUP FACTORY
const setup = ({
  field = baseField,
  parentEntity = gManufacturing(),
  focused = false,
  hasLocalUnitBeenSetManually = false,
  localUnit,
  isCalculationBroken = false,
  errorText = '',
}: SetupOptions = {}) => {
  manufacturingStore.setNodeInt(
    gBomNodeEntity({
      kpi: {
        co2PerPart: gManuCurrentKpi({ broken: isCalculationBroken }),
        costPerPart: gManuCurrentKpi({ broken: isCalculationBroken }),
      },
    })
  )

  const mountOptions = {
    props: {
      field,
      errorText,
      parentEntity,
    },
    data: () => ({
      focused: ref(focused),
      hasLocalUnitBeenSetManually: ref(hasLocalUnitBeenSetManually),
      localUnit: ref(localUnit),
    }),
    global: {
      plugins: [router],
      stubs: [
        'IconDetail',
        'IconDetails',
        'IconInfoDetails',
        'IconUnlink',
        'IconInputMissing',
        'IconWarningCurrency',
        'IconCheckDropdown',
      ],
      provide: {
        objectBaseCurrency: 'EUR',
        calculationBaseCurrency: 'EUR',
        context: 'calculation',
        exchangeRates: { EUR: 1 },
        originalUnit: 'EUR',
      },
    },
  }

  const wrapper = mount(NuField, mountOptions)

  //#region COMPONENT GETTERS
  const getComponent = (component: NuFieldTestComponentTypes) => {
    return wrapper.findComponent({ name: component })
  }
  //#endregion COMPONENT GETTERS

  //#region EXISTS VALIDATION
  const getComponentIsPresent = (component: NuFieldTestComponentTypes) =>
    getComponent(component).exists()
  //#endregion EXISTS VALIDATION

  //#region COMPONENTS PROPS
  const getComponentProps = (
    component: NuFieldTestComponentTypes,
    propName: string
  ) => getComponent(component).props(propName)
  //#endregion COMPONENTS PROPS

  //#region FIELD PARTS
  const isDetailsLabelVisible = () =>
    wrapper.findByDataTest('nu-field-details').exists()
  const isUnlinkIconPresent = () =>
    wrapper.find('[data-test="icon-unlink"]').exists()
  const isInputMissingIconPresent = () =>
    wrapper.find('[data-test="icon-input-missing"]').exists()
  const getInputMissingIconTooltip = () =>
    wrapper.find('[data-test="icon-input-missing"]').attributes('v-tooltip')
  const getMoneyUnit = () => wrapper.find('[data-test="money-unit"]')
  const isMoneyUnitPresent = () => getMoneyUnit().exists()
  const getMoneyUnitText = () => getMoneyUnit().text()
  const getTsetFieldInfo = () => wrapper.findComponent(TsetFieldInfo)
  const isTsetFieldInfoPresent = () => getTsetFieldInfo().exists()
  //#endregion FIELD PARTS

  //#region EVENTS
  const triggerEvent = (event: string, component: VueWrapper, content?: any) =>
    component.vm.$emit(event, content)
  const getEmittedList = () => wrapper.emitted()
  const getEmittedEvent = (event: string) => wrapper.emitted(event)

  const clickOnDetailsLabel = () => {
    wrapper.findByDataTest('nu-field-details').trigger('click')
  }
  //#endregion EVENTS

  //#region VM CHANGES
  const updateProps = async (newProps: any) => {
    await wrapper.setProps(newProps)
  }

  vi.spyOn(objectHelpers, 'getObject').mockReturnValue(
    parentEntity as ManufacturingDTO
  )

  const navigateToSpy = vi
    .spyOn(navigationStore, 'navigateTo')
    // @ts-expect-error type mismatch
    .mockImplementation(() => {})

  const getLastNavigationTarget = () =>
    navigateToSpy.mock.lastCall?.[0]?.targetPage
  //#endregion VM CHANGES

  return {
    getComponent,
    getComponentIsPresent,
    isDetailsLabelVisible,
    isUnlinkIconPresent,
    isInputMissingIconPresent,
    getInputMissingIconTooltip,
    triggerEvent,
    getEmittedList,
    getEmittedEvent,
    getComponentProps,
    updateProps,
    isMoneyUnitPresent,
    getMoneyUnitText,
    getTsetFieldInfo,
    isTsetFieldInfoPresent,
    clickOnDetailsLabel,
    getLastNavigationTarget,
  }
}
//#endregion SETUP FACTORY

//#region TESTS
describe('NuField Component', () => {
  describe('Details label', () => {
    function getFieldWithMetaInfo(metaInfo: FieldMetaInfo) {
      return gResultField({ metaInfo, label: 'some field' })
    }
    const VISIBLE = true
    const HIDDEN = false

    const fieldWithSystemParameter = getFieldWithMetaInfo({
      navigateToObject: 'SYSTEM_PARAMETER',
    })

    describe('by default', () => {
      it('- hide details', () => {
        const { isDetailsLabelVisible } = setup()
        expect(isDetailsLabelVisible()).toBe(false)
      })
    })

    describe('when `field.metaInfo.hasPopup` is `true`', () => {
      it('- show  details', () => {
        const { isDetailsLabelVisible } = setup({
          field: getFieldWithMetaInfo({ hasPopup: true }),
        })
        expect(isDetailsLabelVisible()).toBe(VISIBLE)
      })
    })

    describe('when navigation is enabled ', () => {
      describe('and `field.metaInfo`', () => {
        describe('.navigateToObject is missing', () => {
          it('- hide details', () => {
            const { isDetailsLabelVisible } = setup({
              field: getFieldWithMetaInfo({}),
            })
            expect(isDetailsLabelVisible()).toBe(HIDDEN)
          })
        })
        describe('.navigateToObject is "NONE"', () => {
          it('- hide details', () => {
            const { isDetailsLabelVisible } = setup({
              field: getFieldWithMetaInfo({ navigateToObject: 'NONE' }),
            })
            expect(isDetailsLabelVisible()).toBe(HIDDEN)
          })
        })
        describe('.navigateToObject is not "NONE"', () => {
          it('- show details', () => {
            const { isDetailsLabelVisible } = setup({
              field: getFieldWithMetaInfo({ navigateToObject: 'Calculation' }),
            })
            expect(isDetailsLabelVisible()).toBe(VISIBLE)
          })
        })
      })
    })

    describe('when user clicked on the details label', () => {
      it('- emits open details', async () => {
        const field = getFieldWithMetaInfo({
          navigateToObject: 'SYSTEM_PARAMETER',
        })
        const { clickOnDetailsLabel, getEmittedEvent } = setup({
          field,
        })
        await clickOnDetailsLabel()
        expect(getEmittedEvent('open-details')).toStrictEqual([[field]])
      })
    })
  })

  describe('Unlink icon', () => {
    describe('is not present', () => {
      it('- by default', () => {
        const { isUnlinkIconPresent } = setup()
        expect(isUnlinkIconPresent()).toBe(false)
      })

      it('- when Component is focused', () => {
        const { isUnlinkIconPresent } = setup({ focused: true })
        expect(isUnlinkIconPresent()).toBe(false)
      })

      it('- when field has system value but source is not "I"', () => {
        const { isUnlinkIconPresent } = setup({
          field: gResultField({
            label: 'label',
            systemValue: 4,
            source: 'C',
          }),
        })
        expect(isUnlinkIconPresent()).toBe(false)
      })

      it('- when field has system value, source is "I" but metaInfo has readOnly', () => {
        const { isUnlinkIconPresent } = setup({
          field: gResultField({
            label: 'label',
            systemValue: 4,
            source: 'I',
            metaInfo: { readOnly: true },
          }),
        })
        expect(isUnlinkIconPresent()).toBe(false)
      })
    })

    describe('- is present', () => {
      it('- when field has system value, source is "I" and metaInfo has not readOnly', () => {
        const { isUnlinkIconPresent } = setup({
          field: gResultField({
            label: 'label',
            systemValue: 4,
            source: 'I',
            metaInfo: { min: 4 },
          }),
        })
        expect(isUnlinkIconPresent()).toBe(true)
      })
    })
  })

  describe('"missing input" icon and tooltip', () => {
    describe('when no :errorText prop provided', () => {
      it('- hide "missing input" icon', () => {
        const { isInputMissingIconPresent } = setup()
        expect(isInputMissingIconPresent()).toBe(false)
      })
    })

    describe('when :errorText prop provided', () => {
      it('- show "missing input" icon', () => {
        const { isInputMissingIconPresent } = setup({
          errorText: 'missing input',
        })
        expect(isInputMissingIconPresent()).toBe(true)
      })

      describe('when user hovered on error icon', () => {
        it.todo('- show tooltip', () => {})
      })
    })
  })

  describe('Events', () => {
    const hasEventBeenEmitted = ({
      parentEvent = 'change',
      childEvent = 'change',
      component = 'NuInputText',
      emittedEventListLength: emittedEventLength = 1,
      emitValue,
      expected,
      field = gResultField(),
    }: {
      parentEvent?: string
      childEvent?: string
      emittedEventListLength?: number
      emitValue?: any
      expected?: any
      component?: NuFieldTestComponentTypes
      field?: ResultField
    } = {}) => {
      const { getComponent, getEmittedEvent } = setup({
        field,
      })

      // NOTE: Use of vm.$emit still in place due to the use of a "component" selector, so there is no reliable way of forcing an emission.
      getComponent(component).vm.$emit(childEvent, emitValue)

      expect(getEmittedEvent(parentEvent)!.length).toBe(emittedEventLength)
      expect(getEmittedEvent(parentEvent)![0][0]).toStrictEqual(expected)
    }

    describe('Basic Events', () => {
      it('- emits "convert-units" with the correct payload on child component triggering "change"', () => {
        const field = gResultField({
          name: 'event field name',
          value: 'event field value',
        })
        hasEventBeenEmitted({
          parentEvent: 'convert-units',
          childEvent: 'change',
          expected: field,
          emitValue: field,
        })
      })
      it('- emits "focus" with "true" on child component triggering "focusin"', () => {
        hasEventBeenEmitted({
          parentEvent: 'focus',
          childEvent: 'focusin',
          expected: true,
        })
      })
      it('- emits "focus" with "false on child component triggering "focusout"', () => {
        hasEventBeenEmitted({
          parentEvent: 'focus',
          childEvent: 'focusout',
          expected: false,
        })
      })
    })

    describe('Change events', () => {
      it('- emits "change" with the correct payload when child event payload field has a unit on child component "change', () => {
        const field = gResultField({ unit: 'expected unit' })
        hasEventBeenEmitted({
          emitValue: field,
          expected: { ...field, unit: field.unit },
        })
      })

      it('- emits "change" with the correct payload when child event payload field has no unit and there is no datatype on child component "change', () => {
        const field = gResultField()
        hasEventBeenEmitted({
          emitValue: field,
          expected: { ...field, unit: undefined },
        })
      })

      it('- emits "change" with the correct payload when child event payload field has no unit and there is a datatype with defaultUnit on child component "change"', () => {
        const field = gResultField({ type: 'Length', value: '123321' })
        hasEventBeenEmitted({
          field,
          component: 'NuInputNumber',
          emitValue: field,
          expected: {
            ...field,
            unit: 'METER',
          },
        })
      })
      it('- emits "change" with the correct payload when datatype is Rate on child component "change"', () => {
        const fields = [
          gResultField({ value: '123', unit: 'field unit' }),
          gResultField({ value: 123, unit: 'another unit' }),
          gResultField({ value: '123.45', unit: 'another unit' }),
        ]
        fields.forEach((field) => {
          hasEventBeenEmitted({
            emitValue: field,
            expected: {
              ...field,
              value: +field.value / 100,
            },
            component: 'NuInputNumber',
            field: gResultField({ type: 'Rate', value: field.value }),
          })
        })
      })
    })

    describe('Restore Events', () => {
      it('- emits "restore" with the correct payload when prop field has a unit on child component "restore"', () => {
        const field = gResultField({
          label: 'label',
          unit: 'expected mounted unit',
          type: 'Rate',
          value: 123,
        })
        hasEventBeenEmitted({
          field,
          parentEvent: 'restore',
          childEvent: 'restore',
          expected: {
            ...field,
            value: +field.value / 100,
            unit: field.unit ?? '',
          },
          component: 'NuInputNumber',
        })
      })
      it('- emits "restore" with the correct payload when prop field has no unit and there is no datatype on child component "restore"', () => {
        const field = gResultField({
          label: 'label',
        })
        hasEventBeenEmitted({
          field,
          parentEvent: 'restore',
          childEvent: 'restore',
          expected: {
            ...field,
            unit: undefined,
          },
        })
      })
      it('emits "restore" with the correct payload when mounted field has no unit and there is a datatype on child component "restore"', () => {
        const field = gResultField({
          label: 'label',
          type: 'Rate',
          value: 123,
        })
        hasEventBeenEmitted({
          field,
          parentEvent: 'restore',
          childEvent: 'restore',
          expected: {
            ...field,
            unit: undefined,
            value: +field.value / 100,
          },
          component: 'NuInputNumber',
        })
      })
      it('- emits "restore" with the correct payload when datatype is Rate on child component "restore"', () => {
        const fields = [
          gResultField({
            label: 'label',
            value: '123',
            unit: 'field unit',
            type: 'Rate',
          }),
          gResultField({
            label: 'label',
            value: 123,
            unit: 'another unit',
            type: 'Rate',
          }),
          gResultField({
            label: 'label',
            value: '123.45',
            unit: 'another unit',
            type: 'Rate',
          }),
        ]
        fields.forEach((field) => {
          hasEventBeenEmitted({
            parentEvent: 'restore',
            childEvent: 'restore',
            expected: {
              ...field,
              value: +field.value / 100,
            },
            component: 'NuInputNumber',
            field,
          })
        })
      })
    })
  })

  describe('LocalUnit handling', () => {
    it('should trigger the `updateUnit` function and update the `localUnit` when the `NuSideUnits` emits the `change` event', async () => {
      const field = gResultField({
        type: 'Length',
        unit: 'METER',
        value: 123,
        valueInDefaultUnit: 1234,
        metaInfo: {
          defaultUnit: { unit: 'MILLIMETER', isFixed: true },
        },
      })

      const expectedLocalUnit = { unit: 'KILOMETER', factor: 1 }

      const { getComponentProps, triggerEvent, getComponent } = setup({
        field,
      })
      triggerEvent('change', getComponent('NuSideUnits'), expectedLocalUnit)
      await nextTick()
      expect(getComponentProps('NuSideUnits', 'unit')).toStrictEqual(
        expectedLocalUnit
      )
    })

    it('- should call "setLocalUnit" only if prop "field.unit" changed to a different value compared to localUnit, on top of the first call in mounted (Without update)', async () => {
      const field = gResultField({
        unit: 'KILOGRAM',
      })
      const { updateProps, getEmittedEvent } = setup({
        field,
        localUnit: { unit: 'GRAMM', factor: 1000 },
      })

      await updateProps({ field: { ...field, unit: 'GRAMM' } })
      expect(getEmittedEvent('changed-local-unit')?.length).toBe(3)
    })

    it('- should call "setLocalUnit" only if prop "field.unit" changed to a different value compared to localUnit, on top of the first call in mounted (With update)', async () => {
      const field = gResultField({
        unit: 'KILOGRAM',
      })
      const { updateProps, getEmittedEvent } = setup({
        field,
        localUnit: { unit: 'GRAMM', factor: 1000 },
      })

      await updateProps({ field: { ...field, unit: 'MICROGRAMM' } })
      expect(getEmittedEvent('changed-local-unit')?.length).toBe(3)
    })
  })

  describe('Denominator Unit', () => {
    it('checks whether the `NuField` displays the unit/denominatorUnit `EUR/HUNDRED_PIECES` correctly', async () => {
      const field = gResultField({
        currencyInfo: { EUR: 1 },
        baseCurrency: 'EUR',
        name: 'test_field',
        type: 'Money',
        value: 1,
        source: 'C',
        denominatorUnit: {
          unit: 'HUNDRED_PIECES',
          type: 'PIECES',
          behavior: 'TRANSFORM',
        },
      })
      const { isMoneyUnitPresent, getMoneyUnitText } = setup({ field })
      await nextTick()
      expect(isMoneyUnitPresent()).toBeTruthy()
      expect(getMoneyUnitText()).toEqual(
        `EUR${THIN_SPACE}/${THIN_SPACE}HUNDRED_PIECES`
      )
    })
  })

  describe('Info Box', () => {
    describe('When field translation is not an object', () => {
      it('- does not have an InfoBox component present', () => {
        const { isTsetFieldInfoPresent } = setup()
        expect(isTsetFieldInfoPresent()).toBeFalsy()
      })
    })

    describe('When field translation is an object', () => {
      describe('When it does not have an info property', () => {
        it('- does not have an InfoBox component present', () => {
          const { isTsetFieldInfoPresent } = setup({
            field: gResultField({ name: 'withObjectWithoutInfo' }),
          })
          expect(isTsetFieldInfoPresent()).toBeFalsy()
        })
      })

      describe('When it does have an info property', () => {
        it('- has an InfoBox component present', () => {
          i18n.setLocaleMessage('en', {
            fields: { withObject: { info: 'info' } },
          })
          const { isTsetFieldInfoPresent } = setup({
            field: gResultField({ name: 'withObject' }),
          })
          expect(isTsetFieldInfoPresent()).toBeTruthy()
        })
      })
    })
  })
})
//#endregion TESTS
