import TsetHierarchicalSelectEntry from '@tset/design/molecules/TsetHierarchicalSelectNavigator/TsetHierarchicalSelectEntry.vue'
import TsetHierarchicalSelect from '@tset/design/organisms/TsetHierarchicalSelect/TsetHierarchicalSelect.vue'
import TsetSelectHeader from '@tset/design/organisms/TsetSelect/TsetSelectHeader.vue'
import { gResultField } from '@tset/shared-utils/tests/generators/resultField'
import { mount } from '@vue/test-utils'
import { nextTick } from 'vue'
import NuInputTree from './NuInputTree.vue'

vi.mock('@tset/shared-api/tree.api')

const setup = ({
  props,
}: {
  props: InstanceType<typeof NuInputTree>['$props']
}) => {
  const wrapper = mount(NuInputTree, {
    props,
    global: {
      stubs: ['IconChevronRight', 'IconCheckCircle', 'IconUnlink'],
    },
  })

  //#region Selectors
  const getInput = () => wrapper.findComponent(TsetSelectHeader)
  const getSystemValueEntry = () =>
    wrapper.findByDataTest('nu-input-tree-system-value-entry')
  //#endregion Selectors

  //#region Actions
  const clickInput = async () => {
    getInput().vm.$emit('click')
    await nextTick()
  }
  //#endregion Actions

  //#region HELPERS
  const triggerModelValueUpdate = () =>
    wrapper.findComponent(TsetHierarchicalSelect).vm.$emit('update:modelValue')
  const changeEmitted = () => wrapper.emitted()['change']
  const openPopup = () =>
    wrapper.findComponent(TsetSelectHeader).vm.$emit('click')
  const triggerSystemValueToggle = async () => {
    openPopup()
    await nextTick()
    wrapper.findComponent(TsetHierarchicalSelectEntry).vm.$emit('toggle')
  }
  const restoreEmitted = () => wrapper.emitted()['restore']
  //#endregion HELPERS

  return {
    changeEmitted,
    clickInput,
    getSystemValueEntry,
    restoreEmitted,
    triggerModelValueUpdate,
    triggerSystemValueToggle,
  }
}

describe('NuInputTree', () => {
  test('- renders the systemValue in bulk actions', async () => {
    const { clickInput, getSystemValueEntry } = setup({
      props: {
        field: gResultField({
          name: 'location',
          value: 'value',
          metaInfo: {
            path: 'mock/path/to/locations',
            section: 'bulkActions',
          },
          systemValue: 'systemValue',
        }),
      },
    })

    await clickInput()

    expect(getSystemValueEntry().exists()).toEqual(true)
  })

  describe('Emits', () => {
    it("- emits 'change' event on model value update", () => {
      const { triggerModelValueUpdate, changeEmitted } = setup({
        props: {
          field: gResultField({
            name: 'location',
            value: '',
            metaInfo: {
              path: 'mock/path/to/locations',
            },
          }),
        },
      })

      triggerModelValueUpdate()

      expect(changeEmitted()).toBeDefined()
    })
    it("- emits 'restore' event on system value click", async () => {
      const { triggerSystemValueToggle, restoreEmitted } = setup({
        props: {
          field: gResultField({
            name: 'location',
            value: '',
            metaInfo: {
              path: 'mock/path/to/locations',
            },
            systemValue: 'Austria',
          }),
        },
      })

      await triggerSystemValueToggle()

      expect(restoreEmitted()).toBeDefined()
    })
  })
})
