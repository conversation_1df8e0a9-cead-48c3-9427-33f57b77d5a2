import type { FieldType } from '@shared/field-types'
import { withMockNuTranslation } from '@shared/translation/nuTranslation.mock'
import TsetInput from '@tset/design/atoms/TsetInput'
import { wait } from '@tset/shared-utils/tests/general'
import { gResultField } from '@tset/shared-utils/tests/generators/resultField'
import { flushPromises, mount } from '@vue/test-utils'
import NuFieldSystemValue from './NuFieldSystemValue.vue'
import NuInputText from './NuInputText.vue'

//#region MOCKS
withMockNuTranslation()
const initialField = gResultField({ name: 'inputText', value: 'whatever' })

//#endregion MOCKS

//#region SETUP FACTORY
type SetupOptions = {
  field?: ResultField
  isManuallyOverridden?: boolean
  dataType?: Nullable<FieldType>
  originalUnit?: string
}

const setup = async ({
  field = initialField,
  isManuallyOverridden = false,
  dataType = null,
  originalUnit = 'originalUnit',
}: SetupOptions = {}) => {
  const mountOptions = {
    props: {
      field,
      isManuallyOverridden,
      dataType,
    },
    global: {
      provide: { originalUnit },
    },
  }

  const wrapper = mount(NuInputText, { ...mountOptions })
  await flushPromises()

  const getTsetInput = () => wrapper.getComponent(TsetInput)
  const getNuFieldSystemValue = () => wrapper.getComponent(NuFieldSystemValue)
  const getComponentProps = (
    component: 'TsetInput' | 'NuFieldSystemValue',
    propName: string
  ) => wrapper.getComponent({ name: component }).props(propName)
  const getEmittedEvent = (emittedEvent: string) =>
    wrapper.emitted(emittedEvent)
  const triggerEventTsetInput = async (event: string, payload?: any) =>
    getTsetInput().vm.$emit(event, payload)
  const inputText = async (value: string) =>
    await triggerEventTsetInput('update:modelValue', value)
  const getTerm = () => getComponentProps('TsetInput', 'modelValue')
  const getCurrentField = () => field
  const getCurrentFieldValue = () => getCurrentField().value
  const clickOnSystemValue = () => getNuFieldSystemValue().trigger('mousedown')
  const setProppedField = (field: ResultField) => wrapper.setProps({ field })

  return {
    getTsetInput,
    getNuFieldSystemValue,
    getComponentProps,
    getEmittedEvent,
    triggerEventTsetInput,
    getTerm,
    getCurrentField,
    getCurrentFieldValue,
    inputText,
    clickOnSystemValue,
    setProppedField,
  }
}
//#endregion SETUP FACTORY

//#region TESTS
describe('NuInputText tests', () => {
  describe('- when user clicks onto the input (focused state)', () => {
    it('- emits "focusin"', async () => {
      const { triggerEventTsetInput, getEmittedEvent } = await setup()
      await triggerEventTsetInput('focusin')
      expect(getEmittedEvent('focusin')?.length).toBe(1)
    })

    it('- should not update "term" from props when it is focused', async () => {
      const {
        getTerm,
        triggerEventTsetInput,
        getCurrentFieldValue,
        setProppedField,
      } = await setup()
      const currentFieldValue = getCurrentFieldValue()
      expect(getTerm()).toBe(`$nutTextField ${currentFieldValue}`)
      await triggerEventTsetInput('focusin')
      await setProppedField(gResultField({ value: 'new value' }))
      expect(getTerm()).toBe(`$nutTextField ${getCurrentFieldValue()}`)
    })
  })

  describe('- when user clicks away from the input (lost focus state)', () => {
    it('- emits "focusout"', async () => {
      const { triggerEventTsetInput, getEmittedEvent } = await setup()
      await triggerEventTsetInput('blur')
      expect(getEmittedEvent('focusout')?.length).toBe(1)
    })

    it('- allows update from props', async () => {
      const { getTerm, triggerEventTsetInput, setProppedField } = await setup({
        field: gResultField({ value: 'value' }),
      })
      await triggerEventTsetInput('focusin')
      expect(getTerm()).toBe('$nutTextField value')
      await triggerEventTsetInput('blur')
      await setProppedField(gResultField({ value: 'new value' }))
      expect(getTerm()).toBe('$nutTextField new value')
    })
  })

  describe('- when user enters new value', () => {
    it('- should update "term"', async () => {
      const { getTerm, inputText, getCurrentFieldValue } = await setup()
      expect(getTerm()).toBe(`$nutTextField ${getCurrentFieldValue()}`)
      await inputText('new text')
      expect(getTerm()).toBe('new text')
    })

    it('- emits "change" event', async () => {
      const {
        inputText,
        triggerEventTsetInput,
        getCurrentField,
        getEmittedEvent,
      } = await setup()

      const expected = 'expected'
      await inputText(expected)
      await triggerEventTsetInput('blur')
      expect(getEmittedEvent('change')?.length).toBe(1)
      expect(getEmittedEvent('change')![0][0]).toMatchObject({
        ...getCurrentField(),
        value: expected,
      })
    })

    describe('- when the field is empty and "metaInfo.mandatory" is true', () => {
      it('- should not save', async () => {
        const {
          triggerEventTsetInput,
          getEmittedEvent,
          inputText,
          getTerm,
          getCurrentFieldValue,
        } = await setup({
          field: gResultField({
            name: 'inputText',
            value: 'Nu input text test',
            metaInfo: {
              mandatory: true,
            },
          }),
        })

        await inputText('')
        await triggerEventTsetInput('blur')
        expect(getEmittedEvent('change')).toBeUndefined()
        expect(getTerm()).toBe(getCurrentFieldValue())
      })
    })
  })

  describe('- when custom metaInfo is set, pass TsetInputProps', () => {
    it('- metaInfo.maxLength', async () => {
      const { getComponentProps } = await setup({
        field: gResultField({
          metaInfo: { maxLength: 20 },
        }),
      })
      expect(getComponentProps('TsetInput', 'maximumCharacters')).toBe(20)
    })
    it('- metaInfo.placeHolder', async () => {
      const placeHolder = 'placeHolder'
      const { getComponentProps } = await setup({
        field: gResultField({
          metaInfo: { placeHolder },
        }),
      })

      expect(getComponentProps('TsetInput', 'placeholder')).toBe(placeHolder)
    })
    it('- metaInfo.uppercase', async () => {
      const { getComponentProps } = await setup({
        field: gResultField({
          metaInfo: { uppercase: true },
        }),
      })

      expect(getComponentProps('TsetInput', 'additionClasses')).toContain(
        'uppercase'
      )
    })
  })

  describe('- when user clicks on the SystemValue', () => {
    it('- should emit "restore" on click of child NuFieldSystemValue Component', async () => {
      const {
        triggerEventTsetInput,
        getEmittedEvent,
        getNuFieldSystemValue,
        clickOnSystemValue,
      } = await setup({
        field: gResultField({
          name: 'inputText',
          value: 'Nu input text test',
          metaInfo: {
            mandatory: true,
          },
          systemValue: 'A systemValue',
        }),
        isManuallyOverridden: true,
      })

      await triggerEventTsetInput('focusin')
      expect(getNuFieldSystemValue().isVisible()).toBe(true)
      await clickOnSystemValue()
      await wait(0)
      expect(getEmittedEvent('restore')?.length).toBe(1)
    })
  })
  describe('- when field has translationSection', () => {
    it('- update input value as the translated text', async () => {
      const { getTerm } = await setup({
        field: gResultField({
          value: 'firstField',
          metaInfo: { translationSection: '*' },
        }),
      })
      expect(getTerm()).toBe('$nutTextField firstField')
    })

    it("- shouldn't update term if the value hasn't changed", async () => {
      const { getEmittedEvent, triggerEventTsetInput, getTerm } = await setup({
        field: gResultField({
          value: 'secondField',
          metaInfo: { translationSection: '*' },
        }),
      })
      expect(getTerm()).toBe('$nutTextField secondField')
      await triggerEventTsetInput('focusin')
      await triggerEventTsetInput('blur')
      expect(getEmittedEvent('change')).toBe(undefined)
    })

    it('- should update if term changes', async () => {
      const field = gResultField({
        value: 'secondField',
        source: 'C',
        unit: 'PIECES',
        metaInfo: { translationSection: '*' },
      })
      const newValue = 'field value'
      const { getEmittedEvent, triggerEventTsetInput, getTerm, inputText } =
        await setup({ field })
      expect(getTerm()).toBe('$nutTextField secondField')
      await triggerEventTsetInput('focusin')
      await inputText(newValue)
      await triggerEventTsetInput('blur')
      expect(getEmittedEvent('change')).toStrictEqual([
        [{ ...field, value: newValue, source: 'I' }],
      ])
    })
  })
})
//#endregion TESTS
