{"openapi": "3.0.1", "info": {"title": "OpenAPI definition", "version": "v0"}, "servers": [{"url": "https://price-cc.cost.feature.tset.cloud", "description": "Generated server url"}], "paths": {"/api/md/v1/units/{key}": {"get": {"tags": ["unit"], "summary": "Get a unit", "operationId": "getUnit", "parameters": [{"name": "key", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UnitDto"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}, "put": {"tags": ["unit"], "summary": "Update or create a unit", "operationId": "updateUnit", "parameters": [{"name": "key", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UnitDto"}, "examples": {"Unit_meter": {"$ref": "#/components/examples/Unit_meter"}, "Unit_kilometer": {"$ref": "#/components/examples/Unit_kilometer"}}}}, "required": true}, "responses": {"422": {"description": "Unprocessable Entity", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UnitDto"}}}}, "201": {"description": "Created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UnitDto"}}}}, "409": {"description": "Conflict", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}, "post": {"tags": ["unit"], "summary": "Create a unit", "operationId": "createUnit", "parameters": [{"name": "key", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UnitDto"}, "examples": {"Unit_meter": {"$ref": "#/components/examples/Unit_meter"}, "Unit_kilometer": {"$ref": "#/components/examples/Unit_kilometer"}}}}, "required": true}, "responses": {"422": {"description": "Unprocessable Entity", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "201": {"description": "Created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UnitDto"}}}}, "409": {"description": "Conflict", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}, "delete": {"tags": ["unit"], "summary": "Delete a unit", "operationId": "deleteUnit", "parameters": [{"name": "key", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}, "409": {"description": "Conflict", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/api/md/v1/lovtypes/{key}": {"get": {"tags": ["lov-type"], "summary": "Get a lov type", "operationId": "getLovType", "parameters": [{"name": "key", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LovTypeDto"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}, "put": {"tags": ["lov-type"], "summary": "Update or create a lov type", "operationId": "updateLovType", "parameters": [{"name": "key", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LovTypeDto"}, "examples": {"LovType_transport_type": {"$ref": "#/components/examples/LovType_transport_type"}}}}, "required": true}, "responses": {"422": {"description": "Unprocessable Entity", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LovTypeDto"}}}}, "201": {"description": "Created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LovTypeDto"}}}}, "409": {"description": "Conflict", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}, "post": {"tags": ["lov-type"], "summary": "Create a lov type", "operationId": "createLovType", "parameters": [{"name": "key", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LovTypeDto"}, "examples": {"LovType_transport_type": {"$ref": "#/components/examples/LovType_transport_type"}}}}, "required": true}, "responses": {"422": {"description": "Unprocessable Entity", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "201": {"description": "Created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LovTypeDto"}}}}, "409": {"description": "Conflict", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}, "delete": {"tags": ["lov-type"], "summary": "Delete a lov type", "operationId": "deleteLovType", "parameters": [{"name": "key", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}, "409": {"description": "Conflict", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/api/md/v1/loventries/{key}": {"get": {"tags": ["lov-entry"], "summary": "Get a lov entry", "operationId": "getLovEntry", "parameters": [{"name": "key", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LovEntryDto"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}, "put": {"tags": ["lov-entry"], "summary": "Update or create a lov entry", "operationId": "updateLovEntry", "parameters": [{"name": "key", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LovEntryDto"}, "examples": {"LovEntry_rail": {"$ref": "#/components/examples/LovEntry_rail"}, "LovEntry_sea": {"$ref": "#/components/examples/LovEntry_sea"}}}}, "required": true}, "responses": {"422": {"description": "Unprocessable Entity", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LovEntryDto"}}}}, "201": {"description": "Created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LovEntryDto"}}}}, "409": {"description": "Conflict", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}, "post": {"tags": ["lov-entry"], "summary": "Create a lov entry", "operationId": "createLovEntry", "parameters": [{"name": "key", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LovEntryDto"}, "examples": {"LovEntry_rail": {"$ref": "#/components/examples/LovEntry_rail"}, "LovEntry_sea": {"$ref": "#/components/examples/LovEntry_sea"}}}}, "required": true}, "responses": {"422": {"description": "Unprocessable Entity", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "201": {"description": "Created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LovEntryDto"}}}}, "409": {"description": "Conflict", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}, "delete": {"tags": ["lov-entry"], "summary": "Delete a lov entry", "operationId": "deleteLovEntry", "parameters": [{"name": "key", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}, "409": {"description": "Conflict", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/api/md/v1/lookupstrategies/{key}": {"get": {"tags": ["lookup-strategy"], "summary": "Get a lookup strategy", "operationId": "getLookupStrategy", "parameters": [{"name": "key", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LookupStrategyDto"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}, "put": {"tags": ["lookup-strategy"], "summary": "Update or create a lookup strategy", "operationId": "updateLookupStrategy", "parameters": [{"name": "key", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LookupStrategyDto"}, "examples": {"LookupStrategy_overhead_strategy": {"$ref": "#/components/examples/LookupStrategy_overhead_strategy"}}}}, "required": true}, "responses": {"422": {"description": "Unprocessable Entity", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LookupStrategyDto"}}}}, "201": {"description": "Created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LookupStrategyDto"}}}}, "409": {"description": "Conflict", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}, "post": {"tags": ["lookup-strategy"], "summary": "Create a lookup strategy", "operationId": "createLookupStrategy", "parameters": [{"name": "key", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LookupStrategyDto"}, "examples": {"LookupStrategy_overhead_strategy": {"$ref": "#/components/examples/LookupStrategy_overhead_strategy"}}}}, "required": true}, "responses": {"422": {"description": "Unprocessable Entity", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "201": {"description": "Created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LookupStrategyDto"}}}}, "409": {"description": "Conflict", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}, "delete": {"tags": ["lookup-strategy"], "summary": "Delete a lookup strategy", "operationId": "deleteLookupStrategy", "parameters": [{"name": "key", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}, "409": {"description": "Conflict", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/api/md/v1/headertypes/{key}": {"get": {"tags": ["header-type"], "summary": "Get header type by its key", "operationId": "getHeaderType", "parameters": [{"name": "key", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HeaderTypeResponseDto"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}, "put": {"tags": ["header-type"], "summary": "Update or create a header type", "operationId": "updateHeaderType", "parameters": [{"name": "key", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "forceDeleteEffectivities", "in": "query", "required": false, "schema": {"type": "boolean"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/HeaderTypeDto"}, "examples": {"HeaderType_materials_-_header_classifications": {"$ref": "#/components/examples/HeaderType_materials_-_header_classifications"}, "HeaderType_transport_costs_-_numeric_schema": {"$ref": "#/components/examples/HeaderType_transport_costs_-_numeric_schema"}, "HeaderType_country_mapping_-_lov_schema": {"$ref": "#/components/examples/HeaderType_country_mapping_-_lov_schema"}, "HeaderType_detail_value_type_schema": {"$ref": "#/components/examples/HeaderType_detail_value_type_schema"}}}}, "required": true}, "responses": {"422": {"description": "Unprocessable Entity", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HeaderTypeResponseDto"}}}}, "201": {"description": "Created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HeaderTypeResponseDto"}}}}, "409": {"description": "Conflict", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}, "post": {"tags": ["header-type"], "summary": "Create a new header type", "operationId": "createHeaderType", "parameters": [{"name": "key", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/HeaderTypeDto"}, "examples": {"HeaderType_materials_-_header_classifications": {"$ref": "#/components/examples/HeaderType_materials_-_header_classifications"}, "HeaderType_transport_costs_-_numeric_schema": {"$ref": "#/components/examples/HeaderType_transport_costs_-_numeric_schema"}, "HeaderType_country_mapping_-_lov_schema": {"$ref": "#/components/examples/HeaderType_country_mapping_-_lov_schema"}, "HeaderType_detail_value_type_schema": {"$ref": "#/components/examples/HeaderType_detail_value_type_schema"}}}}, "required": true}, "responses": {"422": {"description": "Unprocessable Entity", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "201": {"description": "Created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HeaderTypeResponseDto"}}}}, "409": {"description": "Conflict", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}, "delete": {"tags": ["header-type"], "summary": "Delete a header type", "operationId": "deleteHeaderType", "parameters": [{"name": "key", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}, "409": {"description": "Conflict", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/api/md/v1/headertypes/{headerTypeKey}/headers/{key}": {"get": {"tags": ["header"], "summary": "Get header by header type and header key", "operationId": "<PERSON><PERSON><PERSON><PERSON>", "parameters": [{"name": "headerTypeKey", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "key", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HeaderDto"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}, "put": {"tags": ["header"], "summary": "Update or create a header", "operationId": "updateHeader", "parameters": [{"name": "headerTypeKey", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "key", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/HeaderDto"}, "examples": {"Header_electronic_component": {"$ref": "#/components/examples/Header_electronic_component"}, "Header_bar_material": {"$ref": "#/components/examples/Header_bar_material"}, "Header_exchange_rate": {"$ref": "#/components/examples/Header_exchange_rate"}, "Header_header_with_lov_detail_value": {"$ref": "#/components/examples/Header_header_with_lov_detail_value"}}}}, "required": true}, "responses": {"422": {"description": "Unprocessable Entity", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HeaderDto"}}}}, "201": {"description": "Created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HeaderDto"}}}}, "409": {"description": "Conflict", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}, "post": {"tags": ["header"], "summary": "Create a new header", "operationId": "createHeader", "parameters": [{"name": "headerTypeKey", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "key", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/HeaderDto"}, "examples": {"Header_electronic_component": {"$ref": "#/components/examples/Header_electronic_component"}, "Header_bar_material": {"$ref": "#/components/examples/Header_bar_material"}, "Header_exchange_rate": {"$ref": "#/components/examples/Header_exchange_rate"}, "Header_header_with_lov_detail_value": {"$ref": "#/components/examples/Header_header_with_lov_detail_value"}}}}, "required": true}, "responses": {"422": {"description": "Unprocessable Entity", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "201": {"description": "Created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HeaderDto"}}}}, "409": {"description": "Conflict", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}, "delete": {"tags": ["header"], "summary": "Delete a header", "operationId": "deleteHeader", "parameters": [{"name": "headerTypeKey", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "key", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "deleteDetails", "in": "query", "required": false, "schema": {"type": "boolean", "default": false}}], "responses": {"200": {"description": "OK"}, "409": {"description": "Conflict", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/api/md/v1/headertypes/{headerTypeKey}/headers/number:{number}/revision:{revision}": {"get": {"tags": ["header"], "summary": "Get header by header type and header key", "operationId": "getHeaderMaterial", "parameters": [{"name": "headerTypeKey", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "number", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "revision", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/HeaderDto"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}, "put": {"tags": ["header"], "summary": "Update or create a header", "operationId": "updateHeaderMaterial", "parameters": [{"name": "headerTypeKey", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "number", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "revision", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/HeaderDto"}, "examples": {"Header_electronic_component": {"$ref": "#/components/examples/Header_electronic_component"}, "Header_bar_material": {"$ref": "#/components/examples/Header_bar_material"}, "Header_exchange_rate": {"$ref": "#/components/examples/Header_exchange_rate"}, "Header_header_with_lov_detail_value": {"$ref": "#/components/examples/Header_header_with_lov_detail_value"}}}}, "required": true}, "responses": {"422": {"description": "Unprocessable Entity", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/HeaderDto"}}}}, "201": {"description": "Created", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/HeaderDto"}}}}, "409": {"description": "Conflict", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}, "post": {"tags": ["header"], "summary": "Create a new header", "operationId": "createHeaderMaterial", "parameters": [{"name": "headerTypeKey", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "number", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "revision", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/HeaderDto"}, "examples": {"Header_electronic_component": {"$ref": "#/components/examples/Header_electronic_component"}, "Header_bar_material": {"$ref": "#/components/examples/Header_bar_material"}, "Header_exchange_rate": {"$ref": "#/components/examples/Header_exchange_rate"}, "Header_header_with_lov_detail_value": {"$ref": "#/components/examples/Header_header_with_lov_detail_value"}}}}, "required": true}, "responses": {"422": {"description": "Unprocessable Entity", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "201": {"description": "Created", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/HeaderDto"}}}}, "409": {"description": "Conflict", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}, "delete": {"tags": ["header"], "summary": "Delete a header", "operationId": "deleteHeaderMaterial", "parameters": [{"name": "headerTypeKey", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "number", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "revision", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "deleteDetails", "in": "query", "required": false, "schema": {"type": "boolean", "default": false}}], "responses": {"200": {"description": "OK"}, "409": {"description": "Conflict", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/api/md/v1/headertypes/{headerTypeKey}/details": {"put": {"tags": ["detail"], "summary": "Update an existing detail", "description": "Deactivate the given detail and creates a new one.", "operationId": "replaceDetail", "parameters": [{"name": "headerTypeKey", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "allowOverwrite", "in": "query", "required": false, "schema": {"type": "boolean", "default": false}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ReplaceDetailRequestDto"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/DetailDto"}}}}}}, "post": {"tags": ["detail"], "summary": "Create and/or update multiple details of a header type", "operationId": "postDetails", "parameters": [{"name": "headerTypeKey", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DetailDto"}}, "examples": {"SingletonList_details_-_numeric": {"$ref": "#/components/examples/SingletonList_details_-_numeric"}, "SingletonList_details_-_lov": {"$ref": "#/components/examples/SingletonList_details_-_lov"}, "SingletonList_details_-_valuetype": {"$ref": "#/components/examples/SingletonList_details_-_valuetype"}, "SingletonList_details_-_price_composition": {"$ref": "#/components/examples/SingletonList_details_-_price_composition"}, "SingletonList_details_-_material_composition": {"$ref": "#/components/examples/SingletonList_details_-_material_composition"}}}}, "required": true}, "responses": {"200": {"description": "All details were successfully processed. The return list is in the same order as the input.", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DetailDto"}}}}}, "422": {"description": "Some details were malformed. The returned list is in the same order as the input, with an item either being a detail (success) or an object with the dto and all detected errors.", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DetailBulkResponseDto"}}}}}}}, "delete": {"tags": ["detail"], "summary": "Delete an existing detail", "description": "Delete the given detail.Return true if the detail was deleted.", "operationId": "deleteDetail", "parameters": [{"name": "headerTypeKey", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DetailReferenceDto"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "boolean"}}}}}}}, "/api/md/v1/headertypes/{headerTypeKey}/details/check-exists": {"put": {"tags": ["detail"], "summary": "Checks if the given detail exists", "operationId": "checkIfExists", "parameters": [{"name": "headerTypeKey", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DetailReferenceDto"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "boolean"}}}}}}}, "/api/md/v1/fields/{key}": {"get": {"tags": ["field-definition"], "summary": "Get a field definition", "operationId": "getFieldDefinition", "parameters": [{"name": "key", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FieldDefinitionDto"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}, "put": {"tags": ["field-definition"], "summary": "Update or create a field definition", "operationId": "updateFieldDefinition", "parameters": [{"name": "key", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FieldDefinitionDto"}, "examples": {"TextFieldDefinition_part_name": {"$ref": "#/components/examples/TextFieldDefinition_part_name"}, "NumericFieldDefinition_part_length": {"$ref": "#/components/examples/NumericFieldDefinition_part_length"}, "DateFieldDefinition_calculation_date": {"$ref": "#/components/examples/DateFieldDefinition_calculation_date"}, "LovFieldDefinition_transport_type": {"$ref": "#/components/examples/LovFieldDefinition_transport_type"}, "ClassificationFieldDefinition_location": {"$ref": "#/components/examples/ClassificationFieldDefinition_location"}}}}, "required": true}, "responses": {"422": {"description": "Unprocessable Entity", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FieldDefinitionDto"}}}}, "201": {"description": "Created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FieldDefinitionDto"}}}}, "409": {"description": "Conflict", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}, "post": {"tags": ["field-definition"], "summary": "Create a field definition", "operationId": "createFieldDefinition", "parameters": [{"name": "key", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FieldDefinitionDto"}, "examples": {"TextFieldDefinition_part_name": {"$ref": "#/components/examples/TextFieldDefinition_part_name"}, "NumericFieldDefinition_part_length": {"$ref": "#/components/examples/NumericFieldDefinition_part_length"}, "DateFieldDefinition_calculation_date": {"$ref": "#/components/examples/DateFieldDefinition_calculation_date"}, "LovFieldDefinition_transport_type": {"$ref": "#/components/examples/LovFieldDefinition_transport_type"}, "ClassificationFieldDefinition_location": {"$ref": "#/components/examples/ClassificationFieldDefinition_location"}}}}, "required": true}, "responses": {"422": {"description": "Unprocessable Entity", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "201": {"description": "Created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FieldDefinitionDto"}}}}, "409": {"description": "Conflict", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}, "delete": {"tags": ["field-definition"], "summary": "Delete a field definition", "operationId": "deleteFieldDefinition", "parameters": [{"name": "key", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}, "409": {"description": "Conflict", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/api/md/v1/dimensions/{key}": {"get": {"tags": ["dimension"], "summary": "Get a dimension", "operationId": "getDimension", "parameters": [{"name": "key", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DimensionDto"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}, "put": {"tags": ["dimension"], "summary": "Update or create a dimension", "description": "When creating a new dimension, at least the base unit with factor `1` must be present in the request. When updating a dimension, units may be null. If they are not null, units will be created, updated, or deleted so that the final result matches the sent units.", "operationId": "updateDimension", "parameters": [{"name": "key", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DimensionDto"}, "examples": {"Dimension_distance": {"$ref": "#/components/examples/Dimension_distance"}, "distance_creation": {"$ref": "#/components/examples/distance_creation"}}}}, "required": true}, "responses": {"422": {"description": "Unprocessable Entity", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DimensionDto"}}}}, "201": {"description": "Created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DimensionDto"}}}}, "409": {"description": "Conflict", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}, "post": {"tags": ["dimension"], "summary": "Create a dimension", "description": "Creates a dimension and its units. Units may also be created later, but at least the base unit with a factor of `1` must be present.", "operationId": "createDimension", "parameters": [{"name": "key", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DimensionDto"}, "examples": {"distance_creation": {"$ref": "#/components/examples/distance_creation"}}}}, "required": true}, "responses": {"422": {"description": "Unprocessable Entity", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "201": {"description": "Created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DimensionDto"}}}}, "409": {"description": "Conflict", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}, "delete": {"tags": ["dimension"], "summary": "Delete a dimension", "operationId": "deleteDimension", "parameters": [{"name": "key", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}, "409": {"description": "Conflict", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/api/md/v1/currencies/{key}": {"get": {"tags": ["currency"], "summary": "Get a currency", "operationId": "getCurrency", "parameters": [{"name": "key", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CurrencyDto"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}, "put": {"tags": ["currency"], "summary": "Update or create a currency", "operationId": "updateCurrency", "parameters": [{"name": "key", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CurrencyDto"}, "examples": {"Currency_paraguayan_guarani": {"$ref": "#/components/examples/Currency_paraguayan_guarani"}, "Currency_ghanaian_cedi": {"$ref": "#/components/examples/Currency_ghanaian_cedi"}}}}, "required": true}, "responses": {"422": {"description": "Unprocessable Entity", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CurrencyDto"}}}}, "201": {"description": "Created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CurrencyDto"}}}}, "409": {"description": "Conflict", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}, "post": {"tags": ["currency"], "summary": "Create a currency", "operationId": "createCurrency", "parameters": [{"name": "key", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CurrencyDto"}, "examples": {"Currency_paraguayan_guarani": {"$ref": "#/components/examples/Currency_paraguayan_guarani"}, "Currency_ghanaian_cedi": {"$ref": "#/components/examples/Currency_ghanaian_cedi"}}}}, "required": true}, "responses": {"422": {"description": "Unprocessable Entity", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "201": {"description": "Created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CurrencyDto"}}}}, "409": {"description": "Conflict", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}, "delete": {"tags": ["currency"], "summary": "Delete a currency", "operationId": "deleteCurrency", "parameters": [{"name": "key", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}, "409": {"description": "Conflict", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/api/md/v1/classificationtypes/{key}": {"get": {"tags": ["classification-type"], "summary": "Get a classification type", "operationId": "getClassificationType", "parameters": [{"name": "key", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClassificationTypeDto"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}, "put": {"tags": ["classification-type"], "summary": "Update or create a classification type", "operationId": "updateClassificationType", "parameters": [{"name": "key", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClassificationTypeDto"}, "examples": {"ClassificationType_location": {"$ref": "#/components/examples/ClassificationType_location"}}}}, "required": true}, "responses": {"422": {"description": "Unprocessable Entity", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClassificationTypeDto"}}}}, "201": {"description": "Created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClassificationTypeDto"}}}}, "409": {"description": "Conflict", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}, "post": {"tags": ["classification-type"], "summary": "Create a classification type", "operationId": "createClassificationType", "parameters": [{"name": "key", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClassificationTypeDto"}, "examples": {"ClassificationType_location": {"$ref": "#/components/examples/ClassificationType_location"}}}}, "required": true}, "responses": {"422": {"description": "Unprocessable Entity", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "201": {"description": "Created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClassificationTypeDto"}}}}, "409": {"description": "Conflict", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}, "delete": {"tags": ["classification-type"], "summary": "Delete a classification type", "operationId": "deleteClassificationType", "parameters": [{"name": "key", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "deleteClassificationsAndFields", "in": "query", "description": "if this parameter is set to true, all classification fields and classifications are also deleted when they are not used", "required": false, "schema": {"type": "boolean", "description": "if this parameter is set to true, all classification fields and classifications are also deleted when they are not used"}}], "responses": {"200": {"description": "OK"}, "409": {"description": "Conflict", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/api/md/v1/classifications/{key}/fields": {"get": {"tags": ["classification"], "summary": "Get fields of classification", "operationId": "getClassificationFields", "parameters": [{"name": "key", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/ClassificationFieldResponseDto"}}}}}}}, "put": {"tags": ["classification"], "summary": "Set fields of classification", "operationId": "setClassificationFields", "parameters": [{"name": "key", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/ClassificationFieldRequestDto"}}, "examples": {"SingletonMap_raw_material_fields": {"$ref": "#/components/examples/SingletonMap_raw_material_fields"}, "LinkedHashMap_bar_fields": {"$ref": "#/components/examples/LinkedHashMap_bar_fields"}, "LinkedHashMap_electronic_component_fields": {"$ref": "#/components/examples/LinkedHashMap_electronic_component_fields"}}}}, "required": true}, "responses": {"422": {"description": "Unprocessable Entity", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/ClassificationFieldResponseDto"}}}}}, "201": {"description": "Created", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/ClassificationFieldResponseDto"}}}}}, "409": {"description": "Conflict", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/api/md/v1/classifications/{key}": {"get": {"tags": ["classification"], "summary": "Get a classification", "operationId": "getClassification", "parameters": [{"name": "key", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClassificationDto"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}, "put": {"tags": ["classification"], "summary": "Update or create a classification", "operationId": "updateClassification", "parameters": [{"name": "key", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClassificationDto"}, "examples": {"Classification_world_-_root": {"$ref": "#/components/examples/Classification_world_-_root"}, "Classification_emea": {"$ref": "#/components/examples/Classification_emea"}, "Classification_austria": {"$ref": "#/components/examples/Classification_austria"}, "Classification_vienna": {"$ref": "#/components/examples/Classification_vienna"}, "Classification_lower_austria": {"$ref": "#/components/examples/Classification_lower_austria"}, "Classification_germany": {"$ref": "#/components/examples/Classification_germany"}, "Classification_berlin": {"$ref": "#/components/examples/Classification_berlin"}}}}, "required": true}, "responses": {"422": {"description": "Unprocessable Entity", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClassificationDto"}}}}, "201": {"description": "Created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClassificationDto"}}}}, "409": {"description": "Conflict", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}, "post": {"tags": ["classification"], "summary": "Create a classification", "operationId": "createClassification", "parameters": [{"name": "key", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClassificationDto"}, "examples": {"Classification_world_-_root": {"$ref": "#/components/examples/Classification_world_-_root"}, "Classification_emea": {"$ref": "#/components/examples/Classification_emea"}, "Classification_austria": {"$ref": "#/components/examples/Classification_austria"}, "Classification_vienna": {"$ref": "#/components/examples/Classification_vienna"}, "Classification_lower_austria": {"$ref": "#/components/examples/Classification_lower_austria"}, "Classification_germany": {"$ref": "#/components/examples/Classification_germany"}, "Classification_berlin": {"$ref": "#/components/examples/Classification_berlin"}}}}, "required": true}, "responses": {"422": {"description": "Unprocessable Entity", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "201": {"description": "Created", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClassificationDto"}}}}, "409": {"description": "Conflict", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}, "delete": {"tags": ["classification"], "summary": "Delete a classification", "operationId": "deleteClassification", "parameters": [{"name": "key", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}, "409": {"description": "Conflict", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "404": {"description": "Not Found", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/api/md/v1/headertypes/{headerTypeKey}/headers": {"get": {"tags": ["header"], "summary": "Find headers of a header type", "operationId": "findAllHeadersOfType", "parameters": [{"name": "headerTypeKey", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "name", "in": "query", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/HeaderDto"}}}}}}}, "post": {"tags": ["header"], "summary": "Create or update multiple headers of a header type", "operationId": "postHeaders", "parameters": [{"name": "headerTypeKey", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/HeaderDto"}}}}, "required": true}, "responses": {"200": {"description": "All headers were successfully processed. The returned list is in the same order as the input.", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/HeaderDto"}}}}}, "422": {"description": "Some headers were malformed. The returned list is in the same order as the input, with an item either being a header (success) or an object with the dto and all detected errors.", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/HeaderBulkResponseDto"}}}}}}}}, "/api/md/v1/headertypes/{headerTypeKey}/details/search": {"post": {"tags": ["detail"], "summary": "Query a header type for details", "operationId": "getDetails", "parameters": [{"name": "headerTypeKey", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "page", "in": "query", "description": "page number starting at index 0 (default 0)", "required": false, "schema": {"type": "string", "description": "page number starting at index 0 (default 0)"}}, {"name": "size", "in": "query", "description": "page size; maximum number of returned items (default 100)", "required": false, "schema": {"type": "string", "description": "page size; maximum number of returned items (default 100)"}}, {"name": "headerOnly", "in": "query", "description": "do not expand result to multi dimensional values", "required": false, "schema": {"type": "string", "description": "do not expand result to multi dimensional values"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DetailQueryDto"}, "examples": {"DetailQuery_search_overhead_rates": {"$ref": "#/components/examples/DetailQuery_search_overhead_rates"}, "DetailQuery_search_without_filters": {"$ref": "#/components/examples/DetailQuery_search_without_filters"}}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/DetailQueryResponseDto"}}}}}}}, "/api/md/v1/headertypes/{headerTypeKey}/details/lookup": {"post": {"tags": ["detail"], "summary": "Perform a masterdata lookup on the header type", "operationId": "lookup", "parameters": [{"name": "headerTypeKey", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/MasterdataLookupRequest"}, "examples": {"MasterdataLookupRequest_overhead_rate_lookup": {"$ref": "#/components/examples/MasterdataLookupRequest_overhead_rate_lookup"}}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MasterdataLookupResponse"}}}}}}}, "/api/md/v1/headertypes/{headerTypeKey}/details/deactivate": {"post": {"tags": ["detail"], "summary": "Deactivate an existing detail", "description": "Deactivate the given detail.Return true if the detail was deactivated, false if it already was inactive.", "operationId": "deactivateDetail", "parameters": [{"name": "headerTypeKey", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DetailReferenceDto"}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "boolean"}}}}}}}, "/api/md/v1/headertypes/{headerTypeKey}/details/create": {"post": {"tags": ["detail"], "summary": "Create a new detail", "operationId": "createDetail", "parameters": [{"name": "headerTypeKey", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "allowOverwrite", "in": "query", "required": false, "schema": {"type": "boolean", "default": false}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DetailDto"}, "examples": {"Detail_overhead_rate": {"$ref": "#/components/examples/Detail_overhead_rate"}, "Detail_lov_detail_-_high_wage_country": {"$ref": "#/components/examples/Detail_lov_detail_-_high_wage_country"}, "Detail_detail_-_valuetype": {"$ref": "#/components/examples/Detail_detail_-_valuetype"}, "Detail_detail_-_price_composition": {"$ref": "#/components/examples/Detail_detail_-_price_composition"}, "Detail_detail_-_material_composition": {"$ref": "#/components/examples/Detail_detail_-_material_composition"}}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"$ref": "#/components/schemas/DetailDto"}}}}}}}, "/api/md/v1/details/latest-version": {"post": {"tags": ["detail"], "summary": "Get the greatest version timestamp of any details of the given headers", "operationId": "getLatestVersion", "requestBody": {"description": "Map of header type key to headers for which the latest version should be fetched.\nIf the set of header keys is null, *all* headers for that type will be considered.", "content": {"application/json": {"schema": {"type": "object", "additionalProperties": {"uniqueItems": true, "type": "array", "items": {"$ref": "#/components/schemas/HeaderNaturalKeyDto"}}}, "examples": {"LinkedHashMap_get_latest_version_for_header_types": {"$ref": "#/components/examples/LinkedHashMap_get_latest_version_for_header_types"}}}}, "required": true}, "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetLatestVersionResponseDto"}}}}}}}, "/api/md/v1/units": {"get": {"tags": ["unit"], "summary": "Find units", "operationId": "findAllUnits", "parameters": [{"name": "name", "in": "query", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/UnitDto"}}}}}}}}, "/api/md/v1/permissions": {"get": {"tags": ["permissions"], "summary": "Get permissions of current user", "operationId": "getPermissions", "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"type": "string"}}}}}}}}, "/api/md/v1/lovtypes/{key}/entries": {"get": {"tags": ["lov-type"], "summary": "Get all entries of this lov type", "operationId": "getLovEntriesOfLovType", "parameters": [{"name": "key", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "searchStr", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "in", "in": "query", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/LovEntryDto"}}}}}}}}, "/api/md/v1/lovtypes": {"get": {"tags": ["lov-type"], "summary": "Find lov types", "operationId": "findAllLovTypes", "parameters": [{"name": "name", "in": "query", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/LovTypeDto"}}}}}}}}, "/api/md/v1/loventries": {"get": {"tags": ["lov-entry"], "summary": "Find lov entries", "operationId": "findAllLovEntries", "parameters": [{"name": "name", "in": "query", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/LovEntryDto"}}}}}}}}, "/api/md/v1/lookupstrategies": {"get": {"tags": ["lookup-strategy"], "summary": "Find lookup strategies", "operationId": "findAllLookupStrategies", "parameters": [{"name": "name", "in": "query", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/LookupStrategyDto"}}}}}}}}, "/api/md/v1/headertypes/{headerTypeKey}/builtinlovtypes/{fieldKey}/entries": {"get": {"tags": ["detail"], "summary": "Get entries for built in lov type in the context of a header type", "operationId": "getBuiltinLovEntries", "parameters": [{"name": "headerTypeKey", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "<PERSON><PERSON><PERSON>", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "name", "in": "query", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"*/*": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/BuiltinLovResponseDto"}}}}}}}}, "/api/md/v1/headertypes": {"get": {"tags": ["header-type"], "summary": "Find header types", "operationId": "findAllHeaderTypes", "parameters": [{"name": "name", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "includeInactive", "in": "query", "required": false, "schema": {"type": "boolean"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/HeaderTypeListResponseDto"}}}}}}}}, "/api/md/v1/fields": {"get": {"tags": ["field-definition"], "summary": "Find field definitions", "operationId": "findAllFieldDefinitions", "parameters": [{"name": "name", "in": "query", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/FieldDefinitionDto"}}}}}}}}, "/api/md/v1/dimensions/{key}/units": {"get": {"tags": ["dimension"], "summary": "Get all units of this dimension", "operationId": "getUnitsOfDimension", "parameters": [{"name": "key", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "searchStr", "in": "query", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/UnitDto"}}}}}}}}, "/api/md/v1/dimensions": {"get": {"tags": ["dimension"], "summary": "Find dimensions", "operationId": "findAllDimensions", "parameters": [{"name": "name", "in": "query", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/DimensionDto"}}}}}}}}, "/api/md/v1/currencies": {"get": {"tags": ["currency"], "summary": "Find currencies", "operationId": "findAllCurrencies", "parameters": [{"name": "name", "in": "query", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/CurrencyDto"}}}}}}}}, "/api/md/v1/classificationtypes/{key}/roots": {"get": {"tags": ["classification-type"], "summary": "Get the root classification of this type, optionally the whole tree", "operationId": "getClassificationsOfType", "parameters": [{"name": "key", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "searchStr", "in": "query", "required": false, "schema": {"type": "string"}}, {"name": "includeDescendants", "in": "query", "required": false, "schema": {"type": "boolean", "default": false}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ClassificationDto"}}}}}}}}, "/api/md/v1/classificationtypes/{key}/fields": {"get": {"tags": ["classification-type"], "summary": "Gets the fields of one or more classifications of this classification type", "operationId": "getClassificationTypeFields", "parameters": [{"name": "key", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "classificationKeys", "in": "query", "description": "return all fields associated with these classifications or with an ancestor of these. If request parameter is omitted, all fields of the complete classificationType are returned", "required": false, "schema": {"type": "string", "description": "return all fields associated with these classifications or with an ancestor of these. If request parameter is omitted, all fields of the complete classificationType are returned"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ClassificationFieldsInfoDto"}}}}, "404": {"description": "Not Found", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/api/md/v1/classificationtypes": {"get": {"tags": ["classification-type"], "summary": "Find classification types", "operationId": "findAllClassificationTypes", "parameters": [{"name": "name", "in": "query", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ClassificationTypeDto"}}}}}}}}, "/api/md/v1/classifications/{key}/parents": {"get": {"tags": ["classification"], "summary": "Get all parent classifications of a node", "operationId": "getParentClassifications", "parameters": [{"name": "key", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ClassificationDto"}}}}}}}}, "/api/md/v1/classifications/{key}/children": {"get": {"tags": ["classification"], "summary": "Get direct child classifications of a node", "operationId": "getChildClassifications", "parameters": [{"name": "key", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "searchStr", "in": "query", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ClassificationDto"}}}}}}}}, "/api/md/v1/classifications": {"get": {"tags": ["classification"], "summary": "Find classifications", "operationId": "findAllClassifications", "parameters": [{"name": "name", "in": "query", "required": false, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/ClassificationDto"}}}}}}}}}, "components": {"schemas": {"UnitDto": {"required": ["dimension<PERSON>ey", "factor", "key", "name"], "type": "object", "properties": {"key": {"pattern": "^[a-zA-Z0-9.\\-_]+$", "type": "string", "description": "A simple key that consists of just a single String value. The key must match the given pattern, and additionally it must contain at least one digit or letter"}, "dimensionKey": {"pattern": "^[a-zA-Z0-9.\\-_]+$", "type": "string", "description": "A simple key that consists of just a single String value. The key must match the given pattern, and additionally it must contain at least one digit or letter"}, "name": {"type": "string"}, "factor": {"minimum": 0, "exclusiveMinimum": true, "type": "number", "format": "double"}}, "description": "Defines a single unit for a dimension (DimensionDto)"}, "ErrorResponse": {"type": "object", "properties": {"timestamp": {"type": "string", "format": "date-time"}, "path": {"type": "string"}, "status": {"type": "integer", "format": "int32"}, "error": {"type": "string"}, "message": {"type": "string"}, "requestId": {"type": "string"}, "trace": {"type": "string"}, "errorCode": {"type": "string"}, "userErrorCode": {"type": "string"}, "userParameters": {"type": "array", "items": {"type": "object"}}, "fallbackMessage": {"type": "string"}}}, "LovTypeDto": {"required": ["key", "name"], "type": "object", "properties": {"key": {"pattern": "^[a-zA-Z0-9.\\-_]+$", "type": "string", "description": "A simple key that consists of just a single String value. The key must match the given pattern, and additionally it must contain at least one digit or letter"}, "name": {"type": "string"}, "systemManaged": {"type": "boolean"}}, "description": "Defines a List Of Values type. Each LovTypeDto consists of multiple entries of type LovEntryDto"}, "LovEntryDto": {"required": ["key", "lovTypeKey", "name"], "type": "object", "properties": {"key": {"pattern": "^[a-zA-Z0-9.\\-_]+$", "type": "string", "description": "A simple key that consists of just a single String value. The key must match the given pattern, and additionally it must contain at least one digit or letter"}, "lovTypeKey": {"pattern": "^[a-zA-Z0-9.\\-_]+$", "type": "string", "description": "A simple key that consists of just a single String value. The key must match the given pattern, and additionally it must contain at least one digit or letter"}, "name": {"type": "string"}, "shortName": {"type": "string"}, "systemManaged": {"type": "boolean"}}, "description": "Defines a single item of a List of Values type, defined by lovTypeKey (LovTypeDto)"}, "ClassificationEffectivityDefinitionDto": {"required": ["greenFallbackDistance", "mandatory", "priority", "type", "yellowFallbackDistance"], "type": "object", "properties": {"lookupOrder": {"type": "integer", "format": "int32"}, "greenFallbackDistance": {"type": "integer", "format": "int32"}, "yellowFallbackDistance": {"type": "integer", "format": "int32"}, "mandatory": {"type": "boolean"}, "priority": {"type": "integer", "format": "int32"}, "type": {"type": "string"}, "defaultValue": {"pattern": "^[a-zA-Z0-9.\\-_]+$", "type": "string", "description": "A simple key that consists of just a single String value. The key must match the given pattern, and additionally it must contain at least one digit or letter"}}}, "CurrencyMeasurementDto": {"required": ["key", "type"], "type": "object", "properties": {"key": {"pattern": "^[a-zA-Z0-9.\\-_]+$", "type": "string", "description": "A simple key that consists of just a single String value. The key must match the given pattern, and additionally it must contain at least one digit or letter"}, "displayName": {"type": "string"}, "type": {"type": "string"}}}, "DateEffectivityDefinitionDto": {"required": ["greenFallbackDistance", "mandatory", "priority", "type", "yellowFallbackDistance"], "type": "object", "properties": {"lookupOrder": {"type": "integer", "format": "int32"}, "greenFallbackDistance": {"type": "integer", "format": "int32"}, "yellowFallbackDistance": {"type": "integer", "format": "int32"}, "mandatory": {"type": "boolean"}, "priority": {"type": "integer", "format": "int32"}, "type": {"type": "string"}, "defaultValue": {"type": "string", "format": "date"}}}, "LookupStrategyDto": {"required": ["effectivities", "headerTypeKey", "key", "name"], "type": "object", "properties": {"key": {"pattern": "^[a-zA-Z0-9.\\-_]+$", "type": "string", "description": "A simple key that consists of just a single String value. The key must match the given pattern, and additionally it must contain at least one digit or letter"}, "name": {"type": "string"}, "headerTypeKey": {"pattern": "^[a-zA-Z0-9.\\-_]+$", "type": "string", "description": "A simple key that consists of just a single String value. The key must match the given pattern, and additionally it must contain at least one digit or letter"}, "effectivities": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/LookupStrategyEffectivityDto"}}}}, "LookupStrategyEffectivityDto": {"type": "object", "discriminator": {"propertyName": "type", "mapping": {"date": "#/components/schemas/DateEffectivityDefinitionDto", "numeric": "#/components/schemas/NumericEffectivityDefinitionDto", "classification": "#/components/schemas/ClassificationEffectivityDefinitionDto", "lov": "#/components/schemas/LovEffectivityDefinitionDto"}}, "oneOf": [{"$ref": "#/components/schemas/DateEffectivityDefinitionDto"}, {"$ref": "#/components/schemas/NumericEffectivityDefinitionDto"}, {"$ref": "#/components/schemas/ClassificationEffectivityDefinitionDto"}, {"$ref": "#/components/schemas/LovEffectivityDefinitionDto"}]}, "LovEffectivityDefinitionDto": {"required": ["greenFallbackDistance", "mandatory", "priority", "type", "validLovEntries", "yellowFallbackDistance"], "type": "object", "properties": {"lookupOrder": {"type": "integer", "format": "int32"}, "greenFallbackDistance": {"type": "integer", "format": "int32"}, "yellowFallbackDistance": {"type": "integer", "format": "int32"}, "mandatory": {"type": "boolean"}, "priority": {"type": "integer", "format": "int32"}, "type": {"type": "string"}, "defaultValue": {"pattern": "^[a-zA-Z0-9.\\-_]+$", "type": "string", "description": "A simple key that consists of just a single String value. The key must match the given pattern, and additionally it must contain at least one digit or letter"}, "validLovEntries": {"type": "array", "items": {"pattern": "^[a-zA-Z0-9.\\-_]+$", "type": "string", "description": "A simple key that consists of just a single String value. The key must match the given pattern, and additionally it must contain at least one digit or letter"}}}}, "MeasurementDto": {"type": "object", "description": "Numerator or denominator can be of type 'unit' or 'currency'. null can be used to indicate that the numerator/denominator is dimensionless", "discriminator": {"propertyName": "type", "mapping": {"unit": "#/components/schemas/UnitMeasurementDto", "currency": "#/components/schemas/CurrencyMeasurementDto"}}, "oneOf": [{"$ref": "#/components/schemas/UnitMeasurementDto"}, {"$ref": "#/components/schemas/CurrencyMeasurementDto"}]}, "NumericEffectivityDefinitionDto": {"required": ["greenFallbackDistance", "mandatory", "priority", "type", "yellowFallbackDistance"], "type": "object", "properties": {"lookupOrder": {"type": "integer", "format": "int32"}, "greenFallbackDistance": {"type": "integer", "format": "int32"}, "yellowFallbackDistance": {"type": "integer", "format": "int32"}, "mandatory": {"type": "boolean"}, "priority": {"type": "integer", "format": "int32"}, "type": {"type": "string"}, "defaultValue": {"$ref": "#/components/schemas/NumericValueDto"}}}, "NumericValueDto": {"required": ["type", "value"], "type": "object", "properties": {"type": {"type": "string"}, "value": {"type": "number", "format": "double"}, "numerator": {"$ref": "#/components/schemas/MeasurementDto"}, "denominator": {"$ref": "#/components/schemas/MeasurementDto"}}, "description": "A numeric value with an unit or currency."}, "UnitMeasurementDto": {"required": ["key", "type"], "type": "object", "properties": {"key": {"pattern": "^[a-zA-Z0-9.\\-_]+$", "type": "string", "description": "A simple key that consists of just a single String value. The key must match the given pattern, and additionally it must contain at least one digit or letter"}, "displayName": {"type": "string"}, "type": {"type": "string"}}}, "AnyCurrencyTypeDto": {"required": ["defaultCurrencyKey", "type"], "type": "object", "properties": {"type": {"type": "string"}, "defaultCurrencyKey": {"pattern": "^[a-zA-Z0-9.\\-_]+$", "type": "string", "description": "A simple key that consists of just a single String value. The key must match the given pattern, and additionally it must contain at least one digit or letter"}, "defaultCurrencyDisplayName": {"type": "string"}}}, "AnyUnitTypeDto": {"required": ["type"], "type": "object", "properties": {"type": {"type": "string"}}}, "ClassificationFieldSchemaDto": {"required": ["classificationTypeKey", "type"], "type": "object", "properties": {"displayName": {"type": "string"}, "type": {"type": "string"}, "classificationTypeKey": {"pattern": "^[a-zA-Z0-9.\\-_]+$", "type": "string", "description": "A simple key that consists of just a single String value. The key must match the given pattern, and additionally it must contain at least one digit or letter"}}}, "CurrencyTypeDto": {"required": ["key", "type"], "type": "object", "properties": {"type": {"type": "string"}, "key": {"pattern": "^[a-zA-Z0-9.\\-_]+$", "type": "string", "description": "A simple key that consists of just a single String value. The key must match the given pattern, and additionally it must contain at least one digit or letter"}, "displayName": {"type": "string"}}}, "DateFieldSchemaDto": {"required": ["type"], "type": "object", "properties": {"displayName": {"type": "string"}, "type": {"type": "string"}}}, "DetailValueSchemaDto": {"type": "object", "discriminator": {"propertyName": "type", "mapping": {"numeric": "#/components/schemas/NumericDetailValueSchemaDto", "lov": "#/components/schemas/LovDetailValueSchemaDto", "valuetype": "#/components/schemas/ValueTypeDetailValueSchemaDto", "materialcomposition": "#/components/schemas/MaterialCompositionDetailValueSchemaDto", "pricecomposition": "#/components/schemas/PriceCompositionDetailValueSchemaDto"}}, "oneOf": [{"$ref": "#/components/schemas/NumericDetailValueSchemaDto"}, {"$ref": "#/components/schemas/LovDetailValueSchemaDto"}, {"$ref": "#/components/schemas/ValueTypeDetailValueSchemaDto"}, {"$ref": "#/components/schemas/MaterialCompositionDetailValueSchemaDto"}, {"$ref": "#/components/schemas/PriceCompositionDetailValueSchemaDto"}]}, "DetailValueTypeDto": {"required": ["detailValueSchema", "key"], "type": "object", "properties": {"key": {"pattern": "^[a-zA-Z0-9.\\-_]+$", "type": "string", "description": "A simple key that consists of just a single String value. The key must match the given pattern, and additionally it must contain at least one digit or letter"}, "name": {"type": "string", "description": "The display name of the value type, can be null on header level"}, "index": {"type": "integer", "description": "Optional index of the value type, value type with index 0 is taken as default when creating new entries", "format": "int32"}, "detailValueSchema": {"$ref": "#/components/schemas/FieldSchemaDto"}}}, "DimensionTypeDto": {"required": ["defaultUnitKey", "key", "type"], "type": "object", "properties": {"type": {"type": "string"}, "key": {"pattern": "^[a-zA-Z0-9.\\-_]+$", "type": "string", "description": "A simple key that consists of just a single String value. The key must match the given pattern, and additionally it must contain at least one digit or letter"}, "displayName": {"type": "string"}, "defaultUnitKey": {"pattern": "^[a-zA-Z0-9.\\-_]+$", "type": "string", "description": "A simple key that consists of just a single String value. The key must match the given pattern, and additionally it must contain at least one digit or letter"}, "defaultUnitDisplayName": {"type": "string"}}}, "EffectivityDto": {"required": ["columnVisibleByDefault", "filterVisibleByDefault", "index"], "type": "object", "properties": {"unitKey": {"pattern": "^[a-zA-Z0-9.\\-_]+$", "type": "string", "description": "A simple key that consists of just a single String value. The key must match the given pattern, and additionally it must contain at least one digit or letter"}, "columnVisibleByDefault": {"type": "boolean"}, "filterVisibleByDefault": {"type": "boolean"}, "index": {"type": "integer", "format": "int32"}}}, "FieldSchemaDto": {"type": "object", "discriminator": {"propertyName": "type", "mapping": {"numeric": "#/components/schemas/NumericFieldSchemaDto", "lov": "#/components/schemas/LovFieldSchemaDto", "date": "#/components/schemas/DateFieldSchemaDto", "classification": "#/components/schemas/ClassificationFieldSchemaDto", "text": "#/components/schemas/TextFieldSchemaDto", "materialcomposition": "#/components/schemas/MaterialCompositionDetailValueFieldSchemaDto", "pricecomposition": "#/components/schemas/PriceCompositionDetailValueFieldSchemaDto"}}, "oneOf": [{"$ref": "#/components/schemas/NumericFieldSchemaDto"}, {"$ref": "#/components/schemas/LovFieldSchemaDto"}, {"$ref": "#/components/schemas/DateFieldSchemaDto"}, {"$ref": "#/components/schemas/ClassificationFieldSchemaDto"}, {"$ref": "#/components/schemas/TextFieldSchemaDto"}, {"$ref": "#/components/schemas/MaterialCompositionDetailValueFieldSchemaDto"}, {"$ref": "#/components/schemas/PriceCompositionDetailValueFieldSchemaDto"}]}, "HeaderKeyType": {"type": "string", "enum": ["header.key.simple", "header.key.material"]}, "HeaderTypeClassificationSchemaDto": {"required": ["classificationTypeKey", "index", "singleSelection"], "type": "object", "properties": {"classificationTypeKey": {"pattern": "^[a-zA-Z0-9.\\-_]+$", "type": "string", "description": "A simple key that consists of just a single String value. The key must match the given pattern, and additionally it must contain at least one digit or letter"}, "index": {"type": "integer", "format": "int32"}, "singleSelection": {"type": "boolean"}}, "description": "The classification types that classify headers of this type"}, "HeaderTypeDto": {"required": ["active", "effectivities", "headerKeyType", "index", "key", "name"], "type": "object", "properties": {"key": {"pattern": "^[a-zA-Z0-9.\\-_]+$", "type": "string", "description": "A simple key that consists of just a single String value. The key must match the given pattern, and additionally it must contain at least one digit or letter"}, "name": {"type": "string"}, "headerKeyType": {"$ref": "#/components/schemas/HeaderKeyType"}, "active": {"type": "boolean"}, "index": {"type": "integer", "format": "int32"}, "effectivities": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/EffectivityDto"}}, "detailValueSchema": {"$ref": "#/components/schemas/DetailValueSchemaDto"}, "classificationTypes": {"type": "array", "description": "The classification types that classify headers of this type", "items": {"$ref": "#/components/schemas/HeaderTypeClassificationSchemaDto"}}, "enableCreateHeaderUi": {"type": "boolean"}, "headerKeyColumnName": {"type": "string"}}}, "LovDetailValueSchemaDto": {"required": ["type", "valueSchema"], "type": "object", "properties": {"type": {"type": "string"}, "valueSchema": {"$ref": "#/components/schemas/LovFieldSchemaDto"}}}, "LovFieldSchemaDto": {"required": ["lovTypeKey", "type"], "type": "object", "properties": {"displayName": {"type": "string"}, "type": {"type": "string"}, "lovTypeKey": {"pattern": "^[a-zA-Z0-9.\\-_]+$", "type": "string", "description": "A simple key that consists of just a single String value. The key must match the given pattern, and additionally it must contain at least one digit or letter"}}}, "MaterialCompositionDetailValueFieldSchemaDto": {"required": ["headerTypeKey", "type"], "type": "object", "properties": {"displayName": {"type": "string"}, "type": {"type": "string"}, "headerTypeKey": {"pattern": "^[a-zA-Z0-9.\\-_]+$", "type": "string", "description": "A simple key that consists of just a single String value. The key must match the given pattern, and additionally it must contain at least one digit or letter"}}}, "MaterialCompositionDetailValueSchemaDto": {"required": ["schema", "type"], "type": "object", "properties": {"type": {"type": "string"}, "schema": {"$ref": "#/components/schemas/MaterialCompositionDetailValueFieldSchemaDto"}}}, "NumericDetailValueSchemaDto": {"required": ["type", "valueSchema"], "type": "object", "properties": {"type": {"type": "string"}, "valueSchema": {"$ref": "#/components/schemas/NumericFieldSchemaDto"}}}, "NumericFieldSchemaDto": {"required": ["type", "unitOfMeasurement"], "type": "object", "properties": {"displayName": {"type": "string"}, "type": {"type": "string"}, "unitOfMeasurement": {"$ref": "#/components/schemas/UnitOfMeasurementTypeDto"}}}, "NumericTypeDto": {"type": "object", "discriminator": {"propertyName": "type", "mapping": {"currency": "#/components/schemas/CurrencyTypeDto", "any-currency": "#/components/schemas/AnyCurrencyTypeDto", "dimension": "#/components/schemas/DimensionTypeDto", "unit": "#/components/schemas/UnitTypeDto", "without-unit-of-measurement": "#/components/schemas/WithoutUnitOfMeasurementTypeDto", "any-unit": "#/components/schemas/AnyUnitTypeDto"}}, "oneOf": [{"$ref": "#/components/schemas/CurrencyTypeDto"}, {"$ref": "#/components/schemas/AnyCurrencyTypeDto"}, {"$ref": "#/components/schemas/DimensionTypeDto"}, {"$ref": "#/components/schemas/UnitTypeDto"}, {"$ref": "#/components/schemas/WithoutUnitOfMeasurementTypeDto"}, {"$ref": "#/components/schemas/AnyUnitTypeDto"}]}, "PriceCompositionDetailValueFieldSchemaDto": {"required": ["classificationTypeKey", "headerTypeKey", "type"], "type": "object", "properties": {"displayName": {"type": "string"}, "type": {"type": "string"}, "headerTypeKey": {"pattern": "^[a-zA-Z0-9.\\-_]+$", "type": "string", "description": "A simple key that consists of just a single String value. The key must match the given pattern, and additionally it must contain at least one digit or letter"}, "classificationTypeKey": {"pattern": "^[a-zA-Z0-9.\\-_]+$", "type": "string", "description": "A simple key that consists of just a single String value. The key must match the given pattern, and additionally it must contain at least one digit or letter"}}}, "PriceCompositionDetailValueSchemaDto": {"required": ["schema", "type"], "type": "object", "properties": {"type": {"type": "string"}, "schema": {"$ref": "#/components/schemas/PriceCompositionDetailValueFieldSchemaDto"}}}, "TextFieldSchemaDto": {"required": ["type"], "type": "object", "properties": {"displayName": {"type": "string"}, "type": {"type": "string"}}}, "UnitOfMeasurementTypeDto": {"type": "object", "properties": {"numerator": {"$ref": "#/components/schemas/NumericTypeDto"}, "denominator": {"$ref": "#/components/schemas/NumericTypeDto"}}}, "UnitTypeDto": {"required": ["key", "type"], "type": "object", "properties": {"type": {"type": "string"}, "key": {"pattern": "^[a-zA-Z0-9.\\-_]+$", "type": "string", "description": "A simple key that consists of just a single String value. The key must match the given pattern, and additionally it must contain at least one digit or letter"}, "displayName": {"type": "string"}}}, "ValueTypeDetailValueSchemaDto": {"required": ["detailValueTypeMapping", "type"], "type": "object", "properties": {"type": {"type": "string"}, "detailValueTypeMapping": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/DetailValueTypeDto"}}}}, "WithoutUnitOfMeasurementTypeDto": {"required": ["type"], "type": "object", "properties": {"type": {"type": "string"}}}, "BuiltinEffectivityDto": {"required": ["columnVisibleByDefault", "editable", "field", "fieldType", "filterVisibleByDefault", "index"], "type": "object", "properties": {"editable": {"type": "boolean"}, "columnVisibleByDefault": {"type": "boolean"}, "filterVisibleByDefault": {"type": "boolean"}, "index": {"type": "integer", "format": "int32"}, "fieldType": {"type": "string"}, "field": {"$ref": "#/components/schemas/BuiltinFieldDto"}}}, "BuiltinFieldDto": {"required": ["key", "name", "type", "valueAccessor"], "type": "object", "properties": {"key": {"pattern": "^[a-zA-Z0-9.\\-_]+$", "type": "string", "description": "A simple key that consists of just a single String value. The key must match the given pattern, and additionally it must contain at least one digit or letter"}, "type": {"$ref": "#/components/schemas/FieldType"}, "name": {"type": "string"}, "valueAccessor": {"type": "string"}, "labelAccessor": {"type": "string"}}}, "ClassificationFieldDefinitionDto": {"required": ["fieldSchema", "key", "type"], "type": "object", "properties": {"fieldSchema": {"$ref": "#/components/schemas/ClassificationFieldSchemaDto"}, "systemManaged": {"type": "boolean"}, "key": {"pattern": "^[a-zA-Z0-9.\\-_]+$", "type": "string", "description": "A simple key that consists of just a single String value. The key must match the given pattern, and additionally it must contain at least one digit or letter"}, "type": {"type": "string"}, "defaultValue": {"pattern": "^[a-zA-Z0-9.\\-_]+$", "type": "string", "description": "A simple key that consists of just a single String value. The key must match the given pattern, and additionally it must contain at least one digit or letter"}}, "description": "A field that can store a value that references a classification (typeKey references a type defined by a ClassificationTypeDto)"}, "DateFieldDefinitionDto": {"required": ["fieldSchema", "key", "type"], "type": "object", "properties": {"fieldSchema": {"$ref": "#/components/schemas/DateFieldSchemaDto"}, "systemManaged": {"type": "boolean"}, "key": {"pattern": "^[a-zA-Z0-9.\\-_]+$", "type": "string", "description": "A simple key that consists of just a single String value. The key must match the given pattern, and additionally it must contain at least one digit or letter"}, "type": {"type": "string"}, "defaultValue": {"type": "string", "format": "date"}}, "description": "A field that can store a value of type date"}, "EffectivityResponseDto": {"type": "object", "discriminator": {"propertyName": "fieldType", "mapping": {"effectivity": "#/components/schemas/FieldEffectivityDto", "builtin": "#/components/schemas/BuiltinEffectivityDto"}}, "oneOf": [{"$ref": "#/components/schemas/FieldEffectivityDto"}, {"$ref": "#/components/schemas/BuiltinEffectivityDto"}]}, "FieldDefinitionDto": {"type": "object", "description": "Abstract interface for the various field definitions", "discriminator": {"propertyName": "type", "mapping": {"date": "#/components/schemas/DateFieldDefinitionDto", "numeric": "#/components/schemas/NumericFieldDefinitionDto", "classification": "#/components/schemas/ClassificationFieldDefinitionDto", "lov": "#/components/schemas/LovFieldDefinitionDto", "text": "#/components/schemas/TextFieldDefinitionDto"}}, "oneOf": [{"$ref": "#/components/schemas/DateFieldDefinitionDto"}, {"$ref": "#/components/schemas/NumericFieldDefinitionDto"}, {"$ref": "#/components/schemas/ClassificationFieldDefinitionDto"}, {"$ref": "#/components/schemas/LovFieldDefinitionDto"}, {"$ref": "#/components/schemas/TextFieldDefinitionDto"}]}, "FieldEffectivityDto": {"required": ["columnVisibleByDefault", "editable", "field", "fieldType", "filterVisibleByDefault", "index"], "type": "object", "properties": {"editable": {"type": "boolean"}, "columnVisibleByDefault": {"type": "boolean"}, "filterVisibleByDefault": {"type": "boolean"}, "index": {"type": "integer", "format": "int32"}, "fieldType": {"type": "string"}, "field": {"$ref": "#/components/schemas/FieldDefinitionDto"}, "unitKey": {"pattern": "^[a-zA-Z0-9.\\-_]+$", "type": "string", "description": "A simple key that consists of just a single String value. The key must match the given pattern, and additionally it must contain at least one digit or letter"}, "unitDisplayName": {"type": "string"}}}, "FieldType": {"type": "string", "enum": ["lov", "classification", "numeric", "date", "text"]}, "HeaderTypeClassificationSchemaResponseDto": {"required": ["classificationTypeKey", "index", "name", "singleSelection"], "type": "object", "properties": {"classificationTypeKey": {"pattern": "^[a-zA-Z0-9.\\-_]+$", "type": "string", "description": "A simple key that consists of just a single String value. The key must match the given pattern, and additionally it must contain at least one digit or letter"}, "name": {"type": "string"}, "systemManaged": {"type": "boolean"}, "index": {"type": "integer", "format": "int32"}, "singleSelection": {"type": "boolean"}}, "description": "The classification types that classify headers of this type"}, "HeaderTypeResponseDto": {"required": ["active", "effectivities", "headerKeyColumnName", "headerKeyType", "index", "key", "name"], "type": "object", "properties": {"key": {"pattern": "^[a-zA-Z0-9.\\-_]+$", "type": "string", "description": "A simple key that consists of just a single String value. The key must match the given pattern, and additionally it must contain at least one digit or letter"}, "name": {"type": "string"}, "headerKeyType": {"$ref": "#/components/schemas/HeaderKeyType"}, "active": {"type": "boolean"}, "index": {"type": "integer", "format": "int32"}, "effectivities": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/EffectivityResponseDto"}}, "detailValueSchema": {"$ref": "#/components/schemas/DetailValueSchemaDto"}, "classificationTypes": {"type": "array", "description": "The classification types that classify headers of this type", "items": {"$ref": "#/components/schemas/HeaderTypeClassificationSchemaResponseDto"}}, "enableCreateHeaderUi": {"type": "boolean"}, "headerKeyColumnName": {"type": "string"}}}, "LovFieldDefinitionDto": {"required": ["fieldSchema", "key", "type"], "type": "object", "properties": {"fieldSchema": {"$ref": "#/components/schemas/LovFieldSchemaDto"}, "systemManaged": {"type": "boolean"}, "key": {"pattern": "^[a-zA-Z0-9.\\-_]+$", "type": "string", "description": "A simple key that consists of just a single String value. The key must match the given pattern, and additionally it must contain at least one digit or letter"}, "type": {"type": "string"}, "defaultValue": {"pattern": "^[a-zA-Z0-9.\\-_]+$", "type": "string", "description": "A simple key that consists of just a single String value. The key must match the given pattern, and additionally it must contain at least one digit or letter"}}, "description": "A field that can store a value where the value must be one of the items defined by the typeKey (LovTypeDto). Lov = List of Values"}, "NumericFieldDefinitionDto": {"required": ["fieldSchema", "key", "type"], "type": "object", "properties": {"fieldSchema": {"$ref": "#/components/schemas/NumericFieldSchemaDto"}, "systemManaged": {"type": "boolean"}, "key": {"pattern": "^[a-zA-Z0-9.\\-_]+$", "type": "string", "description": "A simple key that consists of just a single String value. The key must match the given pattern, and additionally it must contain at least one digit or letter"}, "type": {"type": "string"}}, "description": "Defines a field that can store a numeric value. A dimension (DimensionDto) must be assigned to\n    specify the dimension of the values via the property dimensionKey"}, "TextFieldDefinitionDto": {"required": ["fieldSchema", "key", "type"], "type": "object", "properties": {"fieldSchema": {"$ref": "#/components/schemas/TextFieldSchemaDto"}, "systemManaged": {"type": "boolean"}, "key": {"pattern": "^[a-zA-Z0-9.\\-_]+$", "type": "string", "description": "A simple key that consists of just a single String value. The key must match the given pattern, and additionally it must contain at least one digit or letter"}, "type": {"type": "string"}}, "description": "A field that can store a simple text value"}, "ClassificationValueDto": {"required": ["type", "value"], "type": "object", "properties": {"type": {"type": "string"}, "value": {"pattern": "^[a-zA-Z0-9.\\-_]+$", "type": "string", "description": "A simple key that consists of just a single String value. The key must match the given pattern, and additionally it must contain at least one digit or letter"}, "displayName": {"type": "string"}}}, "DateValueDto": {"required": ["type", "value"], "type": "object", "properties": {"type": {"type": "string"}, "value": {"type": "string", "format": "date"}}}, "FieldValueDto": {"type": "object", "description": "Polymorphic field value in a detail's effectivity key.\nTypes respond to field definitions and are nullable", "discriminator": {"propertyName": "type", "mapping": {"numeric": "#/components/schemas/NumericValueDto", "text": "#/components/schemas/TextValueDto", "date": "#/components/schemas/DateValueDto", "classification": "#/components/schemas/ClassificationValueDto", "lov": "#/components/schemas/LovValueDto"}}, "oneOf": [{"$ref": "#/components/schemas/NumericValueDto"}, {"$ref": "#/components/schemas/TextValueDto"}, {"$ref": "#/components/schemas/DateValueDto"}, {"$ref": "#/components/schemas/ClassificationValueDto"}, {"$ref": "#/components/schemas/LovValueDto"}]}, "HeaderDto": {"required": ["active", "headerTypeKey", "key", "name"], "type": "object", "properties": {"key": {"$ref": "#/components/schemas/HeaderNaturalKeyDto"}, "name": {"type": "string"}, "headerTypeKey": {"pattern": "^[a-zA-Z0-9.\\-_]+$", "type": "string", "description": "A simple key that consists of just a single String value. The key must match the given pattern, and additionally it must contain at least one digit or letter"}, "active": {"type": "boolean"}, "detailValueSchema": {"$ref": "#/components/schemas/DetailValueSchemaDto"}, "classifications": {"type": "object", "additionalProperties": {"type": "array", "description": "this is a map where, the key is a key of a ClassificationTypeDto, and the value is a list of ClassificationDto keys", "items": {"pattern": "^[a-zA-Z0-9.\\-_]+$", "type": "string", "description": "A simple key that consists of just a single String value. The key must match the given pattern, and additionally it must contain at least one digit or letter"}}, "description": "this is a map where, the key is a key of a ClassificationTypeDto, and the value is a list of ClassificationDto keys"}, "classificationFieldValues": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/FieldValueDto"}, "description": "this is a map where, the key is a key of a FieldDefinitionDto, and the value is the value for this classificationField"}}}, "HeaderNaturalKeyDto": {"type": "object", "discriminator": {"propertyName": "type", "mapping": {"header.key.material": "#/components/schemas/MaterialKeyDto"}}, "oneOf": [{"$ref": "#/components/schemas/MaterialKeyDto"}, {"$ref": "#/components/schemas/SimpleKeyDto"}]}, "LovValueDto": {"required": ["type", "value"], "type": "object", "properties": {"type": {"type": "string"}, "value": {"pattern": "^[a-zA-Z0-9.\\-_]+$", "type": "string", "description": "A simple key that consists of just a single String value. The key must match the given pattern, and additionally it must contain at least one digit or letter"}, "displayName": {"type": "string"}}}, "MaterialKeyDto": {"required": ["number", "revision", "type"], "type": "object", "properties": {"type": {"type": "string"}, "number": {"type": "string"}, "revision": {"type": "string"}}}, "SimpleKeyDto": {"pattern": "^[a-zA-Z0-9.\\-_]+$", "type": "string", "description": "A simple key that consists of just a single String value. The key must match the given pattern, and additionally it must contain at least one digit or letter"}, "TextValueDto": {"required": ["type", "value"], "type": "object", "properties": {"type": {"type": "string"}, "value": {"type": "string"}}}, "DetailDto": {"required": ["active", "effectivities", "<PERSON><PERSON><PERSON>", "value"], "type": "object", "properties": {"effectivities": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/FieldValueDto"}, "description": "A map of every effectivity of the header type to a (nullable) value"}, "headerKey": {"$ref": "#/components/schemas/HeaderNaturalKeyDto"}, "value": {"$ref": "#/components/schemas/DetailValueDto"}, "active": {"type": "boolean"}, "headerDisplayName": {"type": "string"}, "modifier": {"type": "string"}, "modificationDate": {"type": "string", "format": "date"}, "detailValueTypeKey": {"pattern": "^[a-zA-Z0-9.\\-_]+$", "type": "string", "description": "A simple key that consists of just a single String value. The key must match the given pattern, and additionally it must contain at least one digit or letter"}, "detailValueTypeDisplayName": {"type": "string"}}, "description": "A header detail.\n\nThe key represents a unique effectivity-value mapping for the header.\nDetails cannot be updated, instead a new version is created. Some requests can then be \"locked\" to a given version.\n"}, "DetailReferenceDto": {"required": ["effectivities", "<PERSON><PERSON><PERSON>"], "type": "object", "properties": {"headerKey": {"$ref": "#/components/schemas/HeaderNaturalKeyDto"}, "effectivities": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/FieldValueDto"}}, "detailValueTypeKey": {"pattern": "^[a-zA-Z0-9.\\-_]+$", "type": "string", "description": "A simple key that consists of just a single String value. The key must match the given pattern, and additionally it must contain at least one digit or letter"}}}, "DetailValueDto": {"type": "object", "description": "A polymorphic value contained in a Detail", "discriminator": {"propertyName": "type", "mapping": {"numeric": "#/components/schemas/NumericDetailValueDto", "lov": "#/components/schemas/LovDetailValueDto", "materialcomposition": "#/components/schemas/MaterialCompositionDetailValueDto", "pricecomposition": "#/components/schemas/PriceCompositionDetailValueDto"}}, "oneOf": [{"$ref": "#/components/schemas/NumericDetailValueDto"}, {"$ref": "#/components/schemas/LovDetailValueDto"}, {"$ref": "#/components/schemas/MaterialCompositionDetailValueDto"}, {"$ref": "#/components/schemas/PriceCompositionDetailValueDto"}]}, "LovDetailValueDto": {"required": ["key", "type"], "type": "object", "properties": {"type": {"type": "string"}, "key": {"pattern": "^[a-zA-Z0-9.\\-_]+$", "type": "string", "description": "A simple key that consists of just a single String value. The key must match the given pattern, and additionally it must contain at least one digit or letter"}, "name": {"type": "string", "description": "the name of the Lov Entry; ignored on POST/PUT"}, "lovTypeKey": {"pattern": "^[a-zA-Z0-9.\\-_]+$", "type": "string", "description": "A simple key that consists of just a single String value. The key must match the given pattern, and additionally it must contain at least one digit or letter"}}}, "MassType": {"type": "string", "enum": ["Manual", "NetMass", "GrossMass", "ScrapMass", "TemplateMass"]}, "MaterialComponentEntryDto": {"required": ["fraction", "<PERSON><PERSON><PERSON>", "quantity"], "type": "object", "properties": {"fraction": {"$ref": "#/components/schemas/NumericValueDto"}, "quantity": {"$ref": "#/components/schemas/NumericValueDto"}, "headerKey": {"pattern": "^[a-zA-Z0-9.\\-_]+$", "type": "string", "description": "A simple key that consists of just a single String value. The key must match the given pattern, and additionally it must contain at least one digit or letter"}}, "description": "the list of material components"}, "MaterialCompositionDetailValueDto": {"required": ["components", "type"], "type": "object", "properties": {"type": {"type": "string"}, "components": {"type": "array", "description": "the list of material components", "items": {"$ref": "#/components/schemas/MaterialComponentEntryDto"}}}}, "NumericDetailValueDto": {"required": ["type", "value"], "type": "object", "properties": {"type": {"type": "string"}, "value": {"type": "number", "format": "double"}, "numerator": {"$ref": "#/components/schemas/MeasurementDto"}, "denominator": {"$ref": "#/components/schemas/MeasurementDto"}, "valueInBaseSiUnit": {"type": "number", "format": "double"}}}, "PriceComponentEntryDto": {"required": ["<PERSON><PERSON>ey", "massType", "templateMass"], "type": "object", "properties": {"massType": {"$ref": "#/components/schemas/MassType"}, "templateMass": {"$ref": "#/components/schemas/NumericValueDto"}, "classificationKey": {"pattern": "^[a-zA-Z0-9.\\-_]+$", "type": "string", "description": "A simple key that consists of just a single String value. The key must match the given pattern, and additionally it must contain at least one digit or letter"}}, "description": "the list of price components"}, "PriceCompositionDetailValueDto": {"required": ["components", "type"], "type": "object", "properties": {"type": {"type": "string"}, "components": {"type": "array", "description": "the list of price components", "items": {"$ref": "#/components/schemas/PriceComponentEntryDto"}}}}, "ReplaceDetailRequestDto": {"required": ["newDetail", "setInactive"], "type": "object", "properties": {"setInactive": {"$ref": "#/components/schemas/DetailReferenceDto"}, "newDetail": {"$ref": "#/components/schemas/DetailDto"}}}, "DimensionDto": {"required": ["baseUnitKey", "key", "name"], "type": "object", "properties": {"key": {"pattern": "^[a-zA-Z0-9.\\-_]+$", "type": "string", "description": "A simple key that consists of just a single String value. The key must match the given pattern, and additionally it must contain at least one digit or letter"}, "name": {"type": "string"}, "baseUnitKey": {"pattern": "^[a-zA-Z0-9.\\-_]+$", "type": "string", "description": "A simple key that consists of just a single String value. The key must match the given pattern, and additionally it must contain at least one digit or letter"}, "units": {"type": "array", "items": {"$ref": "#/components/schemas/DimensionUnitDto"}}}, "description": "Defines the dimension of a numeric type. Each dimension (DimensionDto) consists of multiple units (UnitDto)"}, "DimensionUnitDto": {"required": ["factor", "key", "name"], "type": "object", "properties": {"key": {"pattern": "^[a-zA-Z0-9.\\-_]+$", "type": "string", "description": "A simple key that consists of just a single String value. The key must match the given pattern, and additionally it must contain at least one digit or letter"}, "name": {"type": "string"}, "factor": {"minimum": 0, "exclusiveMinimum": true, "type": "number", "format": "double"}}}, "CurrencyDto": {"required": ["key", "name", "symbol"], "type": "object", "properties": {"key": {"pattern": "^[a-zA-Z0-9.\\-_]+$", "type": "string", "description": "A simple key that consists of just a single String value. The key must match the given pattern, and additionally it must contain at least one digit or letter"}, "name": {"type": "string"}, "symbol": {"type": "string"}}, "description": "Defines the currency of a numeric type."}, "ClassificationTypeDto": {"required": ["key", "name"], "type": "object", "properties": {"key": {"pattern": "^[a-zA-Z0-9.\\-_]+$", "type": "string", "description": "A simple key that consists of just a single String value. The key must match the given pattern, and additionally it must contain at least one digit or letter"}, "name": {"type": "string"}, "systemManaged": {"type": "boolean"}}, "description": "Defines a classification type. Each type consists of multiple classification (ClassificationDto) that form the classification tree.\n    There must be at most one root (ClassificationDto with (ClassificationDto.parentClassificationKey) null) and there must not be any cycles in the tree structure."}, "ClassificationFieldRequestDto": {"required": ["columnVisibleByDefault", "filterVisibleByDefault", "index"], "type": "object", "properties": {"index": {"type": "integer", "format": "int32"}, "columnVisibleByDefault": {"type": "boolean"}, "filterVisibleByDefault": {"type": "boolean"}}}, "ClassificationFieldResponseDto": {"required": ["columnVisibleByDefault", "field", "filterVisibleByDefault", "index"], "type": "object", "properties": {"index": {"type": "integer", "format": "int32"}, "columnVisibleByDefault": {"type": "boolean"}, "filterVisibleByDefault": {"type": "boolean"}, "field": {"$ref": "#/components/schemas/FieldDefinitionDto"}}}, "ClassificationDto": {"required": ["classificationTypeKey", "key", "name"], "type": "object", "properties": {"key": {"pattern": "^[a-zA-Z0-9.\\-_]+$", "type": "string", "description": "A simple key that consists of just a single String value. The key must match the given pattern, and additionally it must contain at least one digit or letter"}, "name": {"type": "string"}, "classificationTypeKey": {"pattern": "^[a-zA-Z0-9.\\-_]+$", "type": "string", "description": "A simple key that consists of just a single String value. The key must match the given pattern, and additionally it must contain at least one digit or letter"}, "parentClassificationKey": {"pattern": "^[a-zA-Z0-9.\\-_]+$", "type": "string", "description": "A simple key that consists of just a single String value. The key must match the given pattern, and additionally it must contain at least one digit or letter"}, "systemManaged": {"type": "boolean"}}, "description": "Defines a single Classification of a ClassificationType"}, "BulkErrorDto": {"required": ["message", "userErrorCode", "userParameters"], "type": "object", "properties": {"userErrorCode": {"type": "string"}, "userParameters": {"type": "array", "items": {"type": "object"}}, "message": {"type": "string"}}}, "HeaderBulkResponseDto": {"type": "object", "description": "Polymorphic response type for bulk header creation.\n\nOn success (2xx), only HeaderDtos will be returned.\nOn error (4xx), the returned list have the same order as the input, with each element either being a HeaderDto or a HeaderBulkResponseErrorDto, which contains the dto and all detected errors.", "discriminator": {"propertyName": "type", "mapping": {"success": "#/components/schemas/HeaderBulkResponseSuccessDto", "error": "#/components/schemas/HeaderBulkResponseErrorDto"}}, "oneOf": [{"$ref": "#/components/schemas/HeaderBulkResponseSuccessDto"}, {"$ref": "#/components/schemas/HeaderBulkResponseErrorDto"}]}, "HeaderBulkResponseErrorDto": {"required": ["dto", "errors", "type"], "type": "object", "properties": {"type": {"type": "string"}, "dto": {"$ref": "#/components/schemas/HeaderDto"}, "errors": {"type": "array", "items": {"$ref": "#/components/schemas/BulkErrorDto"}}}, "description": "A malformed dto with all detected errors"}, "HeaderBulkResponseSuccessDto": {"required": ["active", "classificationFieldValues", "classifications", "headerTypeKey", "key", "name", "type"], "type": "object", "properties": {"type": {"type": "string"}, "key": {"$ref": "#/components/schemas/HeaderNaturalKeyDto"}, "name": {"type": "string"}, "headerTypeKey": {"pattern": "^[a-zA-Z0-9.\\-_]+$", "type": "string", "description": "A simple key that consists of just a single String value. The key must match the given pattern, and additionally it must contain at least one digit or letter"}, "active": {"type": "boolean"}, "detailValueSchema": {"$ref": "#/components/schemas/DetailValueSchemaDto"}, "classifications": {"type": "object", "additionalProperties": {"type": "array", "items": {"pattern": "^[a-zA-Z0-9.\\-_]+$", "type": "string", "description": "A simple key that consists of just a single String value. The key must match the given pattern, and additionally it must contain at least one digit or letter"}}}, "classificationFieldValues": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/FieldValueDto"}}}}, "DetailBulkResponseDto": {"type": "object", "description": "Polymorphic response type for bulk detail creation.\n\nOn success (2xx), only DetailDtos will be returned.\nOn error (4xx), the returned list have the same order as the input, with each element either being a DetailDto or a DetailBulkResponseErrorDto, which contains the dto and all detected errors.", "discriminator": {"propertyName": "type", "mapping": {"success": "#/components/schemas/DetailBulkResponseSuccessDto", "error": "#/components/schemas/DetailBulkResponseErrorDto"}}, "oneOf": [{"$ref": "#/components/schemas/DetailBulkResponseSuccessDto"}, {"$ref": "#/components/schemas/DetailBulkResponseErrorDto"}]}, "DetailBulkResponseErrorDto": {"required": ["dto", "errors", "type"], "type": "object", "properties": {"type": {"type": "string"}, "dto": {"$ref": "#/components/schemas/DetailDto"}, "errors": {"type": "array", "items": {"$ref": "#/components/schemas/BulkErrorDto"}}}, "description": "A malformed dto with all detected errors"}, "DetailBulkResponseSuccessDto": {"required": ["active", "effectivities", "<PERSON><PERSON><PERSON>", "type", "value"], "type": "object", "properties": {"type": {"type": "string"}, "effectivities": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/FieldValueDto"}}, "headerKey": {"$ref": "#/components/schemas/HeaderNaturalKeyDto"}, "value": {"$ref": "#/components/schemas/DetailValueDto"}, "active": {"type": "boolean"}, "headerDisplayName": {"type": "string"}, "modifier": {"type": "string"}, "modificationDate": {"type": "string", "format": "date"}, "detailValueTypeKey": {"pattern": "^[a-zA-Z0-9.\\-_]+$", "type": "string", "description": "A simple key that consists of just a single String value. The key must match the given pattern, and additionally it must contain at least one digit or letter"}, "detailValueTypeDisplayName": {"type": "string"}}}, "BuiltinLovFilterDto": {"required": ["equals", "type"], "type": "object", "properties": {"type": {"type": "string"}, "equals": {"$ref": "#/components/schemas/HeaderNaturalKeyDto"}}}, "ClassificationFilterDto": {"required": ["equals", "includeDescendants", "type"], "type": "object", "properties": {"type": {"type": "string"}, "equals": {"pattern": "^[a-zA-Z0-9.\\-_]+$", "type": "string", "description": "A simple key that consists of just a single String value. The key must match the given pattern, and additionally it must contain at least one digit or letter"}, "includeDescendants": {"type": "boolean"}}}, "DateFilterDto": {"required": ["type"], "type": "object", "properties": {"type": {"type": "string"}, "smallerEquals": {"type": "string", "format": "date"}, "greaterEquals": {"type": "string", "format": "date"}, "equals": {"type": "string", "format": "date"}}}, "DetailQueryDto": {"required": ["classificationFieldFilters", "classificationFilters", "filters", "showInactive"], "type": "object", "properties": {"filters": {"type": "object", "additionalProperties": {"type": "array", "description": "A map from field key to a list of filters. The filters for one field are joined with OR, where as the filters across fields are joined with AND. For example, `(overhead method = A OR overhead method = B) AND (x > 3 OR x = 1)", "items": {"$ref": "#/components/schemas/SingleFilterDto"}}, "description": "A map from field key to a list of filters. The filters for one field are joined with OR, where as the filters across fields are joined with AND. For example, `(overhead method = A OR overhead method = B) AND (x > 3 OR x = 1)"}, "classificationFieldFilters": {"type": "object", "additionalProperties": {"type": "array", "items": {"$ref": "#/components/schemas/SingleFilterDto"}}}, "classificationFilters": {"type": "object", "additionalProperties": {"type": "array", "items": {"$ref": "#/components/schemas/HeaderClassificationFilterDto"}}}, "showStateOf": {"type": "string", "description": "Return masterdata as valid on this date/time. Format must be  yyyy-MM-dd'T'HH:mm:ss.SSSXXX, e.g. 2000-10-31T01:30:00.000Z", "format": "date-time"}, "showInactive": {"type": "boolean"}, "sortOrder": {"type": "array", "items": {"$ref": "#/components/schemas/SortSegmentDto"}}}}, "HeaderClassificationFilterDto": {"type": "object", "description": "Abstract interface for the various filter operations allowed for header classifications", "discriminator": {"propertyName": "type", "mapping": {"classification": "#/components/schemas/ClassificationFilterDto", "null": "#/components/schemas/NullFilterDto"}}, "oneOf": [{"$ref": "#/components/schemas/ClassificationFilterDto"}, {"$ref": "#/components/schemas/NullFilterDto"}]}, "LovFilterDto": {"required": ["equals", "type"], "type": "object", "properties": {"type": {"type": "string"}, "equals": {"pattern": "^[a-zA-Z0-9.\\-_]+$", "type": "string", "description": "A simple key that consists of just a single String value. The key must match the given pattern, and additionally it must contain at least one digit or letter"}}}, "NullFilterDto": {"required": ["type"], "type": "object", "properties": {"type": {"type": "string"}}}, "NumericFilterDto": {"required": ["type"], "type": "object", "properties": {"type": {"type": "string"}, "smallerEquals": {"$ref": "#/components/schemas/NumericValueDto"}, "greaterEquals": {"$ref": "#/components/schemas/NumericValueDto"}, "equals": {"$ref": "#/components/schemas/NumericValueDto"}}}, "SingleFilterDto": {"type": "object", "description": "Abstract interface for the various filter operations", "discriminator": {"propertyName": "type", "mapping": {"numeric": "#/components/schemas/NumericFilterDto", "lov": "#/components/schemas/LovFilterDto", "date": "#/components/schemas/DateFilterDto", "classification": "#/components/schemas/ClassificationFilterDto", "builtinlov": "#/components/schemas/BuiltinLovFilterDto", "null": "#/components/schemas/NullFilterDto"}}, "oneOf": [{"$ref": "#/components/schemas/NumericFilterDto"}, {"$ref": "#/components/schemas/LovFilterDto"}, {"$ref": "#/components/schemas/DateFilterDto"}, {"$ref": "#/components/schemas/ClassificationFilterDto"}, {"$ref": "#/components/schemas/BuiltinLovFilterDto"}, {"$ref": "#/components/schemas/NullFilterDto"}]}, "SortDirection": {"type": "string", "enum": ["ASC", "DESC"]}, "SortSegmentDto": {"required": ["direction", "<PERSON><PERSON><PERSON>"], "type": "object", "properties": {"direction": {"$ref": "#/components/schemas/SortDirection"}, "fieldKey": {"pattern": "^[a-zA-Z0-9.\\-_]+$", "type": "string", "description": "A simple key that consists of just a single String value. The key must match the given pattern, and additionally it must contain at least one digit or letter"}}}, "ClassificationBaseInfoDto": {"required": ["classificationTypeKey", "key", "name"], "type": "object", "properties": {"key": {"pattern": "^[a-zA-Z0-9.\\-_]+$", "type": "string", "description": "A simple key that consists of just a single String value. The key must match the given pattern, and additionally it must contain at least one digit or letter"}, "name": {"type": "string"}, "classificationTypeKey": {"pattern": "^[a-zA-Z0-9.\\-_]+$", "type": "string", "description": "A simple key that consists of just a single String value. The key must match the given pattern, and additionally it must contain at least one digit or letter"}}, "description": "Defines a single Classification of a ClassificationType - DTO that omits the parent, and systemManaged property"}, "DetailQueryResponseDto": {"required": ["content", "hasNext", "maxCountTruncated", "number", "numberOfElements", "size", "totalElements", "totalPages"], "type": "object", "properties": {"content": {"type": "array", "items": {"$ref": "#/components/schemas/HeaderAndDetailDto"}}, "number": {"type": "integer", "description": "Returns the number of the current page. Is always non-negative", "format": "int32"}, "size": {"type": "integer", "description": "Returns the size of the Slice.", "format": "int32"}, "totalElements": {"type": "integer", "description": "Returns the total amount of elements.", "format": "int32"}, "maxCountTruncated": {"type": "boolean", "description": "If this is true, there exist more elements then stated in totalElements but an exact count is not possible."}, "totalPages": {"type": "integer", "description": "Returns the number of total pages.", "format": "int32"}, "numberOfElements": {"type": "integer", "description": "Returns the number of elements currently on this Page.", "format": "int32"}, "hasNext": {"type": "boolean", "description": "Returns if there is a next Slice."}}}, "HeaderAndDetailDto": {"required": ["headerDto"], "type": "object", "properties": {"headerDto": {"$ref": "#/components/schemas/HeaderDetailQueryResponseDto"}, "detailDto": {"$ref": "#/components/schemas/DetailDto"}}}, "HeaderDetailQueryResponseDto": {"required": ["active", "headerTypeKey", "key", "name"], "type": "object", "properties": {"key": {"$ref": "#/components/schemas/HeaderNaturalKeyDto"}, "name": {"type": "string"}, "headerTypeKey": {"pattern": "^[a-zA-Z0-9.\\-_]+$", "type": "string", "description": "A simple key that consists of just a single String value. The key must match the given pattern, and additionally it must contain at least one digit or letter"}, "active": {"type": "boolean"}, "detailValueSchema": {"$ref": "#/components/schemas/DetailValueSchemaDto"}, "classifications": {"type": "object", "additionalProperties": {"type": "array", "description": "this is a map where, the key is a key of a ClassificationTypeDto, and the value is a list of ClassificationDto", "items": {"$ref": "#/components/schemas/ClassificationBaseInfoDto"}}, "description": "this is a map where, the key is a key of a ClassificationTypeDto, and the value is a list of ClassificationDto"}, "classificationFieldValues": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/FieldValueDto"}, "description": "this is a map where, the key is a key of a FieldDefinitionDto, and the value is the value for this classificationField"}}}, "Effectivity": {"required": ["fieldDefinitionKey", "value"], "type": "object", "properties": {"fieldDefinitionKey": {"pattern": "^[a-zA-Z0-9.\\-_]+$", "type": "string", "description": "A simple key that consists of just a single String value. The key must match the given pattern, and additionally it must contain at least one digit or letter"}, "value": {"$ref": "#/components/schemas/FieldValueDto"}}}, "MasterdataLookupRequest": {"required": ["effectivities", "header<PERSON><PERSON><PERSON>", "headerTypeKey", "strategy<PERSON>ey"], "type": "object", "properties": {"strategyKey": {"pattern": "^[a-zA-Z0-9.\\-_]+$", "type": "string", "description": "A simple key that consists of just a single String value. The key must match the given pattern, and additionally it must contain at least one digit or letter"}, "headerTypeKey": {"pattern": "^[a-zA-Z0-9.\\-_]+$", "type": "string", "description": "A simple key that consists of just a single String value. The key must match the given pattern, and additionally it must contain at least one digit or letter"}, "headerKeys": {"type": "array", "items": {"$ref": "#/components/schemas/HeaderNaturalKeyDto"}}, "effectivities": {"type": "array", "items": {"$ref": "#/components/schemas/Effectivity"}}, "timestampEpochMillis": {"type": "integer", "format": "int64"}}}, "MasterdataLookupResponse": {"required": ["headersWithoutItems", "items"], "type": "object", "properties": {"items": {"type": "array", "items": {"$ref": "#/components/schemas/DetailDto"}}, "headersWithoutItems": {"type": "array", "items": {"$ref": "#/components/schemas/HeaderDto"}}}}, "GetLatestVersionResponseDto": {"type": "object", "properties": {"timestampEpochMillis": {"type": "integer", "format": "int64"}}}, "BuiltinLovResponseDto": {"required": ["key", "name"], "type": "object", "properties": {"key": {"$ref": "#/components/schemas/HeaderNaturalKeyDto"}, "name": {"type": "string"}}}, "HeaderTypeListResponseDto": {"required": ["active", "headerKeyType", "index", "key", "name"], "type": "object", "properties": {"key": {"pattern": "^[a-zA-Z0-9.\\-_]+$", "type": "string", "description": "A simple key that consists of just a single String value. The key must match the given pattern, and additionally it must contain at least one digit or letter"}, "name": {"type": "string"}, "headerKeyType": {"$ref": "#/components/schemas/HeaderKeyType"}, "active": {"type": "boolean"}, "index": {"type": "integer", "format": "int32"}}}, "ClassificationFieldsInfoDto": {"required": ["classifications", "fields"], "type": "object", "properties": {"classifications": {"type": "array", "description": "Classifications and associated field keys", "items": {"$ref": "#/components/schemas/ClassificationWithFieldsDto"}}, "fields": {"type": "array", "description": "FieldDefinitions of all referenced fields, and default column/filter visibility", "items": {"$ref": "#/components/schemas/FieldWithVisibilityDto"}}}, "description": "This DTO transports information about multiple classifications and their associated classification fields"}, "ClassificationWithFieldsDto": {"required": ["classification", "classificationAncestors", "fields"], "type": "object", "properties": {"classification": {"$ref": "#/components/schemas/ClassificationBaseInfoDto"}, "classificationAncestors": {"type": "array", "description": "the ancestors of this classification starting at the direct parent (first element). the last element of this list is the root of the classification type", "items": {"$ref": "#/components/schemas/ClassificationBaseInfoDto"}}, "fields": {"type": "array", "description": "field keys for this classification and all ancestors of this classification (sorted by classification field index)", "items": {"pattern": "^[a-zA-Z0-9.\\-_]+$", "type": "string", "description": "A simple key that consists of just a single String value. The key must match the given pattern, and additionally it must contain at least one digit or letter"}}}, "description": "one Classification + a list of Classification Field Keys for this classification"}, "FieldWithVisibilityDto": {"required": ["columnVisibleByDefault", "field", "filterVisibleByDefault"], "type": "object", "properties": {"field": {"$ref": "#/components/schemas/FieldDefinitionDto"}, "columnVisibleByDefault": {"type": "boolean"}, "filterVisibleByDefault": {"type": "boolean"}}, "description": "FieldDefinitions of all referenced fields, and default column/filter visibility"}}, "examples": {"SingletonMap_raw_material_fields": {"summary": "Raw material fields", "value": "{\"material-group\":{\"index\":0,\"columnVisibleByDefault\":true,\"filterVisibleByDefault\":false}}"}, "LinkedHashMap_bar_fields": {"summary": "Bar fields", "description": "Bar also inherits the fields from raw material.", "value": "{\"bar-length\":{\"index\":10,\"columnVisibleByDefault\":true,\"filterVisibleByDefault\":true},\"surface-finish\":{\"index\":20,\"columnVisibleByDefault\":false,\"filterVisibleByDefault\":false}}"}, "LinkedHashMap_electronic_component_fields": {"summary": "Electronic component fields", "value": "{\"manufacturer\":{\"index\":0,\"columnVisibleByDefault\":false,\"filterVisibleByDefault\":true},\"mpn\":{\"index\":10,\"columnVisibleByDefault\":true,\"filterVisibleByDefault\":true},\"mounting-type\":{\"index\":30,\"columnVisibleByDefault\":true,\"filterVisibleByDefault\":true}}"}, "HeaderType_materials_-_header_classifications": {"summary": "Materials - Header Classifications", "value": "{\"key\":\"material\",\"name\":\"Materials - Header Classifications\",\"headerKeyType\":\"header.key.simple\",\"active\":true,\"index\":0,\"effectivities\":{},\"detailValueSchema\":{\"type\":\"numeric\",\"valueSchema\":{\"type\":\"numeric\",\"unitOfMeasurement\":{\"numerator\":{\"type\":\"currency\",\"key\":\"EUR\"},\"denominator\":{\"type\":\"unit\",\"key\":\"pcs\"}}}},\"classificationTypes\":[{\"classificationTypeKey\":\"material\",\"index\":2,\"singleSelection\":false}]}"}, "Header_electronic_component": {"summary": "Electronic component", "value": "{\"key\":\"TLE5309D\",\"name\":\"IC TLE5309D PG-TDSO-16-2\",\"headerTypeKey\":\"material\",\"active\":true,\"classifications\":{\"material\":[\"material-elco\"]},\"classificationFieldValues\":{\"manufacturer\":{\"type\":\"text\",\"value\":\"INFINEON TECHNOLOGIES\"},\"mpn\":{\"type\":\"text\",\"value\":\"TLE5309D E1211\"},\"mounting-type\":{\"type\":\"lov\",\"value\":\"SMD\"}}}"}, "Header_bar_material": {"summary": "Bar material", "value": "{\"key\":\"1.6657\",\"name\":\"15NiCr13\",\"headerTypeKey\":\"material\",\"active\":true,\"classifications\":{\"material\":[\"material-bar\"]},\"classificationFieldValues\":{\"material-group\":{\"type\":\"lov\",\"value\":\"P7\"},\"bar-length\":{\"type\":\"numeric\",\"value\":3.2,\"numerator\":{\"type\":\"unit\",\"key\":\"m\"}},\"surface-finish\":{\"type\":\"numeric\",\"value\":25.0,\"numerator\":{\"type\":\"unit\",\"key\":\"rz\"}},\"pre-heat-treatment\":{\"type\":\"lov\",\"value\":\"annealed\"}}}"}, "Dimension_distance": {"summary": "Distance", "value": "{\"key\":\"distance\",\"name\":\"Distance\",\"baseUnitKey\":\"meter\"}"}, "Unit_meter": {"summary": "<PERSON>er", "value": "{\"key\":\"meter\",\"dimensionKey\":\"distance\",\"name\":\"m\",\"factor\":1.0}"}, "Unit_kilometer": {"summary": "Kilometer", "value": "{\"key\":\"kilometer\",\"dimensionKey\":\"distance\",\"name\":\"km\",\"factor\":1000.0}"}, "distance_creation": {"summary": "Distance with units", "value": "{\"key\":\"distance\",\"name\":\"Distance\",\"baseUnitKey\":\"meter\",\"units\":[{\"key\":\"meter\",\"name\":\"m\",\"factor\":1.0},{\"key\":\"kilometer\",\"name\":\"km\",\"factor\":1000.0}]}"}, "LovType_transport_type": {"summary": "Transport type", "value": "{\"key\":\"transport-type\",\"name\":\"Transport type\"}"}, "LovEntry_rail": {"summary": "Rail", "value": "{\"key\":\"rail\",\"lovTypeKey\":\"transport-type\",\"name\":\"Rail\"}"}, "LovEntry_sea": {"summary": "Sea", "value": "{\"key\":\"sea\",\"lovTypeKey\":\"transport-type\",\"name\":\"Sea\"}"}, "ClassificationType_location": {"summary": "Location", "value": "{\"key\":\"location\",\"name\":\"Location\"}"}, "Classification_world_-_root": {"summary": "World - root", "value": "{\"key\":\"world\",\"name\":\"World\",\"classificationTypeKey\":\"location\"}"}, "Classification_emea": {"summary": "EMEA", "value": "{\"key\":\"emea\",\"name\":\"EMEA\",\"classificationTypeKey\":\"location\",\"parentClassificationKey\":\"world\"}"}, "Classification_austria": {"summary": "Austria", "value": "{\"key\":\"austria\",\"name\":\"Austria\",\"classificationTypeKey\":\"location\",\"parentClassificationKey\":\"emea\"}"}, "Classification_vienna": {"summary": "Vienna", "value": "{\"key\":\"vienna\",\"name\":\"Vienna\",\"classificationTypeKey\":\"location\",\"parentClassificationKey\":\"austria\"}"}, "Classification_lower_austria": {"summary": "Lower austria", "value": "{\"key\":\"lower-austria\",\"name\":\"Lower austria\",\"classificationTypeKey\":\"location\",\"parentClassificationKey\":\"austria\"}"}, "Classification_germany": {"summary": "Germany", "value": "{\"key\":\"germany\",\"name\":\"Germany\",\"classificationTypeKey\":\"location\",\"parentClassificationKey\":\"emea\"}"}, "Classification_berlin": {"summary": "Berlin", "value": "{\"key\":\"berlin\",\"name\":\"Berlin\",\"classificationTypeKey\":\"location\",\"parentClassificationKey\":\"germany\"}"}, "TextFieldDefinition_part_name": {"summary": "Part name", "value": "{\"type\":\"text\",\"key\":\"part-name\",\"fieldSchema\":{\"type\":\"text\",\"displayName\":\"Part name\"}}"}, "NumericFieldDefinition_part_length": {"summary": "Part length", "value": "{\"type\":\"numeric\",\"key\":\"part-length\",\"fieldSchema\":{\"type\":\"numeric\",\"unitOfMeasurement\":{\"numerator\":{\"type\":\"dimension\",\"key\":\"distance\",\"defaultUnitKey\":\"meter\"},\"denominator\":{\"type\":\"without-unit-of-measurement\"}},\"displayName\":\"Part length\"}}"}, "DateFieldDefinition_calculation_date": {"summary": "Calculation date", "value": "{\"type\":\"date\",\"key\":\"calculation-date\",\"fieldSchema\":{\"type\":\"date\",\"displayName\":\"Calculation date\"}}"}, "LovFieldDefinition_transport_type": {"summary": "Transport type", "value": "{\"type\":\"lov\",\"key\":\"transport-type\",\"fieldSchema\":{\"type\":\"lov\",\"lovTypeKey\":\"transport-type\",\"displayName\":\"Transport type\"}}"}, "ClassificationFieldDefinition_location": {"summary": "Location", "value": "{\"type\":\"classification\",\"key\":\"location\",\"fieldSchema\":{\"type\":\"classification\",\"classificationTypeKey\":\"location\",\"displayName\":\"Location\"}}"}, "Detail_overhead_rate": {"summary": "Overhead rate", "value": "{\"effectivities\":{\"overheadMethod\":{\"type\":\"lov\",\"value\":\"BUILD_TO_PRINT_AUTO\"},\"validFromVolume\":{\"type\":\"numeric\",\"value\":1000.0,\"numerator\":{\"type\":\"unit\",\"key\":\"pcs\"}}},\"headerKey\":\"Tset.Profit.PurchasePart\",\"value\":{\"type\":\"numeric\",\"value\":4.0,\"numerator\":{\"type\":\"unit\",\"key\":\"percentage\"}},\"active\":true}"}, "Detail_lov_detail_-_high_wage_country": {"summary": "LOV Detail - High Wage Country", "value": "{\"effectivities\":{\"region\":{\"type\":\"classification\",\"value\":\"vienna\"}},\"headerKey\":\"high-wage-country\",\"value\":{\"type\":\"lov\",\"key\":\"true\"},\"active\":true}"}, "Detail_detail_-_valuetype": {"summary": "Detail - ValueType", "value": "{\"effectivities\":{\"region\":{\"type\":\"classification\",\"value\":\"vienna\"}},\"headerKey\":\"high-wage-country\",\"value\":{\"type\":\"lov\",\"key\":\"true\"},\"active\":true,\"detailValueTypeKey\":\"price\"}"}, "Detail_detail_-_price_composition": {"summary": "Detail - Price composition", "value": "{\"effectivities\":{\"region\":{\"type\":\"classification\",\"value\":\"vienna\"}},\"headerKey\":\"0815\",\"value\":{\"type\":\"pricecomposition\",\"components\":[{\"massType\":\"GrossMass\",\"templateMass\":{\"type\":\"numeric\",\"value\":1000.0,\"numerator\":{\"type\":\"unit\",\"key\":\"tset.unit.mass.kilogram\"}},\"classificationKey\":\"tset.ref.classification.basePrice\"}]},\"active\":true,\"detailValueTypeKey\":\"pricecomposition\"}"}, "Detail_detail_-_material_composition": {"summary": "Detail - Material composition", "value": "{\"effectivities\":{\"region\":{\"type\":\"classification\",\"value\":\"vienna\"}},\"headerKey\":\"0815\",\"value\":{\"type\":\"materialcomposition\",\"components\":[{\"fraction\":{\"type\":\"numeric\",\"value\":5.0,\"numerator\":{\"type\":\"unit\",\"key\":\"tset.unit.rate.percentage\"}},\"quantity\":{\"type\":\"numeric\",\"value\":1000.0,\"numerator\":{\"type\":\"unit\",\"key\":\"tset.unit.piece.piece\"}},\"headerKey\":\"tset.ref.header.dc01\"}]},\"active\":true,\"detailValueTypeKey\":\"materialcomposition\"}"}, "SingletonList_details_-_numeric": {"summary": "Details - Numeric", "value": "[{\"effectivities\":{\"overheadMethod\":{\"type\":\"lov\",\"value\":\"BUILD_TO_PRINT_AUTO\"},\"validFromVolume\":{\"type\":\"numeric\",\"value\":1000.0,\"numerator\":{\"type\":\"unit\",\"key\":\"pcs\"}}},\"headerKey\":\"Tset.Profit.PurchasePart\",\"value\":{\"type\":\"numeric\",\"value\":4.0,\"numerator\":{\"type\":\"unit\",\"key\":\"percentage\"}},\"active\":true}]"}, "SingletonList_details_-_lov": {"summary": "Details - LOV", "value": "[{\"effectivities\":{\"region\":{\"type\":\"classification\",\"value\":\"vienna\"}},\"headerKey\":\"high-wage-country\",\"value\":{\"type\":\"lov\",\"key\":\"true\"},\"active\":true}]"}, "SingletonList_details_-_valuetype": {"summary": "Details - ValueType", "value": "[{\"effectivities\":{\"region\":{\"type\":\"classification\",\"value\":\"vienna\"}},\"headerKey\":\"high-wage-country\",\"value\":{\"type\":\"lov\",\"key\":\"true\"},\"active\":true,\"detailValueTypeKey\":\"price\"}]"}, "SingletonList_details_-_price_composition": {"summary": "Details - Price composition", "value": "[{\"effectivities\":{\"region\":{\"type\":\"classification\",\"value\":\"vienna\"}},\"headerKey\":\"0815\",\"value\":{\"type\":\"pricecomposition\",\"components\":[{\"massType\":\"GrossMass\",\"templateMass\":{\"type\":\"numeric\",\"value\":1000.0,\"numerator\":{\"type\":\"unit\",\"key\":\"tset.unit.mass.kilogram\"}},\"classificationKey\":\"tset.ref.classification.basePrice\"}]},\"active\":true,\"detailValueTypeKey\":\"pricecomposition\"}]"}, "SingletonList_details_-_material_composition": {"summary": "Details - Material composition", "value": "[{\"effectivities\":{\"region\":{\"type\":\"classification\",\"value\":\"vienna\"}},\"headerKey\":\"0815\",\"value\":{\"type\":\"materialcomposition\",\"components\":[{\"fraction\":{\"type\":\"numeric\",\"value\":5.0,\"numerator\":{\"type\":\"unit\",\"key\":\"tset.unit.rate.percentage\"}},\"quantity\":{\"type\":\"numeric\",\"value\":1000.0,\"numerator\":{\"type\":\"unit\",\"key\":\"tset.unit.piece.piece\"}},\"headerKey\":\"tset.ref.header.dc01\"}]},\"active\":true,\"detailValueTypeKey\":\"materialcomposition\"}]"}, "DetailQuery_search_overhead_rates": {"summary": "Search overhead rates", "value": "{\"filters\":{\"overheadMethod\":[{\"type\":\"lov\",\"equals\":\"BUILD_TO_PRINT_AUTO\"},{\"type\":\"lov\",\"equals\":\"BUILD_TO_PRINT_NON_AUTO\"}],\"validFromVolume\":[{\"type\":\"numeric\",\"greaterEquals\":{\"type\":\"numeric\",\"value\":100000.0}}]},\"classificationFieldFilters\":{\"drilling-type\":[{\"type\":\"lov\",\"equals\":\"automation\"}],\"length\":[{\"type\":\"numeric\",\"greaterEquals\":{\"type\":\"numeric\",\"value\":100000.0}}]},\"classificationFilters\":{\"classification-type-key\":[{\"type\":\"classification\",\"equals\":\"classification\",\"includeDescendants\":false}],\"another-classification-type-key\":[{\"type\":\"classification\",\"equals\":\"another-classification\",\"includeDescendants\":true}]},\"showInactive\":false}"}, "DetailQuery_search_without_filters": {"summary": "Search without filters", "value": "{\"filters\":{},\"classificationFieldFilters\":{},\"classificationFilters\":{},\"showInactive\":false}"}, "Currency_paraguayan_guarani": {"summary": "Paraguayan guarani", "value": "{\"key\":\"PYG\",\"name\":\"Paraguayan guarani\",\"symbol\":\"₲\"}"}, "Currency_ghanaian_cedi": {"summary": "Ghanaian cedi", "value": "{\"key\":\"GHS\",\"name\":\"Ghanaian cedi\",\"symbol\":\"₵\"}"}, "HeaderType_transport_costs_-_numeric_schema": {"summary": "Transport costs - Numeric Schema", "value": "{\"key\":\"transport-cost\",\"name\":\"Transport costs - Numeric Schema\",\"headerKeyType\":\"header.key.simple\",\"active\":true,\"index\":0,\"effectivities\":{\"part-length\":{\"unitKey\":\"meter\",\"columnVisibleByDefault\":true,\"filterVisibleByDefault\":true,\"index\":0},\"transport-type\":{\"columnVisibleByDefault\":true,\"filterVisibleByDefault\":true,\"index\":10},\"calculation-date\":{\"columnVisibleByDefault\":true,\"filterVisibleByDefault\":true,\"index\":20}},\"detailValueSchema\":{\"type\":\"numeric\",\"valueSchema\":{\"type\":\"numeric\",\"unitOfMeasurement\":{\"numerator\":{\"type\":\"currency\",\"key\":\"EUR\"},\"denominator\":{\"type\":\"unit\",\"key\":\"kilometer\"}},\"displayName\":\"EUR Per Kilometer\"}},\"classificationTypes\":[]}"}, "HeaderType_country_mapping_-_lov_schema": {"summary": "Country Mapping - <PERSON><PERSON><PERSON>", "value": "{\"key\":\"country-mapping\",\"name\":\"Country Mapping - LOV <PERSON>\",\"headerKeyType\":\"header.key.simple\",\"active\":true,\"index\":0,\"effectivities\":{\"region\":{\"columnVisibleByDefault\":true,\"filterVisibleByDefault\":true,\"index\":0}},\"detailValueSchema\":{\"type\":\"lov\",\"valueSchema\":{\"type\":\"lov\",\"lovTypeKey\":\"transport-type\"}}}"}, "HeaderType_detail_value_type_schema": {"summary": "Detail Value Type Schema", "value": "{\"key\":\"detail-value-type\",\"name\":\"Detail Value Type Schema\",\"headerKeyType\":\"header.key.simple\",\"active\":true,\"index\":0,\"effectivities\":{\"region\":{\"columnVisibleByDefault\":true,\"filterVisibleByDefault\":true,\"index\":0}},\"detailValueSchema\":{\"type\":\"valuetype\",\"detailValueTypeMapping\":{\"price\":{\"key\":\"price\",\"name\":\"Price Values\",\"index\":0,\"detailValueSchema\":{\"type\":\"numeric\",\"unitOfMeasurement\":{\"numerator\":{\"type\":\"any-currency\",\"defaultCurrencyKey\":\"EUR\"},\"denominator\":{\"type\":\"unit\",\"key\":\"pcs\"}}}},\"emission\":{\"key\":\"emission\",\"name\":\"CO2 Emission Values\",\"index\":1,\"detailValueSchema\":{\"type\":\"numeric\",\"unitOfMeasurement\":{\"numerator\":{\"type\":\"unit\",\"key\":\"kg_co2\"},\"denominator\":{\"type\":\"unit\",\"key\":\"pcs\"}}}},\"pricecomposition\":{\"key\":\"pricecomposition\",\"name\":\"Price composition\",\"index\":2,\"detailValueSchema\":{\"type\":\"pricecomposition\",\"headerTypeKey\":\"tset.ref.header-type.price-composition\",\"classificationTypeKey\":\"tset.ref.classification-type.price-composition\",\"displayName\":\"Price composition\"}},\"materialcomposition\":{\"key\":\"materialcomposition\",\"name\":\"Material composition\",\"index\":3,\"detailValueSchema\":{\"type\":\"materialcomposition\",\"headerTypeKey\":\"tset.ref.header-type.material\",\"displayName\":\"Material composition\"}}}}}"}, "Header_exchange_rate": {"summary": "Exchange rate", "value": "{\"key\":\"PYG\",\"name\":\"Paraguayan guarani\",\"headerTypeKey\":\"exchange-rate\",\"active\":true,\"detailValueSchema\":{\"type\":\"numeric\",\"valueSchema\":{\"type\":\"numeric\",\"unitOfMeasurement\":{\"numerator\":{\"type\":\"currency\",\"key\":\"PYG\"},\"denominator\":{\"type\":\"currency\",\"key\":\"EUR\"}}}}}"}, "Header_header_with_lov_detail_value": {"summary": "Header with LOV detail value", "value": "{\"key\":\"lov-header\",\"name\":\"Header with LOV detail value\",\"headerTypeKey\":\"mixed-headers\",\"active\":true,\"detailValueSchema\":{\"type\":\"lov\",\"valueSchema\":{\"type\":\"lov\",\"lovTypeKey\":\"transport-type\"}}}"}, "LinkedHashMap_get_latest_version_for_header_types": {"summary": "Get Latest Version for Header Types", "value": "{\"transport-cost\":[\"Tset.MaterialOverheadCosts\",\"Tset.Profit.RawMaterial\"],\"country-mapping\":[\"lov-header\"]}"}, "LookupStrategy_overhead_strategy": {"summary": "Overhead strategy", "value": "{\"key\":\"overhead-strategy\",\"name\":\"Overhead strategy\",\"headerTypeKey\":\"overhead\",\"effectivities\":{\"tset.data-source-field\":{\"type\":\"lov\",\"priority\":3,\"greenFallbackDistance\":0,\"yellowFallbackDistance\":1,\"mandatory\":false,\"lookupOrder\":0,\"defaultValue\":\"tset.customer\",\"validLovEntries\":[\"tset.customer\",\"tset.tset\"]},\"validFromVolume\":{\"type\":\"numeric\",\"priority\":1,\"greenFallbackDistance\":100000,\"yellowFallbackDistance\":1000000000,\"mandatory\":true,\"lookupOrder\":0},\"overheadMethod\":{\"type\":\"lov\",\"priority\":0,\"greenFallbackDistance\":0,\"yellowFallbackDistance\":1,\"mandatory\":false,\"lookupOrder\":0,\"validLovEntries\":[]},\"manufacturingStepType\":{\"type\":\"lov\",\"priority\":2,\"greenFallbackDistance\":0,\"yellowFallbackDistance\":1,\"mandatory\":false,\"lookupOrder\":0,\"validLovEntries\":[]}}}"}, "MasterdataLookupRequest_overhead_rate_lookup": {"summary": "Overhead rate lookup", "value": "{\"strategyKey\":\"defaultStrategy\",\"headerTypeKey\":\"overhead\",\"headerKeys\":[\"Tset.MaterialOverheadCosts\",\"Tset.Profit.RawMaterial\"],\"effectivities\":[{\"fieldDefinitionKey\":\"overheadMethod\",\"value\":{\"type\":\"lov\",\"value\":\"BUILD_TO_PRINT_AUTO\"}},{\"fieldDefinitionKey\":\"validFromVolume\",\"value\":{\"type\":\"numeric\",\"value\":100000.0,\"numerator\":{\"type\":\"unit\",\"key\":\"pcs\"}}}]}"}}}}