import { manufacturingStore } from '@domain/calculation/manufacturing.store'
import { MergeSource } from '@tset/shared-model/calculation/Merge'
import { gBomLink } from '@tset/shared-utils/tests/generators/bomLink'
import { gBomNodeEntity } from '@tset/shared-utils/tests/generators/bomNodeEntity'
import { withAxiosMock } from '@tset/shared-utils/tests/mocks/withAxiosMock'
import {
  flushPromises,
  mount,
  type ComponentMountingOptions,
} from '@vue/test-utils'
import { nextTick } from 'vue'
import CalculationUpdatePanel from './CalculationUpdatePanel.vue'
import RefreshTitle from './RefreshTitle.vue'
import UpdateItem from './UpdateItem.vue'

//#region MOCKS
const { setMockResponse } = withAxiosMock()
//#endregion MOCKS

//#region SETUP FACTORY
const setup = ({
  openMergesAvailable = [],
}: {
  openMergesAvailable?: MergeSource[]
} = {}) => {
  const mountOptions: ComponentMountingOptions<typeof CalculationUpdatePanel> =
    {
      global: {
        stubs: ['IconControlsRefresh', 'IconSpinner'],
      },
    }

  manufacturingStore.setNodeInt(gBomNodeEntity({ subNodes: [gBomLink()] }))
  setMockResponse(
    gBomNodeEntity({
      subNodes: [gBomLink()],
      openMergesAvailable,
    })
  )
  const wrapper = mount(CalculationUpdatePanel, mountOptions)

  //#region HELPERS
  const getByTestId = (id: string) =>
    wrapper.find(`[data-test='calculation-update-panel-${id}']`)
  const hasInitialLoader = () => getByTestId('initial-loader').exists()
  const hasEmptyView = () => getByTestId('empty-view').exists()
  const waitForUIUpdate = () => nextTick()
  const emptyViewRefreshButton = () =>
    getByTestId('empty-view').find('button[data-test=button-refresh]')
  const hasEmptyViewRefreshButton = () => emptyViewRefreshButton().exists()
  const clickEmptyViewRefreshButton = () =>
    emptyViewRefreshButton().trigger('click')
  const hasEmptyViewIsRefreshing = () =>
    getByTestId('empty-view').find("[data-test='loading-spinner']").exists()
  const getUpdatesList = () => getByTestId('updates-list')
  const getUpdateListTitle = () =>
    getUpdatesList().findComponent(RefreshTitle).text()
  const getUpdateButtonsText = () =>
    getUpdatesList()
      .findComponent(UpdateItem)
      .findAll('button')
      .map((b) => b.text())
  const getUpdateItemTitle = () =>
    getUpdatesList()
      .findComponent(UpdateItem)
      .find('[data-test=update-item-title]')
      .text()

  const clickAvailableUpdateRefreshButton = () =>
    getUpdatesList().find('button[data-test=button-refresh]').trigger('click')
  const isRefreshButtonVisible = () =>
    getUpdatesList().find('button[data-test=button-refresh]').exists()
  const isLoadingSpinnerVisible = () =>
    getUpdatesList().find("[data-test='loading-spinner']").exists()
  //#endregion HELPERS

  return {
    hasInitialLoader,
    hasEmptyView,
    waitForUIUpdate,
    hasEmptyViewRefreshButton,
    clickEmptyViewRefreshButton,
    hasEmptyViewIsRefreshing,
    wrapper,
    getUpdateButtonsText,
    getUpdateItemTitle,
    getUpdateListTitle,
    isLoadingSpinnerVisible,
    isRefreshButtonVisible,
    clickAvailableUpdateRefreshButton,
  }
}
//#endregion SETUP FACTORY

//#region TESTS
describe('CalculationUpdatePanel', () => {
  beforeEach(() => {
    expect.hasAssertions()
  })
  describe('First view', () => {
    it('- shows initial loader', async () => {
      const { hasInitialLoader, waitForUIUpdate } = setup()
      await waitForUIUpdate()
      expect(hasInitialLoader()).toBeTruthy()
    })
  })
  describe('No Updates', () => {
    it('- shows empty view', async () => {
      const { hasEmptyView, hasInitialLoader } = setup()
      await flushPromises()
      expect(hasInitialLoader()).toBeFalsy()
      expect(hasEmptyView()).toBeTruthy()
    })
    it('- can refresh updates', async () => {
      const {
        hasEmptyViewRefreshButton,
        clickEmptyViewRefreshButton,
        hasEmptyViewIsRefreshing,
        waitForUIUpdate,
      } = setup()
      await waitForUIUpdate()
      await flushPromises()

      expect(hasEmptyViewRefreshButton()).toBe(true)
      await clickEmptyViewRefreshButton()
      expect(hasEmptyViewIsRefreshing()).toBe(true)
    })
  })

  describe('Has Updates', () => {
    it('- shows available updates title', async () => {
      const { getUpdateListTitle, waitForUIUpdate } = setup({
        openMergesAvailable: [MergeSource.MASTERDATA],
      })
      await waitForUIUpdate()
      await flushPromises()
      expect(getUpdateListTitle()).toBe('conflicts.availableUpdates')
    })
    it('- allows to request updates', async () => {
      const { isRefreshButtonVisible, waitForUIUpdate } = setup({
        openMergesAvailable: [MergeSource.MASTERDATA],
      })
      await waitForUIUpdate()
      await flushPromises()
      expect(isRefreshButtonVisible()).toBe(true)
    })
    it('- shows loader while refreshing', async () => {
      const {
        clickAvailableUpdateRefreshButton,
        isLoadingSpinnerVisible,
        waitForUIUpdate,
      } = setup({
        openMergesAvailable: [MergeSource.MASTERDATA],
      })
      await waitForUIUpdate()
      await flushPromises()
      await clickAvailableUpdateRefreshButton()
      expect(isLoadingSpinnerVisible()).toBe(true)
    })
    describe('Check Titles and Buttons', () => {
      const testCase =
        (
          mergeSources: MergeSource[],
          expectedTitle: string,
          expectedButtons: string[]
        ) =>
        async () => {
          const { getUpdateButtonsText, waitForUIUpdate, getUpdateItemTitle } =
            setup({
              openMergesAvailable: mergeSources,
            })
          await waitForUIUpdate()
          await flushPromises()
          expect(getUpdateItemTitle()).toBe(expectedTitle)
          expect(getUpdateButtonsText()).toStrictEqual(expectedButtons)
        }
      it(
        '- shows "Update" for main variant update',
        testCase([MergeSource.MASTER], 'conflicts.mainVariantChanges', [
          'conflicts.Update',
        ])
      )
      it(
        '- shows "Update" for variant change update',
        testCase([MergeSource.BRANCH], 'conflicts.variantChanges', [
          'conflicts.Update',
        ])
      )
      it(
        '- show "Update calculation" for masterdata changes',
        testCase([MergeSource.MASTERDATA], 'conflicts.masterdataChanges', [
          'conflicts.UpdateCalculation',
        ])
      )
      it(
        '- show "Updated Assemboy" for masterdata children only changes',
        testCase(
          [MergeSource.MASTERDATA_CHILDREN],
          'conflicts.masterdataChanges',
          ['conflicts.UpdateSelectedAssembly']
        )
      )
      it(
        '- show "Update Whole" and "Update Current" for masterdata and child changes',
        testCase(
          [MergeSource.MASTERDATA_CHILDREN, MergeSource.MASTERDATA],
          'conflicts.masterdataChanges',
          [
            'conflicts.UpdateSelectedCalculation',
            'conflicts.UpdateSelectedAssembly',
          ]
        )
      )
    })
  })
})
//#endregion TESTS
