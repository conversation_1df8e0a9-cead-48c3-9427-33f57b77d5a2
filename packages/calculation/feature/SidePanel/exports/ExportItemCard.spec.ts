import { manufacturingStore } from '@domain/calculation/manufacturing.store'
import { $t } from '@shared/translation/nuTranslation'
import TsetCard from '@tset/design/atoms/TsetCard/TsetCard.vue'
import TsetWave from '@tset/design/atoms/TsetWave/TsetWave.vue'
import { gBomNodeEntity } from '@tset/shared-utils/tests/generators/bomNodeEntity'
import { gManufacturing } from '@tset/shared-utils/tests/generators/manufacturing'
import { mount, type ComponentMountingOptions } from '@vue/test-utils'
import { nextTick } from 'vue'
import ExportItemCard from './ExportItemCard.vue'
import { gExportItem, type ExportItem } from './exports.model'

//#region SETUP FACTORY
const setup = ({
  type = 'TSET_FILE',
  item = gExportItem(),
  loadedNode = gBomNodeEntity(),
  isLoading = false,
}: {
  type?: string
  item?: ExportItem
  loadedNode?: BomNodeEntity
  isLoading?: boolean
} = {}) => {
  const props: InstanceType<typeof ExportItemCard>['$props'] = {
    type,
    item,
    isLoading,
  }
  const mountOptions: ComponentMountingOptions<typeof ExportItemCard> = {
    props,

    global: {
      stubs: ['IconDownload'],
    },
  }

  const wrapper = mount(ExportItemCard, mountOptions)
  manufacturingStore.setNode({ node: loadedNode })

  //#region HELPERS
  const getTsetCard = () => wrapper.getComponent(TsetCard)
  const getWrapperDiv = () => getTsetCard().get('div')
  const getMainLabel = () =>
    wrapper.find("[data-test='export-item-card-main-label']")
  const getSubLabel = () =>
    wrapper.find("[data-test='export-item-card-sub-label']")
  const getMainLabelText = () => getMainLabel().text()
  const getSubLabelText = () => getSubLabel().text()
  const getIconDownload = () => wrapper.get('icon-download-stub')
  const getIconTset = () => wrapper.get('icontset')
  const getIconFileDocumentXLSX = () => wrapper.get('iconfiledocumentxlsx')
  const getIconFileDocumentCSV = () => wrapper.get('iconfiledocumentcsv')
  const getEmittedEvent = (event: string) => wrapper.emitted(event)
  const getLoadingIndicator = () => wrapper.findComponent(TsetWave)
  //#endregion HELPERS

  return {
    getEmittedEvent,
    getIconDownload,
    getIconFileDocumentCSV,
    getIconFileDocumentXLSX,
    getIconTset,
    getMainLabel,
    getMainLabelText,
    getSubLabel,
    getSubLabelText,
    getTsetCard,
    getWrapperDiv,
    getLoadingIndicator,
  }
}
//#endregion SETUP FACTORY

//#region TESTS
describe('ExportItemCard', () => {
  describe('Labels', () => {
    const type = 'TSET_XLSX'

    it('- has the proper translation for the main label based on the type', () => {
      const { getMainLabelText } = setup({ type })
      expect(getMainLabelText()).toBe($t(`exports.${type}`))
    })

    it('- has the proper translation for the sub label (description) based on the type', () => {
      const { getSubLabelText } = setup({ type })
      expect(getSubLabelText()).toBe($t(`exports.descriptions.${type}`))
    })
  })

  describe('Events', () => {
    it('- emits "click" when the TsetCard is clicked (TsetCard "click" event is triggered)', async () => {
      const { getWrapperDiv, getEmittedEvent } = setup()
      getWrapperDiv().trigger('click')
      expect(getEmittedEvent('click')).toBeTruthy()
    })
  })

  describe('Icons', () => {
    it('- has the IconTset by default', () => {
      const { getIconTset } = setup()
      expect(getIconTset().isVisible()).toBeTruthy()
    })

    it('-  has the IconFileDocumentXLSX when the item is of type "XLSX"', () => {
      const { getIconFileDocumentXLSX } = setup({
        item: gExportItem({ type: 'XLSX' }),
      })
      expect(getIconFileDocumentXLSX().isVisible()).toBeTruthy()
    })

    it('- has the IconFileDocumentCSV when the item is of type "CSV"', () => {
      const { getIconFileDocumentCSV } = setup({
        item: gExportItem({ type: 'CSV' }),
      })
      expect(getIconFileDocumentCSV().isVisible()).toBeTruthy()
    })

    it('- has the IconDownload when the div has been hovered', async () => {
      const { getIconDownload, getWrapperDiv } = setup()
      getWrapperDiv().trigger('mouseenter')
      await nextTick()
      expect(getIconDownload().isVisible()).toBeTruthy()
    })

    it('- has the IconTset when the div has been hovered, but the card is disabled', async () => {
      const { getIconTset, getWrapperDiv } = setup({
        type: 'TSET_FILE',
        loadedNode: gBomNodeEntity({
          manufacturing: gManufacturing({ copyable: false }),
        }),
      })
      getWrapperDiv().trigger('mouseenter')
      await nextTick()
      expect(getIconTset().isVisible()).toBeTruthy()
    })

    it('- has the right icon when the div hover status has been removed', async () => {
      const { getIconTset, getWrapperDiv } = setup()
      getWrapperDiv().trigger('mouseenter')
      await nextTick()
      getWrapperDiv().trigger('mouseleave')
      await nextTick()
      expect(getIconTset().isVisible()).toBeTruthy()
    })

    it('- has loader instead of an icon when `isLoading` prop is true', () => {
      const { getLoadingIndicator } = setup({ isLoading: true })

      expect(getLoadingIndicator().exists()).toBeTruthy()
    })
  })
})
//#endregion TESTS
