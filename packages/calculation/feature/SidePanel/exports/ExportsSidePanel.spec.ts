import { manufacturingStore } from '@domain/calculation/manufacturing.store'
import { getRecordLength } from '@tset/shared-utils/helpers/general'
import { gBomNodeEntity } from '@tset/shared-utils/tests/generators/bomNodeEntity'
import { gManufacturingEntity } from '@tset/shared-utils/tests/generators/manufacturingEntity'
import { gResultField } from '@tset/shared-utils/tests/generators/resultField'
import { withAxiosMock } from '@tset/shared-utils/tests/mocks/withAxiosMock'
import type { ComponentMountingOptions, VueWrapper } from '@vue/test-utils'
import { flushPromises, shallowMount } from '@vue/test-utils'
import { defineComponent } from 'vue'
import { createRouter, createWebHistory } from 'vue-router'
import ExportItemCard from './ExportItemCard.vue'
import ExportsCardSection from './ExportsCardSection.vue'
import ExportsSidePanel from './ExportsSidePanel.vue'
import * as exportsApi from './exports.api'
import * as exportsData from './exports.data'
import type { CustomExport, ExportRecord } from './exports.model'
import { gExportItem } from './exports.model'

//#region MOCKS
const { axiosMock, setMockResponse, clearAllMocks } = withAxiosMock()
const defaultExportList: ExportRecord = {
  TSET_XLSX: gExportItem({ type: 'TSET_XLSX' }),
  TSET_CO2_XLSX: gExportItem({ type: 'TSET_CO2_XLSX' }),
  PPC_V9_XLSX: gExportItem({ type: 'PPC_V9_XLSX' }),
  PPC_V9_CSV: gExportItem({ type: 'PPC_V9_CSV' }),
  TSET_FILE: gExportItem({ type: 'TSET_FILE' }),
}
const loadedProject = {
  _id: 'initial-project-id',
  key: 'initial-project-key',
}
const customExportResponse: CustomExport[] = [
  {
    typeId: 'customId',
    shortName: 'shortName',
    longName: 'longName',
  },
  {
    typeId: 'anotherCustomId',
    shortName: 'anotherShortName',
    longName: 'anotherLongName',
  },
]
const customExportList: ExportRecord = {
  customId: {
    label: 'shortName',
    section: 'custom',
    sublabel: 'longName',
    type: '',
  },
  anotherCustomId: {
    label: 'anotherShortName',
    section: 'custom',
    sublabel: 'anotherLongName',
    type: '',
  },
}

vi.mock('@/store/cost.store', () => {
  return {
    costStore: {
      loadedProject: {
        _id: 'initial-project-id',
        key: 'initial-project-key',
      },
    },
  }
})
const getCustomExportAPIMock = vi
  .spyOn(exportsApi, 'getCustomExportAPI')
  .mockResolvedValue([])
const downloadExportedItemMock = vi.spyOn(exportsData, 'downloadExportedItem')
const triggerCustomExportAPIMock = vi.spyOn(
  exportsApi,
  'triggerCustomExportAPI'
)
//#endregion MOCKS

//#region SETUP FACTORY
const setup = async ({
  response = defaultExportList,
  node = gBomNodeEntity(),
  withCustomExports = false,
}: {
  response?: ExportRecord
  node?: BomNodeEntity
  withCustomExports?: boolean
} = {}) => {
  window.open = vi.fn()
  clearAllMocks()
  setMockResponse(response)
  manufacturingStore.setNode({ node })
  getCustomExportAPIMock.mockResolvedValue(
    withCustomExports ? customExportResponse : []
  )
  const router = createRouter({
    routes: [{ path: '/', component: defineComponent({}) }],
    history: createWebHistory(),
  })
  window.open = vi.fn()
  await router.push({ query: { path: 'bomNodeId', branch: 'branchId' } })
  const mountOptions: ComponentMountingOptions<typeof ExportsSidePanel> = {
    global: {
      plugins: [router],
    },
  }

  const wrapper = shallowMount(ExportsSidePanel, mountOptions)
  await flushPromises()

  //#region HELPERS
  const getExportItemCards = () => wrapper.findAllComponents(ExportItemCard)
  const clickOnExportItemCard = () =>
    getExportItemCards().at(0)?.trigger('click')
  const getExportCardSections = () =>
    wrapper.findAllComponents(ExportsCardSection)
  const getCustomExportsCard = () =>
    wrapper.findComponent<VueWrapper>(
      "[data-test='exports-side-panel-exports-card-section-custom']"
    )
  const getCustomExportsCardProps = (prop: string) =>
    // @ts-expect-error no idea how to type this
    getCustomExportsCard().props(prop)
  const clickOnCustomExport = () =>
    getCustomExportsCard().vm.$emit('click', 'customId')
  const getTsetExportsCard = () =>
    wrapper.findComponent<VueWrapper>(
      "[data-test='exports-side-panel-exports-card-section-default']"
    )
  const getTsetExportsCardProps = (prop: string) =>
    // @ts-expect-error no idea how to type this
    getTsetExportsCard().props(prop)
  const clickOnTsetExport = () =>
    getTsetExportsCard().vm.$emit('click', 'TSET_XLSX')
  const getCustomExportEmail = () =>
    wrapper.find("[data-test='exports-side-panel-custom-export-email']")
  const getCustomExportText = () =>
    wrapper.find("[data-test='exports-side-panel-custom-export-text']")
  const getCustomExportTextContent = () => getCustomExportText().text()
  const getTsetExportsWrapper = () =>
    wrapper.find('[data-test="exports-side-panel-tset-exports-wrapper"]')
  //#endregion HELPERS

  return {
    getExportItemCards,
    clickOnExportItemCard,
    getExportCardSections,
    getCustomExportsCard,
    getCustomExportsCardProps,
    clickOnCustomExport,
    getTsetExportsCard,
    getTsetExportsCardProps,
    clickOnTsetExport,
    getCustomExportEmail,
    getCustomExportText,
    getCustomExportTextContent,
    getTsetExportsWrapper,
    wrapper,
  }
}
//#endregion SETUP FACTORY

//#region TESTS
describe('ExportsSidePanel', () => {
  describe('Initial Call', () => {
    it('- makes an API call to retrieve available exports', async () => {
      const node = gBomNodeEntity()
      await setup({ node })

      expect(axiosMock.get).toHaveBeenCalledWith(
        `/api/projects/initial-project-id/calculations/${node.id}/export/formats`
      )
    })

    it('- makes an API call to retrieve available custom exports', async () => {
      await setup()
      expect(getCustomExportAPIMock).toHaveBeenCalled()
    })
  })

  describe('Only one section of exports', () => {
    it('- has the text for inviting to the creation on Custom exports present', async () => {
      const { getCustomExportText } = await setup()
      expect(getCustomExportText().exists()).toBe(true)
    })

    it('- has the email for inviting to the creation on Custom exports present', async () => {
      const { getCustomExportEmail } = await setup()
      expect(getCustomExportEmail().exists()).toBe(true)
    })

    it('- has the text splitted into three parts for the email action to kick in', async () => {
      const { getCustomExportTextContent } = await setup()
      expect(getCustomExportTextContent()).toBe(
        'statics.exportsPanelDescriptionPart1 statics.exportsPanelDescriptionPart2 statics.exportsPanelDescriptionPart3'
      )
    })

    it('- hides export card sections', async () => {
      const { getExportCardSections } = await setup()
      expect(getExportCardSections().length).toBe(0)
    })

    it('- has as many "ExportItemCard" as there were ExportItems in the answer from the API call', async () => {
      const { getExportItemCards } = await setup()
      expect(getExportItemCards().length).toBe(
        getRecordLength(defaultExportList)
      )
    })

    describe('when clicking on an item', () => {
      it('- triggers download of the export', async () => {
        const node = gBomNodeEntity()
        const { clickOnExportItemCard } = await setup({ node })
        await clickOnExportItemCard()

        expect(downloadExportedItemMock).toHaveBeenCalledWith(
          'TSET_XLSX',
          loadedProject,
          node
        )
      })
    })
  })

  describe('Two section of exports (with custom export)', () => {
    it('- hides single section with cards and a hint', async () => {
      const { getTsetExportsWrapper } = await setup({
        withCustomExports: true,
      })

      expect(getTsetExportsWrapper().exists()).toBeFalsy()
    })

    it('- shows 2 export card sections', async () => {
      const { getExportCardSections } = await setup({ withCustomExports: true })
      expect(getExportCardSections().length).toBe(2)
    })

    it('- provides the list of default exports to the card', async () => {
      const { getTsetExportsCardProps } = await setup({
        withCustomExports: true,
      })
      expect(getTsetExportsCardProps('exportList')).toEqual(defaultExportList)
    })

    it('- provides the list of custom exports to the card', async () => {
      const { getCustomExportsCardProps } = await setup({
        withCustomExports: true,
      })

      expect(getCustomExportsCardProps('exportList')).toEqual(customExportList)
    })

    describe('when default export section emits a click', () => {
      it('- triggers download of the export', async () => {
        const node = gBomNodeEntity()
        const { clickOnTsetExport } = await setup({
          node,
          withCustomExports: true,
        })
        await clickOnTsetExport()

        expect(downloadExportedItemMock).toHaveBeenCalledWith(
          'TSET_XLSX',
          loadedProject,
          node
        )
      })
    })

    describe('when custom export section emits a click', () => {
      it('- sets loading export item to the selected one', async () => {
        const { clickOnCustomExport, getCustomExportsCardProps } = await setup({
          withCustomExports: true,
        })

        expect(getCustomExportsCardProps('itemLoading')).toBe(null)
        await clickOnCustomExport()
        expect(getCustomExportsCardProps('itemLoading')).toBe('customId')
        await flushPromises()
        expect(getCustomExportsCardProps('itemLoading')).toBe(null)
      })

      it('- triggers API call to start custom export', async () => {
        const calcName = 'calcName'
        const node = gBomNodeEntity({
          manufacturing: gManufacturingEntity({
            fields: [
              gResultField({ name: 'partDesignation', value: calcName }),
            ],
          }),
        })
        const { clickOnCustomExport } = await setup({
          node,
          withCustomExports: true,
        })
        await clickOnCustomExport()

        expect(triggerCustomExportAPIMock).toHaveBeenCalledWith({
          branchId: 'branchId',
          calculationName: calcName,
          path: ['bomNodeId'],
          projectKey: loadedProject.key,
          typeId: 'customId',
        })
      })
    })
  })
})
//#endregion TESTS
