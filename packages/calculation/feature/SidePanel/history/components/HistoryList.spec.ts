import { dataTest } from '@tset/shared-utils/tests/general'
import { gHistoryNodeChange } from '@tset/shared-utils/tests/generators/historyNodeChange'
import { gHistoryTriggerDTO } from '@tset/shared-utils/tests/generators/historyTriggerDTO'
import { gResultField } from '@tset/shared-utils/tests/generators/resultField'
import { mount, type ComponentMountingOptions } from '@vue/test-utils'
import type { Component } from 'vue'
import CompositeItemLabel from './CompositeItemLabel.vue'
import HistoryList from './HistoryList.vue'
import BulkActionChangeItem from './triggers/BulkActionChangeItem.vue'
import CompositeTriggerList from './triggers/CompositeTriggerList.vue'
import ExternalChangeItem from './triggers/ExternalChangeItem.vue'
import FieldValueChangeItem from './triggers/FieldValueChangeItem.vue'
import GenericHistoryChangeItem from './triggers/GenericHistoryChangeItem.vue'
import VariantBomChangeItem from './triggers/VariantBomChangeItem.vue'

//#region SETUP FACTORY
const setup = (
  changes: InstanceType<typeof HistoryList>['$props']['changes']
) => {
  const mountOptions: ComponentMountingOptions<typeof HistoryList> = {
    props: {
      changes,
      type: 'saved',
    },
    global: {
      stubs: [
        'IconArrowDown',
        'IconArrowUp',
        'IconArrowLeft',
        'TsetTranslate',
        'IconChevronLeft',
        'IconWarning',
      ],
    },
  }

  const wrapper = mount(HistoryList, mountOptions)

  //#region HELPERS
  const getRenderedItemCount = () =>
    wrapper.findAll(dataTest('history-list-item')).length
  const rendersExternalChange = () =>
    wrapper.findAllComponents(ExternalChangeItem).length === 1
  const getFirstRenderedComponent = () =>
    wrapper.find(dataTest('history-list-item') + ' div').getCurrentComponent()
      ?.type.name
  //#endregion HELPERS

  return {
    getRenderedItemCount,
    rendersExternalChange,
    getFirstRenderedComponent,
  }
}
//#endregion SETUP FACTORY

//#region TESTS
describe('HistoryList', () => {
  beforeEach(() => {
    expect.hasAssertions()
  })

  describe('External change', () => {
    function testCase(
      relation: BomNodeRelationType,
      expectation: boolean,
      rendered: number
    ) {
      const { rendersExternalChange, getRenderedItemCount } = setup([
        gHistoryNodeChange({ relationToRequested: relation }),
      ])
      expect(rendersExternalChange()).toBe(expectation)
      expect(getRenderedItemCount()).toBe(rendered)
    }
    describe('Parent', () => {
      it('- renders external change', () => testCase('PARENT', true, 0))
    })
    describe('Child', () => {
      it('- renders external change', () => testCase('CHILD', true, 0))
    })
    describe('Ext', () => {
      it("- doesn't render external change", () => testCase('EXT', false, 1))
    })
    describe('Same', () => {
      it("- doesn't render external change", () => testCase('SAME', false, 1))
    })
  })

  describe('Item render count', () => {
    it('- renders correct number of changes', () => {
      const change = [
        gHistoryNodeChange({
          relationToRequested: 'SAME',
          trigger: {
            triggerType: 'FieldValueChange',
            data: {
              path: [
                {
                  displayDesignation: gResultField({ value: 'calc-1' }),
                  entityType: 'entityType',
                  objectId: 'objectId',
                },
                {
                  displayDesignation: gResultField({ value: 'sub-calc-1' }),
                  entityType: 'entityType',
                  objectId: 'sub object id',
                },
              ],
            },
          },
        }),
        gHistoryNodeChange({ relationToRequested: 'SAME' }),
        gHistoryNodeChange({ relationToRequested: 'SAME' }),
      ]
      const { getRenderedItemCount } = setup(change)
      expect(getRenderedItemCount()).toBe(change.length)
    })
  })
  describe('Same Changes', () => {
    function testCase(
      triggerType: TopLevelTriggerType | HistoryTriggerDTO,
      expectedComponent: Component['name']
    ) {
      it(`- ${triggerType} renders ${expectedComponent}`, () => {
        const { getFirstRenderedComponent } = setup([
          gHistoryNodeChange({
            relationToRequested: 'SAME',
            trigger:
              typeof triggerType === 'object'
                ? triggerType
                : // TODO fix this
                  //  @ts-expect-error i don't understand this
                  gHistoryTriggerDTO({ triggerType: triggerType }),
          }),
        ])
        expect(getFirstRenderedComponent()).toBe(expectedComponent)
      })
    }

    describe('Field Value changes', () => {
      testCase('FieldValueChange', FieldValueChangeItem.name)
      testCase('FieldValueChangeWithCurrency', FieldValueChangeItem.name)
      testCase('FieldUnlock', FieldValueChangeItem.name)
      testCase('MainImageChange', FieldValueChangeItem.name)
      testCase('SetResponsible', FieldValueChangeItem.name)
    })

    describe('Variant and Bom Changes', () => {
      testCase('RenameBranch', VariantBomChangeItem.name)
      testCase('BomPublish', VariantBomChangeItem.name)
      testCase('UnpublishBranch', VariantBomChangeItem.name)
      testCase('SavePublicVariant', VariantBomChangeItem.name)
      testCase('RootManufacturingChange', VariantBomChangeItem.name)
      testCase('BomTypeChange', VariantBomChangeItem.name)
      testCase('SubBomTypeChange', VariantBomChangeItem.name)
      testCase('MergeAction', VariantBomChangeItem.name)
    })

    describe('Bulk action changes', () => {
      testCase('BulkActionTrigger', BulkActionChangeItem.name)
    })

    describe('Others', () => {
      testCase('CrossProjectEntityCopy', CompositeItemLabel.name)
      testCase('EntityRearrangement', GenericHistoryChangeItem.name)
      testCase('EntityReorder', GenericHistoryChangeItem.name)
      testCase('DirtyRecalculateCalculation', GenericHistoryChangeItem.name)
      testCase('RecalculationTrigger', GenericHistoryChangeItem.name)
      testCase('UpdateMasterData', GenericHistoryChangeItem.name)
    })

    describe('CompositeChanges', () => {
      testCase(
        gHistoryTriggerDTO({
          triggerType: 'CompositeTriggerAction',
          data: {
            triggers: [],
          },
        }),
        CompositeTriggerList.name
      )
    })
  })
})
//#endregion TESTS
