<template>
  <div v-data-test class="flex items-center gap-4 overflow-hidden">
    <IconWarning
      v-if="dirtyLoading && !props.broken"
      v-data-test:dirty-icon
      v-tooltip="$t('tooltip.DIRTY_LOADING_HISTORY')"
      class="w-24 text-primary-default"
    />
    <TsetBadge :type="badgeType" class="max-w-max overflow-hidden">
      <template v-if="broken">
        <div class="flex items-center gap-4">
          <IconWarning
            v-data-test:broken-icon
            class="w-16 text-warning-default"
          />
          <span class="h-18 text-[25px] font-semibold text-warning-default">
            ----
          </span>
        </div>
      </template>
      <template v-else>
        <span
          v-data-test:value
          v-tooltip="`${diffValue} ${unit}`"
          class="min-w-10 truncate text-right"
        >
          {{ diffValueFormatted }} {{ unit }}
        </span>
        <span class="px-4 [&>svg]:h-20">
          <IconArrowUp
            v-if="diffChangeType === 'increase'"
            v-data-test:increase
          />
          <IconArrowDown
            v-else-if="diffChangeType === 'decrease'"
            v-data-test:decrease
          />
          <IconArrowLeft v-else v-data-test:neutral />
        </span>
        <span v-data-test:percent v-tooltip="percentVal" class="truncate">
          {{ percentVal }}
        </span>
      </template>
    </TsetBadge>
  </div>
</template>

<script setup lang="ts">
import { $formatNumber } from '@shared/format/formatNumber'
import type { BadgeType } from '@tset/design/atoms/TsetBadge'
import TsetBadge from '@tset/design/atoms/TsetBadge'
import { computed } from 'vue'

//#region PROPS
const props = withDefaults(
  defineProps<{
    kpi: KpiChange<number>
    unit: string
    dirtyLoading?: boolean
    broken?: boolean
  }>(),
  { dirtyLoading: false, broken: false }
)
//#endregion PROPS

const broken = computed(() => {
  return Boolean(props.kpi.broken)
})

const diffValue = computed(
  () => Number(props.kpi.newValue ?? 0) - Number(props.kpi.oldValue ?? 0)
)

const percentVal = computed(() => {
  const percent = props.kpi.percentageDifference
  return `${Number.isFinite(percent) ? $formatNumber(percent) : '—'} %`
})

const diffValueFormatted = computed(() => {
  const negativeSign = diffValue.value < 0 ? '-' : false
  const positiveSign = diffValue.value > 0 ? '+' : false
  const sign = positiveSign || negativeSign || ''
  return `${sign}${$formatNumber(diffValue.value)
    .replace('-', '')
    .replace('+', '')}`
})

const diffChangeType = computed(() => {
  if (broken.value) {
    return 'broken'
  } else if (diffValue.value < 0) {
    return 'decrease'
  } else if (diffValue.value > 0) {
    return 'increase'
  } else {
    return 'neutral'
  }
})

const badgeType = computed(
  (): BadgeType =>
    (
      ({
        decrease: 'success',
        increase: 'error',
        neutral: 'primary',
        broken: 'warning',
      }) as const
    )[diffChangeType.value]
)
</script>

<script lang="ts">
export default {
  name: 'CostChangeBadge',
}
</script>
