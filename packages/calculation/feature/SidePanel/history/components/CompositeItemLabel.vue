<template>
  <HistoryItem
    :action="$t(`newHistory.${item.triggerType}`)"
    class="text-black-default"
  >
    <TsetTranslate
      v-if="sourceAndTarget"
      keypath="history.itemFromSource"
      tag="div"
      class="flex flex-wrap items-center gap-5 overflow-hidden"
    >
      <template #item>
        <HistoryItem
          :entity-icon="sourceAndTarget.itemIcon"
          :title="sourceAndTarget.itemLabel"
        />
      </template>
      <template #source>
        <HistoryItem
          :entity-icon="sourceAndTarget.sourceIcon"
          :title="sourceAndTarget.sourceLabel"
        />
      </template>
    </TsetTranslate>
    <HistoryItem v-else-if="normalLabel.title" v-bind="normalLabel" />
  </HistoryItem>
</template>

<script setup lang="ts">
import { $nutDeep } from '@shared/translation/nuTranslation'
import TsetTranslate from '@tset/shared-ui/TsetTranslate/TsetTranslate.vue'
import { iconGetter } from '@tset/shared-utils/helpers/general'
import { computed } from 'vue'
import { getHistoryPath } from '../../history/history.utils'
import HistoryItem from './HistoryItem.vue'

const props = defineProps<{
  item: CompositeChildTriggerDTO
  isBomCreation?: boolean
}>()

const sourceAndTarget = computed(() => {
  const item = props.item.data
  if (!(item.sourceType && item.sourceProjectId)) {
    return null
  }
  return {
    sourceIcon: iconGetter(
      item.sourceParentCalcPartName
        ? 'CALCULATION'
        : item.sourceProjectId
          ? 'PROJECT'
          : ''
    ),
    sourceLabel: item.sourceParentCalcPartName ?? item.sourceProjectName ?? '',
    itemIcon: iconGetter(props.item.data.sourceType ?? ''),
    itemLabel:
      props.item.data.partInfo?.partName ??
      props.item.data.sourceDisplayName ??
      '',
  }
})

const normalLabel = computed(() => {
  const item = props.item.data
  const title =
    item.sourceDisplayName ??
    item.partInfo?.partName ??
    item.entityDisplayName ??
    ''
  const path = item.path ?? item.sourceEntityPath ?? []
  return {
    title: props.isBomCreation ? '' : title,
    entityIcon: iconGetter(path.at(-1)?.entityType ?? item.entityType ?? ''),
    path: getHistoryPath(path),
  }
})
</script>

<script lang="ts">
export default {
  name: 'CompositeItemLabel',
}
</script>
