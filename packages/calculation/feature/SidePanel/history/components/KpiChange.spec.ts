import { manufacturingStore } from '@domain/calculation/manufacturing.store'
import { useDisplayCurrency } from '@tset/shared-utils/api/useDisplayCurrency'
import { dataTest } from '@tset/shared-utils/tests/general'
import { gBomNodeEntity } from '@tset/shared-utils/tests/generators/bomNodeEntity'
import { gCurrencyInfo } from '@tset/shared-utils/tests/generators/currencyInfo'
import { gKpiChange } from '@tset/shared-utils/tests/generators/kpiChange'
import { gManufacturing } from '@tset/shared-utils/tests/generators/manufacturing'
import { gResultField } from '@tset/shared-utils/tests/generators/resultField'
import {
  flushPromises,
  mount,
  type ComponentMountingOptions,
} from '@vue/test-utils'
import KpiChange from './KpiChange.vue'

//#region SETUP FACTORY
const setup = (props: InstanceType<typeof KpiChange>['$props']) => {
  const mountOptions: ComponentMountingOptions<typeof KpiChange> = {
    props,
    global: {
      stubs: ['IconArrowUp', 'IconArrowDown', 'IconArrowLeft', 'IconWarning'],
    },
  }
  useDisplayCurrency().setDisplayCurrency('EUR')
  const wrapper = mount(KpiChange, mountOptions)

  manufacturingStore.setNodeInt(
    gBomNodeEntity({
      manufacturing: gManufacturing({
        fields: [
          gResultField({ name: 'costPerPart', unit: 'EUR' }),
          gResultField({ name: 'cO2PerPart', unit: 'kgCo2e' }),
        ],
      }),
    })
  )

  //#region HELPERS
  const getCostText = () => wrapper.find(dataTest('kpi-change-cost')).text()
  const getCo2Text = () => wrapper.find(dataTest('kpi-change-co-2')).text()
  //#endregion HELPERS

  return {
    getCostText,
    getCo2Text,
  }
}
//#endregion SETUP FACTORY

//#region TESTS
it('displays expected changes', async () => {
  const { getCostText, getCo2Text } = setup({
    kpi: {
      costPerPart: gKpiChange({
        newValue: gCurrencyInfo({ EUR: 1 }),
        oldValue: gCurrencyInfo({ EUR: 4 }),
        percentageDifference: 10,
      }),
      co2PerPart: gKpiChange({
        newValue: 5,
        oldValue: 2,
        percentageDifference: 5,
      }),
    },
  })
  await flushPromises()

  const costValue = '-3.00 EUR'
  const costPercent = '10.00 %'
  const co2Value = '+3.00 kgCo2e'
  const co2Percent = '5.00 %'
  expect(getCostText()).toBe(`statics.cost:${costValue}${costPercent}`)
  expect(getCo2Text()).toBe(`statics.co2:${co2Value}${co2Percent}`)
})

describe('Broken state', () => {
  it('display broken calculation history info', () => {
    const { getCo2Text, getCostText } = setup({
      kpi: {
        costPerPart: gKpiChange({
          newValue: gCurrencyInfo({ EUR: 1 }),
          oldValue: gCurrencyInfo({ EUR: 4 }),
          percentageDifference: 10,
          broken: true,
        }),
        co2PerPart: gKpiChange({
          newValue: 5,
          oldValue: 2,
          percentageDifference: 5,
          broken: true,
        }),
      },
    })
    expect(getCostText()).toBe(`statics.cost: ----`)
    expect(getCo2Text()).toBe(`statics.co2: ----`)
  })
})
//#endregion TESTS
