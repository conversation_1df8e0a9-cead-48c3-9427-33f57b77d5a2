<template>
  <HistoryItem v-data-test v-bind="historyItemProp">
    <template #difference>
      <KpiChange
        :kpi="change.kpi"
        :dirty-child-loading="change.dirtyChildLoading"
      />
      <p v-if="isOnCalculation" class="text-body-semibold">
        {{ $t('statics.calculation') }}
      </p>
    </template>
  </HistoryItem>
</template>

<script setup lang="ts">
import { $t } from '@shared/translation/nuTranslation'
import { iconGetter } from '@tset/shared-utils/helpers/general'
import { computed } from 'vue'
import { getHistoryPath } from '../../../history/history.utils'
import HistoryItem from '../HistoryItem.vue'
import KpiChange from '../KpiChange.vue'

//#region PROPS
const props = defineProps<{
  change: HistoryComponentChangeDTO<HistorySingleEntryType>
}>()
//#endregion PROPS
const data = computed(() => props.change.changes.data)
const isOnCalculation = computed(() => {
  return (data.value?.path ?? []).length < 2
})
const historyItemProp = computed(() => {
  const { partInfo, entityDisplayName, path, entityType } = data.value
  const title =
    partInfo?.partName ??
    entityDisplayName ??
    props.change.partInfo?.partName ??
    ''
  return {
    path: getHistoryPath(path),
    title: title,
    entityIcon: iconGetter(
      path?.at(-1)?.entityType ?? entityType ?? 'Calculation'
    ),
    action: $t(`newHistory.${props.change.changes.triggerType}`),
  }
})
</script>
<script lang="ts">
export default {
  name: 'GenericHistoryChange',
}
</script>
