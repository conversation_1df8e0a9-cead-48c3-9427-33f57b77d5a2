<template>
  <HistoryItem
    v-data-test
    :action="$translateWithFallback(`history.${triggerType}`, '')"
  >
    <template v-if="showDifference" #difference>
      <KpiChange
        :kpi="change.kpi"
        :dirty-child-loading="change.dirtyChildLoading"
      />
    </template>
    <TsetTranslate
      :keypath="translationLabel"
      tag="div"
      class="text-body-light flex flex-wrap items-center gap-5 text-black-default"
    >
      <template #newValue>
        <HistoryItem :title="values.new" />
      </template>
      <template #oldValue>
        <HistoryItem :title="values.old" />
      </template>
      <template #source>
        <TsetBadge :label="values.source" type="accent1" />
      </template>
    </TsetTranslate>
  </HistoryItem>
</template>

<script setup lang="ts">
import { $translateWithFallback } from '@shared/translation/nuTranslation'
import TsetBadge from '@tset/design/atoms/TsetBadge'
import TsetTranslate from '@tset/shared-ui/TsetTranslate/TsetTranslate.vue'
import { isResultField } from '@tset/shared-utils/helpers/typeGuard/isResultField'
import { computed } from 'vue'
import { hideChangeKpi } from '../../../history/history.utils'
import HistoryItem from '../HistoryItem.vue'
import KpiChange from '../KpiChange.vue'

//#region PROPS
const props = defineProps<{
  change: HistoryComponentChangeDTO<HistorySingleEntryType>
}>()
//#endregion PROPS

const triggerType = computed(() => props.change.changes.triggerType)

const showDifference = computed(() => !hideChangeKpi(triggerType.value))

const translationLabel = computed(() => {
  return (
    {
      BomPublish: 'history.replacedAsMain',
      RenameBranch: 'history.renamedFrom',
      SavePublicVariant: 'history.createdFrom',
      UnpublishBranch: 'history.replacedAsMain',
      MergeAction: 'history.updatedCalculationFrom',
      BomTypeChange: 'history.replacedBy',
      SubBomTypeChange: 'history.replacedBy',
      RootManufacturingChange: 'history.calculationChanged',
    }[triggerType.value.toString()] ?? ''
  )
})

const values = computed(() => {
  const { oldValue: oldFieldOrValue, fieldValue: newFieldOrValue } =
    props.change.changes.data
  const newFieldValue = isResultField(newFieldOrValue)
    ? newFieldOrValue.value
    : newFieldOrValue
  const oldFieldValue = isResultField(oldFieldOrValue)
    ? oldFieldOrValue.value
    : oldFieldOrValue

  const newValue = fieldTranslate(newFieldValue?.toString())
  const oldValue = fieldTranslate(oldFieldValue?.toString())
  return { new: newValue, old: oldValue, source: newValue }
})

function fieldTranslate(value: string | undefined) {
  return $translateWithFallback(`fields.${value}`, value ?? '')
}
</script>

<script lang="ts">
export default {
  name: 'VariantBomChange',
}
</script>
