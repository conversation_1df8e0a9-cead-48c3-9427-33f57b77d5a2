<template>
  <HistoryItem v-data-test v-bind="historyItemProps">
    <template #difference>
      <KpiChange
        :kpi="change.kpi"
        :dirty-child-loading="change.dirtyChildLoading"
      />
    </template>
    <FieldValueChange v-bind="fieldChangeProps" />
  </HistoryItem>
</template>

<script setup lang="ts">
import {
  getHistoryPath,
  makeResultField,
} from '@calculation/sidepanel/history/history.utils'
import { $nutLabel, $t } from '@shared/translation/nuTranslation'
import { getStepEntityDisplayDesignation } from '@tset/shared-utils/helpers/field'
import { iconGetter } from '@tset/shared-utils/helpers/general'
import { newField } from '@tset/shared-utils/helpers/manufacturing'
import { isResultField } from '@tset/shared-utils/helpers/typeGuard/isResultField'
import { computed } from 'vue'
import FieldValueChange from '../FieldValueChange.vue'
import HistoryItem from '../HistoryItem.vue'
import KpiChange from '../KpiChange.vue'

const props = defineProps<{
  change: HistoryComponentChangeDTO<HistorySingleEntryType>
}>()

const data = computed(() => props.change.changes.data)

const fieldChangeProps = computed(() => {
  const field = data.value.fieldValue ?? ''
  const name = data.value.fieldName ?? ''

  let updatedField
  if (!isResultField(field)) {
    updatedField = newField(name, '')
  } else if (name === field.name) {
    updatedField = field
  } else {
    // field is overwritten
    updatedField = newField(name, '', field.type, field.metaInfo)
  }

  return {
    label: $nutLabel(updatedField),
    previous: makeResultField(data.value.oldValue ?? '', name),
    current: makeResultField(field, name),
  }
})

const historyItemProps = computed(() => {
  const path = data.value.path
  const lastPathItem = path?.at(-1)
  return {
    path: getHistoryPath(path),
    title: lastPathItem
      ? getStepEntityDisplayDesignation(lastPathItem.displayDesignation)
      : $t('entityTypes.standardCalculation'),
    entityIcon: iconGetter(
      lastPathItem?.entityType ?? data.value.entityType ?? ''
    ),
  }
})
</script>

<script lang="ts">
export default {
  name: 'FieldChangeTriggerItem',
}
</script>
