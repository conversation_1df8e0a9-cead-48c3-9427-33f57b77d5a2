import { gHistoryNodeTrigger } from '@tset/shared-utils/tests/generators/historyNodeTrigger'
import { gPerPartDefinition } from '@tset/shared-utils/tests/generators/perPartDefinition'
import { mount, type ComponentMountingOptions } from '@vue/test-utils'
import CompositeTriggerList from './CompositeTriggerList.vue'

//#region GENERATORS
const generateChanges = (
  triggerType: CompositeTriggerChildType,
  length: number
): HistoryMultiEntryType => {
  return {
    triggerType: 'CompositeTriggerAction',
    data: {
      triggers: Array.from(Array(length)).map(() => ({
        data: gHistoryNodeTrigger(),
        triggerType,
      })),
    },
  }
}
const kpi = (): HistoryKpiPerPart => {
  return {
    co2PerPart: gPerPartDefinition<number>(),
    costPerPart: gPerPartDefinition<CurrencyInfo>(),
  }
}
//#endregion GENERATORS

//#region SETUP FACTORY
const setup = (props: InstanceType<typeof CompositeTriggerList>['$props']) => {
  const mountOptions: ComponentMountingOptions<typeof CompositeTriggerList> = {
    props,
    global: {
      stubs: [
        'IconChevronLeft',
        'IconWarning',
        'IconArrowLeft',
        'IconArrowDown',
        'IconArrowUp',
      ],
    },
  }

  const wrapper = mount(CompositeTriggerList, mountOptions)

  //#region HELPERS
  const itemListLength = () =>
    wrapper.findAll(
      '[data-test=composite-trigger-list] [data-test=composite-trigger-list-item]'
    ).length

  const fullItemListLength = () =>
    wrapper.findAll(
      '[data-test=composite-trigger-list-details] [data-test=composite-trigger-list-item]'
    ).length
  const showAllButtonExists = () =>
    wrapper.find('[data-test="composite-trigger-list-show-all"]').exists()
  const clickShowAllButton = () =>
    wrapper
      .find('[data-test="composite-trigger-list-show-all"]')
      .trigger('click')
  //#endregion HELPERS

  return {
    itemListLength,
    fullItemListLength,
    showAllButtonExists,
    clickShowAllButton,
  }
}
//#endregion SETUP FACTORY

//#region TESTS
describe('CompositeTriggerList', () => {
  describe('List', () => {
    describe('renders all changes when there are ', () => {
      it('- less than 5 changes ', () => {
        const changes = generateChanges('EntityDeletion', 3)
        const { itemListLength } = setup({
          change: {
            dirtyChildLoading: true,
            kpi: kpi(),
            changes,
            partInfo: undefined,
          },
          isBomCreation: false,
        })
        expect(changes.data.triggers.length).toEqual(itemListLength())
      })
      it('- 5 changes', () => {
        const changes = generateChanges('EntityCreation', 5)
        const { itemListLength } = setup({
          change: {
            dirtyChildLoading: true,
            kpi: kpi(),
            changes,
            partInfo: undefined,
          },
          isBomCreation: false,
        })
        expect(changes.data.triggers.length).toEqual(itemListLength())
      })
    })
    describe('truncates the list when there are ', () => {
      it('- more than 5 changes', () => {
        const changes = generateChanges('BomCreation', 6)
        const { itemListLength } = setup({
          change: {
            changes,
            kpi: kpi(),
            dirtyChildLoading: false,
            partInfo: undefined,
          },
          isBomCreation: false,
        })

        expect(itemListLength()).toEqual(5)
      })

      it('- a lof of changes', () => {
        const changes = generateChanges('BomCreation', 15)
        const { itemListLength } = setup({
          change: {
            changes,
            kpi: kpi(),
            dirtyChildLoading: false,
            partInfo: undefined,
          },
          isBomCreation: false,
        })

        expect(itemListLength()).toEqual(5)
      })
    })
  })
  describe('Show all button', () => {
    describe('is hidden when there are ', () => {
      it('- less than 5 changes in list', () => {
        const changes = generateChanges('CrossProjectEntityCopy', 3)
        const { showAllButtonExists } = setup({
          isBomCreation: false,
          change: {
            changes,
            kpi: kpi(),
            dirtyChildLoading: false,
            partInfo: undefined,
          },
        })

        expect(showAllButtonExists()).toBe(false)
      })
      it('- 5 changes in list', () => {
        const changes = generateChanges('CrossProjectEntityCopy', 5)
        const { showAllButtonExists } = setup({
          isBomCreation: false,
          change: {
            changes,
            kpi: kpi(),
            dirtyChildLoading: false,
            partInfo: undefined,
          },
        })

        expect(showAllButtonExists()).toBe(false)
      })
    })
    describe('is displayed when there are', () => {
      it('- more than 5 changes in list', () => {
        const changes = generateChanges('BomCreation', 6)
        const { showAllButtonExists } = setup({
          isBomCreation: false,
          change: {
            changes,
            kpi: kpi(),
            dirtyChildLoading: false,
            partInfo: undefined,
          },
        })
        expect(showAllButtonExists()).toBe(true)
      })
      it('- a lot of changes in list', () => {
        const changes = generateChanges('BomCreation', 10)
        const { showAllButtonExists } = setup({
          isBomCreation: false,
          change: {
            changes,
            kpi: kpi(),
            dirtyChildLoading: false,
            partInfo: undefined,
          },
        })
        expect(showAllButtonExists()).toBe(true)
      })
    })

    describe('Clicking the Show all button', () => {
      it('- renders all changes', async () => {
        const length = 10
        const changes = generateChanges('BomCreation', length)
        const { clickShowAllButton, fullItemListLength } = setup({
          isBomCreation: false,
          change: {
            changes,
            kpi: kpi(),
            dirtyChildLoading: false,
            partInfo: undefined,
          },
        })
        await clickShowAllButton()
        expect(fullItemListLength()).toBe(length)
      })
    })
  })
})
//#endregion TESTS
