import {
  useSidePanelController,
  type SidePanelTab,
} from '@calculation/sidepanel/useSidePanelController'
import { getStepEntityDisplayDesignation } from '@tset/shared-utils/helpers/field'
import { newField } from '@tset/shared-utils/helpers/manufacturing'
import { isResultField } from '@tset/shared-utils/helpers/typeGuard/isResultField'
import type { InjectionKey, Ref } from 'vue'
import { computed } from 'vue'

export const HistoryStateKey = Symbol() as InjectionKey<Ref<HistoryState>>

export function isBomCreation(trigger: HistoryTriggerDTO) {
  if (isCompositeTriggerType(trigger.triggerType)) {
    // Added for BCT where the Composite child can also be toplevel
    return trigger.triggerType === 'BomCreation'
  }
  return (
    trigger.triggerType === 'CompositeTriggerAction' &&
    trigger.data.triggers.every(
      ({ triggerType }) => triggerType === 'BomCreation'
    )
  )
}

export function makeResultField(
  value: ResultField | string,
  name: string = ''
): ResultField {
  return isResultField(value) ? value : newField(name, value)
}

// #region Trigger Check
export function isCompositeBulkChange(
  change: HistoryTriggerDTO
): change is HistoryMultiEntryType {
  return change.triggerType === 'CompositeTriggerAction'
}

export function isFieldValueChange(triggerType: TopLevelTriggerType) {
  const fieldChangeType: TopLevelTriggerType[] = [
    'FieldValueChange',
    'FieldValueChangeWithCurrency',
    'SetResponsible',
    'FieldUnlock',
    'MainImageChange',
  ]
  return fieldChangeType.includes(triggerType)
}

export function isVariantBomChange(triggerType: TopLevelTriggerType) {
  const variantChangeType: TopLevelTriggerType[] = [
    /// variant changes
    'RenameBranch',
    'BomPublish',
    'UnpublishBranch',
    'SavePublicVariant',
    /// bom changes
    'RootManufacturingChange',
    'BomTypeChange',
    'SubBomTypeChange',
    'MergeAction',
  ]
  return variantChangeType.includes(triggerType)
}

export function hideChangeKpi(triggerType: TopLevelTriggerType | undefined) {
  if (!triggerType) {
    return false
  }
  const variantChangeType: TopLevelTriggerType[] = [
    'RenameBranch',
    'BomPublish',
    'UnpublishBranch',
    'SavePublicVariant',
  ]
  return variantChangeType.includes(triggerType)
}

export function isBulkActionChange(triggerType: TopLevelTriggerType) {
  return triggerType === 'BulkActionTrigger'
}

export function isCompositeTriggerType(
  triggerType: string
): triggerType is CompositeTriggerChildType {
  const compositeTypes: CompositeTriggerChildType[] = [
    'EntityCreation',
    'EntityDeletion',
    'DuplicateEntity',
    'BomCreation',
    'CrossProjectEntityCopy',
  ]
  return compositeTypes.includes(triggerType as any)
}
//endregion Trigger Check

export function getHistoryPath(path: HistoryPath[] | undefined): string {
  if ((path ?? []).length > 1) {
    return (
      path
        ?.map((p) => getStepEntityDisplayDesignation(p.displayDesignation))
        .join(' / ') ?? ''
    )
  }
  return ''
}

export function convertSingleToMultiHistoryType(
  triggerType: CompositeTriggerChildType,
  item: HistoryNodeChange
): CompositeChildTriggerDTO {
  return {
    data: { ...item.trigger.data, partInfo: item.partInfo },
    triggerType: triggerType,
  }
}

export function getHistoryItemDTO(node: BomNodeHistoryDto): HistoryItemDTO {
  const { kpi, nodeChanges: changes } = node
  const { timestamp, createdBy, changesetId } = changes[0]
  return {
    createdAt: timestamp.toString(),
    createdBy: createdBy?.name ?? '',
    changesetId,
    kpi,
    changes,
  }
}

export function useHistorySidePanel(sidePanelTab?: Required<SidePanelTab>) {
  const sidePanel = useSidePanelController(sidePanelTab)

  const isActive = computed(() => sidePanel.isActive())

  function toggle(key?: string) {
    sidePanel.toggle(key)
  }
  function open() {
    if (!isActive.value) {
      toggle()
    }
  }
  function close() {
    sidePanel.close()
  }

  return { isActive, open, close, toggle }
}
