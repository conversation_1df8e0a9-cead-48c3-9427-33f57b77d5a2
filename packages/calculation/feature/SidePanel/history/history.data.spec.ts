import { gBranch } from '@tset/shared-model/calculation/Branch'
import { InfiniteStatus } from '@tset/shared-ui/utilities/TsetInfiniteLoader'
import { gBomNodeEntity } from '@tset/shared-utils/tests/generators/bomNodeEntity'
import { gBomNodeHistoryDto } from '@tset/shared-utils/tests/generators/bomNodeHistoryDto'
import { gHistoryNodeChange } from '@tset/shared-utils/tests/generators/historyNodeChange'
import { withAxiosMock } from '@tset/shared-utils/tests/mocks/withAxiosMock'
import { flushPromises } from '@vue/test-utils'
import { computed, nextTick, ref } from 'vue'
import { useCalculationHistory } from './history.data'

//#region MOCKS
const { getLastCall, setMockResponse, getCallsCount, clearAllMocks } =
  withAxiosMock()
//#endregion MOCKS

//#region SETUP FACTORY
const setup = ({
  firstResponse = [
    gBomNodeHistoryDto({ nodeChanges: [gHistoryNodeChange()] }),
    gBomNodeHistoryDto({ nodeChanges: [gHistoryNodeChange()] }),
  ],
  node = {} as Partial<BomNodeEntity>,
} = {}) => {
  clearAllMocks()
  setMockResponse(firstResponse)
  const _projectId = ref('project-id')
  const bomNode = ref(gBomNodeEntity(node))
  const { saved, fetchSavedNext, unsaved, savedInfiniteStatus } =
    useCalculationHistory(
      bomNode,
      computed(() => ({
        bomNodeId: bomNode.value.id,
        branch: bomNode.value.branch,
        projectId: _projectId.value,
        bomNodeIsLoading: false,
      }))
    )

  //#region HELPERS
  const branchId = () => bomNode.value.branch.id
  const bomNodeId = () => bomNode.value.id
  const projectId = () => _projectId.value

  const updateBomNode = async () => {
    bomNode.value = gBomNodeEntity({})
    await nextTick()
  }
  const fetchNext = async () => {
    await fetchSavedNext()
  }

  const getSavedDataLength = () => saved.value.length
  const getUnsavedDataLength = () => (unsaved.value?.changes ?? []).length
  const firstRequestBody = () => [
    `/api/history/node/${projectId()}/${bomNodeId()}?includeChildren=true&includeParents=true&includePublish=true&type=ALL&limit=20&branch=${branchId()}`,
    {
      signal: new AbortController().signal,
    },
  ]
  //#endregion HELPERS

  return {
    when: { updateBomNode, fetchNext },
    then: {
      firstRequestBody,
      getSavedDataLength,
      getUnsavedDataLength,
      savedInfiniteStatus: () => savedInfiniteStatus.value,
    },
  }
}
//#endregion SETUP FACTORY

//#region TESTS
describe('history data', () => {
  describe('when mounted', () => {
    it('- fetches history items', () => {
      const { then } = setup()
      expect(getCallsCount('get')).toBe(1)
      expect(getLastCall('get')).toStrictEqual(then.firstRequestBody())
    })
  })

  describe('when bom node changes', () => {
    it('- refetches history items', async () => {
      const { when, then } = setup()
      await when.updateBomNode()
      expect(getCallsCount('get')).toBe(2)
      expect(getLastCall('get')).toStrictEqual(then.firstRequestBody())
    })
  })

  describe('when fetching the next items', () => {
    it('- appends to existing history items', async () => {
      const { when, then } = setup({
        firstResponse: [
          gBomNodeHistoryDto({ nodeChanges: [gHistoryNodeChange()] }),
          gBomNodeHistoryDto({ nodeChanges: [gHistoryNodeChange()] }),
        ],
      })
      await flushPromises()
      expect(then.getSavedDataLength()).toBe(2)
      setMockResponse([
        gBomNodeHistoryDto({ nodeChanges: [gHistoryNodeChange()] }),
        gBomNodeHistoryDto({ nodeChanges: [gHistoryNodeChange()] }),
      ])
      await when.fetchNext()
      expect(then.getSavedDataLength()).toBe(4)
    })
  })

  describe('when branch is not a published branch', () => {
    it('- has unsaved data', async () => {
      const branch = gBranch({
        global: false,
      })
      const { then } = setup({
        node: { branch },
        firstResponse: [
          gBomNodeHistoryDto({
            branchId: branch.id,
            nodeChanges: [gHistoryNodeChange()],
          }),
        ],
      })
      await flushPromises()
      expect(then.getUnsavedDataLength()).toBe(1)
    })
  })
  describe('when branch is a published branch', () => {
    it('- has no unsaved data', async () => {
      const branch = gBranch({
        global: true,
      })
      const { then } = setup({
        node: { branch },
        firstResponse: [
          gBomNodeHistoryDto({
            branchId: branch.id,
            nodeChanges: [gHistoryNodeChange()],
          }),
        ],
      })
      await flushPromises()
      expect(then.getUnsavedDataLength()).toBe(0)
    })
  })
  describe('Infinite fetch status', () => {
    describe('when last fetch is empty', () => {
      it('- has no more', async () => {
        const { when, then } = setup()
        await flushPromises()
        setMockResponse([])
        await when.fetchNext()
        expect(then.savedInfiniteStatus()).toBe(InfiniteStatus.NO_MORE)
      })
    })
    describe('when first fetch is empty', () => {
      it('- has no results ', async () => {
        const { then } = setup({ firstResponse: [] })
        await flushPromises()
        expect(then.savedInfiniteStatus()).toBe(InfiniteStatus.NO_RESULTS)
      })
    })
    describe('while fetching next', () => {
      it('- is loading', async () => {
        const { when, then } = setup()
        await flushPromises()
        setMockResponse(
          [...Array(20)].map(() =>
            gBomNodeHistoryDto({ nodeChanges: [gHistoryNodeChange()] })
          )
        )
        when.fetchNext()
        expect(then.savedInfiniteStatus()).toBe(InfiniteStatus.LOADING)
        await flushPromises()
        expect(then.savedInfiniteStatus()).toBe(InfiniteStatus.IDLE)
      })
    })
  })
})
//#endregion TESTS
