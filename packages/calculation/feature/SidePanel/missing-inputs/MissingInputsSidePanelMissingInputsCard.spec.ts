import { i18n } from '@shared/translation/nuTranslation'
import { PAGE } from '@tset/shared-model/navigation/navigation'
import NuUpdate<PERSON>ield from '@tset/shared-ui/parts/fields/NuUpdateField.vue'
import { gResultField } from '@tset/shared-utils/tests/generators/resultField'
import { mount, type ComponentMountingOptions } from '@vue/test-utils'
import MissingInputsSidePanelMissingInputsCard from './MissingInputsSidePanelMissingInputsCard.vue'

//#region TESTS
describe('Test component functionality', () => {
  describe('max fields', () => {
    i18n.setLocaleMessage('en', {
      statics: { modularization: { moreMissingFields: 'missing {count}' } },
    })
    it('show max of 3 fields', () => {
      const { getDisplayedFields, getMoreFieldText } = given({
        fields: [
          gResultField({ name: 'one' }),
          gResultField({ name: 'two' }),
          gResultField({ name: 'three' }),
          gR<PERSON><PERSON><PERSON>ield({ name: 'four' }),
          gResultField({ name: 'five' }),
        ],
      })
      expect(getDisplayedFields()).toStrictEqual(['one', 'two', 'three'])
      expect(getMoreFieldText()).toBe('missing 2')
    })
    it('shows as much of entity ref fields as available', () => {
      const { getDisplayedFields, getMoreFieldText } = given({
        fields: [
          gResultField({ name: 'one' }),
          gResultField({ name: 'two' }),
          gResultField({ name: 'three', type: 'EntityRef' }),
          gResultField({ name: 'four' }),
          gResultField({ name: 'five', type: 'EntityRef' }),
          gResultField({ name: 'six' }),
          gResultField({ name: 'seven' }),
          gResultField({ name: 'eight' }),
          gResultField({ name: 'nine', type: 'EntityRef' }),
          gResultField({ name: 'ten', type: 'EntityRef' }),
          gResultField({ name: 'eleven' }),
        ],
      })
      expect(getDisplayedFields()).toStrictEqual([
        'three',
        'five',
        'nine',
        'ten',
      ])
      expect(getMoreFieldText()).toBe('missing 7')
    })
  })
  describe('fields sorting', () => {
    it('sort fields by index', () => {
      const { getDisplayedFields } = given({
        fields: [
          gResultField({ name: 'pink', metaInfo: { fieldIndex: 10 } }),
          gResultField({ name: 'yellow', metaInfo: { fieldIndex: 45 } }),
          gResultField({ name: 'red', metaInfo: { fieldIndex: 2 } }),
          gResultField({ name: 'green', metaInfo: { fieldIndex: 55 } }),
        ],
      })
      expect(getDisplayedFields()).toStrictEqual(['red', 'pink', 'yellow'])
    })
    it('Entity ref fields has a higher sort preference', () => {
      const { getDisplayedFields } = given({
        fields: [
          gResultField({
            name: 'pink',
            type: 'EntityRef',
            metaInfo: { fieldIndex: 10 },
          }),
          gResultField({ name: 'yellow', metaInfo: { fieldIndex: 45 } }),
          gResultField({ name: 'red', metaInfo: { fieldIndex: 2 } }),
          gResultField({
            name: 'green',
            type: 'EntityRef',
            metaInfo: { fieldIndex: 55 },
          }),
        ],
      })
      expect(getDisplayedFields()).toStrictEqual(['pink', 'green', 'red'])
    })
  })
})
//#endregion TESTS

//#region SETUP FACTORY
const given = ({ fields }: { fields: ResultField[] }) => {
  const props: InstanceType<
    typeof MissingInputsSidePanelMissingInputsCard
  >['$props'] = {
    section: {
      fieldName: '',
      page: PAGE.MANU_BOM_ENTRYDETAIL,
      id: 'entry',
      title: 'entry-title',
      fields,
      type: 'modal',
      modalOpener: () => {},
    },
  }
  const mountOptions: ComponentMountingOptions<
    typeof MissingInputsSidePanelMissingInputsCard
  > = {
    props,
    global: {
      stubs: ['NuUpdateField', 'NuNavigationLinik', 'ManuEntityLink'],
      provide: {
        context: 'calculation',
      },
    },
  }

  const wrapper = mount(MissingInputsSidePanelMissingInputsCard, mountOptions)
  const prefix = (str: string) =>
    'missing-inputs-side-panel-missing-inputs-card-' + str
  //#region HELPERS
  const getDisplayedFields = () =>
    wrapper
      .findByDataTest(prefix('fields'))
      .findAll('div')
      .map((d) => {
        const upd = d.findComponent(NuUpdateField)
        return upd.exists() ? upd.props('field').name : d.text()
      })

  const getMoreFieldText = () =>
    wrapper.findByDataTest(prefix('more-fields')).text()
  //#endregion HELPERS

  return {
    getMoreFieldText,
    getDisplayedFields,
  }
}
//#endregion SETUP FACTORY
