import { manufacturingStore } from '@domain/calculation/manufacturing.store'
import { useCalculationMissingFields } from '@domain/calculation/utils/useCalculationIssues'
import NotificationDot from '@tset/design/atoms/NotificationDot/NotificationDot.vue'
import { MergeSource } from '@tset/shared-model/calculation/Merge'
import { gBomNodeEntity } from '@tset/shared-utils/tests/generators/bomNodeEntity'
import { gResultField } from '@tset/shared-utils/tests/generators/resultField'
import type { DOMWrapper } from '@vue/test-utils'
import { mount } from '@vue/test-utils'
import { computed } from 'vue'
import ManuHeaderToolbar from './ManuHeaderToolbar.vue'
import { useSidePanelController } from './useSidePanelController'

describe('ManuHeaderToolbar', () => {
  describe('Tabs', () => {
    describe('Comments', () => {
      describe('Toggle Notes', () => {
        test('changes status of notes box when clicked', async () => {
          const { clickTab, notesTab, panelIsOpened, activePanel } = setup()
          await clickTab(notesTab)
          expect(panelIsOpened()).toBeTruthy()
          expect(activePanel()).toContain('calculationNotes')
        })

        test('displays if there are notes', () => {
          const { notesTab } = setup({
            userNotes: 'some comments here',
          })
          expect(notesTab().hasAlert).toBeTruthy()
        })

        test('no alert if there are no notes', () => {
          const { notesTab } = setup({
            userNotes: '',
          })
          expect(notesTab().hasAlert).toBeFalsy()
        })
      })
    })

    describe('Conflicts', () => {
      describe('With conflicts', () => {
        const shouldShowNotificationIcon = (mergeSource: MergeSource) => {
          const { conflictsTab } = setup({
            openMergesAvailable: [mergeSource],
          })
          expect(
            conflictsTab().hasAlert,
            `fails if icon is not displayed for ${mergeSource} source`
          ).toBeTruthy()
        }

        it('- shows notification icon for merge source: BRANCH', () =>
          shouldShowNotificationIcon(MergeSource.BRANCH))
        it('- shows notification icon for merge source: MASTER', () =>
          shouldShowNotificationIcon(MergeSource.MASTER))
        it('- shows notification icon for merge source: MASTERDATA', () =>
          shouldShowNotificationIcon(MergeSource.MASTERDATA))
        it('- shows notification icon for merge source: MASTERDATA_CHILDREN', () =>
          shouldShowNotificationIcon(MergeSource.MASTERDATA_CHILDREN))
      })
      describe('Clicking the Button', () => {
        describe('Panel currently opened with a different section', () => {
          it('- opens side panel for update', async () => {
            const { conflictsTab, clickTab, panelIsOpened, activePanel } =
              setup({
                openedPanel: 'exports',
              })
            expect(activePanel()).toContain('export')
            await clickTab(conflictsTab)
            expect(panelIsOpened()).toBe(true)
            expect(activePanel()).toContain('conflicts')
          })
        })
        describe('Panel is closed', () => {
          it('- opens side panel for update', async () => {
            const { conflictsTab, clickTab, panelIsOpened, activePanel } =
              setup({})
            await clickTab(conflictsTab)
            expect(panelIsOpened()).toBe(true)
            expect(activePanel()).toContain('conflicts')
          })
        })
        describe('Panel is currently opened with conflict', () => {
          it('- closes the panel', async () => {
            const { conflictsTab, clickTab, panelIsOpened } = setup({
              openedPanel: 'conflicts',
            })
            await clickTab(conflictsTab)
            expect(panelIsOpened()).toBe(false)
          })
        })
      })
    })

    describe('Issues', () => {
      // opens and closes
      describe('Toggle Issues panel', () => {
        test('changes status of issues panel when clicked', async () => {
          const { clickTab, issuesTab, panelIsOpened, activePanel } = setup()
          await clickTab(issuesTab)
          expect(panelIsOpened()).toBeTruthy()
          expect(activePanel()).toContain('issues')
          await clickTab(issuesTab)
          expect(panelIsOpened()).toBeFalsy()
        })
      })
      // has notification
      describe('with issues', () => {
        test('has notification indicator', () => {
          const { issuesTab } = setup({ hasIssues: true })
          expect(issuesTab().hasAlert).toBe(true)
        })
      })
      describe('without issues', () => {
        test('has no notification indicator', () => {
          const { issuesTab } = setup({ hasIssues: false })
          expect(issuesTab().hasAlert).toBe(false)
        })
      })
    })

    describe('Exports', () => {
      describe('toggles exports panel', () => {
        test('changes status of exports panel when clicked', async () => {
          const { clickTab, exportsTab, panelIsOpened, activePanel } = setup()
          await clickTab(exportsTab)
          expect(panelIsOpened()).toBeTruthy()
          expect(activePanel()).toContain('export')
          await clickTab(exportsTab)
          expect(panelIsOpened()).toBeFalsy()
        })
      })
    })

    describe('History', () => {
      describe('toggles history panel', () => {
        test('changes status of history panel when clicked', async () => {
          const { clickTab, historyTab, panelIsOpened, activePanel } = setup()
          await clickTab(historyTab)
          expect(panelIsOpened()).toBeTruthy()
          expect(activePanel()).toContain('history')
          await clickTab(historyTab)
          expect(panelIsOpened()).toBeFalsy()
        })
      })
    })
  })
})

//#region SETUP FACTORY
vi.mock('@/api/manufacturing')
vi.mock('@domain/calculation/utils/useCalculationIssues', () => ({
  useCalculationMissingFields: vi.fn(),
}))

const useCalculationMissingFieldsMock = vi.mocked(useCalculationMissingFields)

const setup = ({
  openMergesAvailable = [],
  userNotes,
  openedPanel,
  hasIssues = false,
}: {
  openMergesAvailable?: MergeSource[]
  userNotes?: string
  openedPanel?: 'history' | 'exports' | 'notes' | 'conflicts' | 'issues'
  hasIssues?: boolean
} = {}) => {
  useCalculationMissingFieldsMock.mockImplementation(() => ({
    hasMissingFields: computed(() => hasIssues),
    missingFields: computed(() => []),
    numberOfMissingFields: computed(() => 0),
    hasMissingFieldOnPage: () => false,
  }))
  const bomNodeEntity = gBomNodeEntity({ openMergesAvailable })
  if (typeof userNotes === 'string') {
    bomNodeEntity.manufacturing.fields.push(
      gResultField({
        name: 'userNotes',
        value: userNotes,
      })
    )
  }
  manufacturingStore.setNodeInt(bomNodeEntity)

  const wrapper = mount(ManuHeaderToolbar)

  const controller = useSidePanelController()

  if (openedPanel) {
    controller.open(`calculation-${openedPanel}`)
  } else {
    controller.close()
  }

  // #region Helpers
  const tabButton = (name: string) => {
    const tab = wrapper.findByDataTest(`tset-icon-tab-button-${name}`)
    const hasAlert = tab.findComponent(NotificationDot).exists()
    return { tab, hasAlert }
  }

  const historyTab = () => tabButton('calculation-history')
  const notesTab = () => tabButton('calculation-notes')
  const conflictsTab = () => tabButton('calculation-conflicts')
  const exportsTab = () => tabButton('calculation-exports')
  const issuesTab = () => tabButton('calculation-issues')
  const panelIsOpened = () => controller.isOpened.value
  const activePanel = () => controller.title.value
  const clickTab = (tab: () => { tab: DOMWrapper<Element> }) =>
    tab().tab.trigger('click')
  // #region Helpers

  return {
    clickTab,
    historyTab,
    notesTab,
    conflictsTab,
    exportsTab,
    issuesTab,
    panelIsOpened,
    activePanel,
  }
}
// #endregion SETUP FACTORY
