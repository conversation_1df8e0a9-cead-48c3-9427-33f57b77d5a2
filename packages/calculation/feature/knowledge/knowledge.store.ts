/* eslint-disable max-lines */
import { navigationStore } from '@/store'
import { manufacturingStore } from '@domain/calculation/manufacturing.store'
import {
  getValueForFields,
  populateFieldsWithOldValues,
  updateListOptimistically,
} from '@tset/shared-utils/helpers/field'
import {
  allEntitiesOfType,
  getField,
  newField,
} from '@tset/shared-utils/helpers/manufacturing'
import { Action, Module, Mutation, VuexModule } from 'vuex-module-decorators'
// API Layer
import { createRowsApi } from '@/api/rows'
import { knowledgeModalName, type CTSType } from '@calculation/knowledge'
import type Manufacturing from '@domain/calculation/Manufacturing'
import { $formatField } from '@shared/format/formatField'
import { $nutTextField, $t } from '@shared/translation/nuTranslation'
import {
  createObject,
  fetchFields,
  fetchFieldsWithRefresh,
  finalizeBomUpload,
  getRoughCTSFields,
  getUnitAndTypeByDimension,
  restoreModalField,
  serializeEntityType,
  startBomUpload,
} from '@tset/shared-utils/api/knowledge'
import { useDisplayCurrency } from '@tset/shared-utils/api/useDisplayCurrency'
import {
  isDirectCTSType,
  openCTSModal,
} from '@tset/shared-utils/helpers/cycleTimeSteps'
import { shouldNeverHappen } from '@tset/shared-utils/helpers/typescript'
import Notifications from '@tset/shared-utils/plugins/notifications'
import { useModal } from '@tset/shared-utils/plugins/vue-modal'
import type { RouteLocation } from 'vue-router'
import KnowledgeModal from './KnowledgeModal.vue'

const { displayCurrency } = useDisplayCurrency()
const { hide, show } = useModal()

type MasterdataComposite = Composite & {
  mdClassificationKey?: string
}

@Module({
  stateFactory: true,
  name: 'knowledge',
  namespaced: true,
})
class KnowledgeModule extends VuexModule {
  //#region STATE
  public entityType: ManufacturingDTO['type'] = 'MATERIAL'
  public parentType: string | null = null
  public parentId: string | null = null
  public entityClass: string | null = null
  public fieldsMasterdata: ResultField[] = []
  public fieldsMasterdataOverwritten: ResultField[] = []
  public fieldsManualEntry: ResultField[] = []
  public fieldsUploadBom: ResultField[] = []
  public fieldsCalculationModule: ResultField[] = []
  public uploadBomReplaceCheckbox: boolean = false
  public searchTerm = ''
  public selectedTab: SelectedKnowledgeTab = 'manually'
  public selectedSearchItem: TableRow | null = null
  public availableTechnology: string | null = null
  public selectedTechnology: string | null = null
  public selectedTechnologyClassification: string | null = null
  public selectedMasterdataKey: MasterdataComposite | null = null
  public isLoading: LoadingState = {
    fetchFields: false,
    postObject: false,
    getRows: false,
    name: '',
    state: true,
    lazyLoadRows: false,
    canClick: false,
    reFetchFields: false,
    addObject: false,
  }
  public selectedCycleTimeStepType = 'Standard'
  public activefilterFields: ResultField[] = []
  public savedActivefilterFields: ResultField[] = []
  private filteredFields: ApiFilter = {}
  public page = 0
  private sortBy = 'displayDesignation'
  private orderBy = 'ASC'
  private rowsApi = createRowsApi<MasterdataList>()
  public loadingBomUpload = false
  public fetchNewMasterdataFieldsError: Nullable<string> = null
  public fetchFieldsResponse: Nullable<AddObjectRequest> = null
  public activeManufacturingChild: Nullable<Manufacturing> = null
  public dimension: Nullable<string> = null
  public manufacturingDimension: Nullable<ResultField> = null
  public tabsToShow: Nullable<SelectedKnowledgeTab[]> = null
  public entityClassFilters: string[] = []
  public linkEntityId: Nullable<string> = null
  public linkEntityField: Nullable<string> = null
  public projectId: string = '' // The projectId will always be set directly after loading the project
  private fetchFieldsRequestId: number = 0
  //#endregion STATE

  //#region MUTATIONS
  @Mutation
  setProjectId(projectId: string) {
    this.projectId = projectId
  }

  @Mutation
  setFetchFieldsResponse(payload: Nullable<AddObjectRequest>) {
    this.fetchFieldsResponse = payload
  }

  @Mutation
  setFetchNewMasterdataFieldsError(
    error: typeof this.fetchNewMasterdataFieldsError
  ) {
    this.fetchNewMasterdataFieldsError = error
  }

  @Mutation
  setSelectedMasterdataKey(payload: Nullable<MasterdataComposite>) {
    if (payload) {
      this.selectedMasterdataKey = { ...payload }
    } else {
      this.selectedMasterdataKey = null
    }
  }

  @Mutation
  setLoadingState(payload: { name: keyof LoadingState; state: boolean }) {
    this.isLoading = {
      ...this.isLoading,
      [payload.name]: payload.state,
    }
  }

  @Mutation
  setEntityClass(entityClass: string) {
    this.entityClass = entityClass
  }

  @Mutation
  clearEntityClass() {
    this.entityClass = null
  }

  @Mutation
  setFieldsMasterdata(payload: ResultField[]) {
    this.fieldsMasterdata = updateListOptimistically(
      this.fieldsMasterdata,
      payload
    )
  }

  @Mutation
  setFieldsMasterdataOverwritten(payload: ResultField[]) {
    this.fieldsMasterdataOverwritten = updateListOptimistically(
      this.fieldsMasterdataOverwritten,
      payload
    )
  }

  @Mutation
  setFieldsManualEntry(payload: ResultField[]) {
    const updatedPayload = populateFieldsWithOldValues(
      payload,
      this.fieldsManualEntry
    )

    this.fieldsManualEntry = updateListOptimistically(
      this.fieldsManualEntry,
      updatedPayload
    )
  }

  @Mutation
  setFieldsUploadBom(payload: ResultField[]) {
    this.fieldsUploadBom = payload
  }

  @Mutation
  setFieldsCalculationModule(payload: ResultField[]) {
    this.fieldsCalculationModule = updateListOptimistically(
      this.fieldsCalculationModule,
      payload
    )
  }

  @Mutation
  setSearchTerm(payload: string) {
    this.searchTerm = payload
  }

  @Mutation
  setSelectedTab(name: SelectedKnowledgeTab) {
    this.selectedTab = name
  }

  // TODO T0083 we need better typing for this
  @Mutation
  setEntityType(entityType: ManufacturingEntityType) {
    this.entityType = entityType
  }

  @Mutation
  setParentType(parentType: Nullable<string>) {
    this.parentType = parentType
  }

  @Mutation
  setParentId(parentId: Nullable<string>) {
    this.parentId = parentId
  }

  @Mutation
  setLoadingBomUpload(loading: boolean) {
    this.loadingBomUpload = loading
  }

  @Mutation
  initValues() {
    this.parentType = null
    this.parentId = null
    this.entityClass = null
    this.searchTerm = ''
    this.selectedSearchItem = null
    this.availableTechnology = null
    this.selectedTechnology = null
    this.selectedMasterdataKey = null
    this.selectedCycleTimeStepType = ''
    this.selectedTab = 'manually'
    this.fieldsMasterdata = []
    this.fieldsMasterdataOverwritten = []
    this.fieldsManualEntry = []
    this.fieldsCalculationModule = []
    this.activeManufacturingChild = null
    this.manufacturingDimension = null
  }

  @Mutation
  setEntityFromManuStep(payload: ManufacturingEntityType) {
    this.entityType = payload
  }

  @Mutation
  public setActiveFilterFields(payload: ResultField) {
    this.activefilterFields = this.activefilterFields.map((field) => {
      if (field.name === payload.name) {
        return { ...payload }
      }
      return { ...field }
    })
  }

  @Mutation
  private saveFilters() {
    this.savedActivefilterFields = this.activefilterFields
  }

  @Mutation
  setApiQueryFilter(filter: ApiFilter) {
    this.filteredFields = filter
  }

  @Mutation
  setPage(payload: number) {
    this.page = payload
  }

  @Mutation
  public reinstateFilterFields() {
    this.activefilterFields = this.savedActivefilterFields
  }

  @Action
  public async clearFilterAction() {
    this.saveFilters()
    this.clearFilter()
    return this.getRows()
  }

  @Mutation
  private clearFilter() {
    this.filteredFields = {}
    this.activefilterFields = this.activefilterFields.map((field) => ({
      ...field,
      value: [],
    }))
  }

  @Mutation
  public setUploadBomReplaceCheckbox(val: boolean) {
    this.uploadBomReplaceCheckbox = val
  }

  @Mutation
  setSelectedSearchItem(payload: Nullable<TableRow>) {
    this.selectedSearchItem = payload
  }

  @Mutation
  setAvailableTechnology(payload: Nullable<string>) {
    this.availableTechnology = payload
  }
  @Mutation
  setSelectedTechnology(payload: Nullable<string>) {
    this.selectedTechnology = payload
  }

  @Mutation
  setSelectedTechnologyClassification(payload: Nullable<string>) {
    this.selectedTechnologyClassification = payload
  }

  @Mutation
  public setCycletimeStepType(type: string) {
    this.selectedCycleTimeStepType = type
  }

  @Mutation
  setActiveManufacturingChild(value: Manufacturing | null) {
    this.activeManufacturingChild = value
  }

  @Mutation
  setDimension(value: string) {
    this.dimension = value
  }

  @Mutation
  clearDimension() {
    this.dimension = null
  }

  @Mutation
  setManufacturingDimension(value: Nullable<ResultField>) {
    this.manufacturingDimension = value
  }

  @Mutation
  setTabsToShow(value: typeof this.tabsToShow) {
    this.tabsToShow = value
  }

  @Mutation
  setEntityClassFilters(value: string[]) {
    this.entityClassFilters = value
  }

  @Mutation
  setLinkEntityId(value: Nullable<string>) {
    this.linkEntityId = value
  }

  @Mutation
  setLinkEntityField(value: Nullable<string>) {
    this.linkEntityField = value
  }

  @Mutation
  incrementFetchFieldsRequestId() {
    this.fetchFieldsRequestId += 1
  }
  //#endregion MUTATIONS

  //#region GETTERS
  get correspondingFields(): ResultField[] {
    switch (this.selectedTab) {
      case 'nu-masterdata':
        return [...this.fieldsMasterdata]
      case 'masterdata':
        return [...this.fieldsMasterdata, ...this.fieldsMasterdataOverwritten]
      case 'masterdata-external':
        return [...this.fieldsMasterdata, ...this.fieldsMasterdataOverwritten]
      case 'manually':
        return this.fieldsManualEntry
      case 'uploadBom':
        return this.fieldsUploadBom
      case 'calculationModule':
        if (this.isNewMasterdataTab) {
          return [...this.fieldsMasterdata]
        }
        return this.fieldsCalculationModule
      default:
        throw shouldNeverHappen(
          'KnowldgeStore: correspondingFields',
          this.selectedTab
        )
    }
  }

  get correspondingFieldsWithoutReadOnly(): ResultField[] {
    return this.correspondingFields.filter((field) => !field.metaInfo?.readOnly)
  }

  get bomNodeId(): string {
    return this.bomNode?.id
  }

  get branch(): Branch {
    return this.bomNode?.branch
  }

  get bomNode(): BomNodeEntity {
    return manufacturingStore.loadedNode!
  }

  get stepRootEntity() {
    return manufacturingStore.stepRootEntity
  }

  get part(): Part {
    if (!this.bomNode) {
      return { number: '', designation: '', id: '' }
    }
    return this.bomNode.manufacturing.part!
  }

  get activeStep() {
    return manufacturingStore.activeStep!
  }

  get stepField(): ResultField {
    return getField(this.activeStep, 'displayDesignation')
  }

  get destinationIcon(): string {
    if (this.activeStep) {
      return 'IconStep'
    }
    if (
      this.entityType === 'CO2_PROCESSING_MATERIAL' ||
      this.entityType === 'COMPONENT_MATERIAL'
    ) {
      return 'IconMaterial'
    }
    return 'IconCalculation'
  }
  get destination(): string {
    if (this.entityType === 'CYCLETIME_STEP') {
      const group = this.activeStep.getEntity(this.parentId!)
      if (group) {
        return group.getField<string>('displayDesignation')?.value ?? ''
      }
    }
    if (!this.stepField.empty) {
      return $formatField(this.stepField, {
        formatZeroValues: true,
        formatEmptyValues: true,
        withUnit: false,
      })
    }
    if (this.entityType === 'CO2_PROCESSING_MATERIAL') {
      return ''
    }
    if (this.entityType === 'COMPONENT_MATERIAL') {
      return this.activeManufacturingChild?.ref ?? ''
    }
    return this.part.designation
  }
  get isDestinationLoading(): boolean {
    return (
      manufacturingStore.soonToBeLoadedBomNodeBranch !== null &&
      manufacturingStore.soonToBeLoadedBomNodeBranch.bomNodeId !==
        this.bomNodeId
    )
  }

  get apiQueryFilter() {
    const filter = {
      ...this.filteredFields,
      dimension:
        this.entityType === 'COMPONENT_MATERIAL' ? this.dimension : null,
      page: [this.page],
      orderBy: [this.orderBy],
      sortBy: [this.sortBy],
      manufacturingDimension: this.manufacturingDimension?.value,
      technology: this.availableTechnology,
    }
    return this.entityType === 'C_PART'
      ? {
          ...filter,
          isMasterDataTab: 'false',
        }
      : filter
  }

  get isNewMasterdataTab(): boolean {
    if (this.selectedTab === 'nu-masterdata') {
      return true
    }
    if (
      this.selectedTab === 'calculationModule' &&
      this.entityType === 'MATERIAL'
    ) {
      return true
    }
    return false
  }

  //#endregion GETTERS

  //#region ACTIONS
  /**
   * This actions reloads the field for the selected entity.
   * Not sure why but it seems like to be a caching / race condition issue.
   */
  @Action
  private async fetchFieldsForNewEntityClass(fields: ResultField[]) {
    // the old class is always undefined for an entity from "nu-masterdata"
    const oldClass = this.correspondingFields.find(
      (field) => field.name == 'entityClass'
    )?.value

    const newClass = fields.find(
      (field: ResultField) => field.name == 'entityClass'
    )?.value
    if (newClass !== oldClass) {
      this.setEntityClass(newClass as string)
    }
    // in case user selected a class but it changed we refetch fields
    let allowSetManualField = this.selectedTab === 'manually'
    if (oldClass && newClass && oldClass != newClass) {
      allowSetManualField = false
      await this.fetchFields()
    }
    return { allowSetManualField }
  }

  @Action
  async knowledgeFieldsOrganizer(payload: {
    fields: ResultField[]
    shouldPrefillDisplayDesignation?: boolean
  }) {
    const { fields, shouldPrefillDisplayDesignation } = payload
    const { allowSetManualField } =
      await this.fetchFieldsForNewEntityClass(fields)
    if (
      this.selectedTab === 'masterdata' ||
      this.selectedTab === 'masterdata-external'
    ) {
      // COST-8264: Prefill manufacturing step displayDesignation
      const prefillField = {
        name: 'displayDesignation',
        value: this.selectedSearchItem?.displayDesignation?.value ?? '',
      }
      const fieldsMasterdata =
        shouldPrefillDisplayDesignation && this.selectedSearchItem
          ? fields.map((field: ResultField) =>
              field.name === prefillField.name
                ? {
                    ...field,
                    value: prefillField.value,
                  }
                : field
            )
          : fields
      this.setFieldsMasterdata(fieldsMasterdata)
    } else if (this.selectedTab === 'uploadBom') {
      this.setFieldsUploadBom(fields)
    } else if (allowSetManualField) {
      this.setFieldsManualEntry(fields)
    }

    if (!shouldPrefillDisplayDesignation) {
      this.setLoadingState({ name: 'canClick', state: true })
    }
  }

  @Action
  async startAddObject(
    request: StartAddObjectRequest,
    preventModalOpening?: boolean
  ): Promise<
    | {
        modalProps: {
          projectId: string
          modalName: string
        }
      }
    | undefined
  > {
    if (request.entityClass) {
      this.setEntityClass(request.entityClass)
    } else {
      this.clearEntityClass()
    }

    this.setEntityType(request.entityType!)
    this.setParentType(
      request.parentType ??
        (manufacturingStore.activeStep ? 'MANUFACTURING_STEP' : null)
    )
    this.setParentId(
      request.parentId ?? manufacturingStore.activeStep?.id ?? null
    )

    if (request.dimension) {
      this.setDimension(request.dimension)
    } else {
      this.clearDimension()
    }

    this.setManufacturingDimension(
      manufacturingStore.loadedManufacturing?.getField<string>('dimension') ??
        null
    )

    if (
      this.entityType === 'MANUFACTURING_STEP' &&
      this.selectedTab == 'calculationModule'
    ) {
      const technology =
        manufacturingStore.loadedManufacturing?.getField<string>(
          'technologyModel'
        )
      if (
        technology != undefined &&
        technology.value !== 'ManualManufacturing'
      ) {
        this.setAvailableTechnology(technology.value)
      }
    }

    this.setTabsToShow(request.tabsToShow ?? null)
    this.setEntityClassFilters(request.entityClassFilters ?? [])
    this.setLinkEntityId(request.linkEntityId ?? null)
    this.setLinkEntityField(request.linkEntityField ?? null)

    if (preventModalOpening) {
      return
    }

    if (request.ctsType && request.parentId) {
      await openCTSModal({
        parentId: request.parentId,
        ctsType: request.ctsType,
      })
      return
    } else {
      return {
        modalProps: {
          projectId: this.projectId,
          modalName: knowledgeModalName,
        },
      }
    }
  }

  @Action
  async fetchFields(payload?: {
    newMasterdata?: { headerKey: string }
    parentId?: string
  }) {
    // Generate unique request ID to validate response and prevent race conditions
    this.incrementFetchFieldsRequestId()
    const requestId = this.fetchFieldsRequestId

    this.setLoadingState({ name: 'fetchFields', state: true })
    this.setLoadingState({ name: 'canClick', state: false })

    await manufacturingStore.subscribeToLoadManufacturing()

    // External Id must be 'undefined' by default, otherwise, it spills towards the 'manualEntry' tab
    let externalId
    if (
      [
        'masterdata',
        'nu-masterdata',
        'masterdata-external',
        'calculationModule',
      ].includes(this.selectedTab)
    ) {
      externalId = this.selectedSearchItem?.externalId
      this.setSelectedMasterdataKey(this.selectedSearchItem?.composite ?? null)
    } else {
      this.setSelectedMasterdataKey(null)
    }

    try {
      const {
        bomNodeId,
        branch,
        entityClass,
        entityType,
        parentId,
        parentType,
        projectId,
        selectedMasterdataKey,
      } = this

      const data = await fetchFields(bomNodeId, branch, projectId, {
        entityClass:
          // We need to disable it for MATERIAL in the calculationModule
          // until also MANUFACTURING_STEPS is migrated, then we can remove it
          this.selectedTab === 'calculationModule' && entityType === 'MATERIAL'
            ? null
            : entityClass,
        entityType,
        parentId,
        parentType: this.isNewMasterdataTab ? parentType : null,
        // this should only be sent for the old masterdata
        selectedMasterdataKey: this.isNewMasterdataTab
          ? null
          : selectedMasterdataKey,
        externalId,
        dirtyChildLoading: true,
        masterdataHeaderKey: payload?.newMasterdata?.headerKey,
      })

      // Check if this request is still the current one before updating state
      if (requestId !== this.fetchFieldsRequestId) {
        return
      }

      this.setFetchFieldsResponse(data)

      if (this.selectedTab === 'manually') {
        this.setFieldsManualEntry(data.fields)
      } else {
        await this.knowledgeFieldsOrganizer({
          fields: data.fields,
          shouldPrefillDisplayDesignation: true,
        })
      }

      return data
    } finally {
      // Only update loading state if this is still the current request
      if (requestId === this.fetchFieldsRequestId) {
        this.setLoadingState({ name: 'fetchFields', state: false })
        this.setLoadingState({ name: 'canClick', state: true })
      }
    }
  }

  @Action
  async fetchRoughCTSFields() {
    this.setLoadingState({ name: 'fetchFields', state: true })
    this.setLoadingState({ name: 'canClick', state: false })

    try {
      const { bomNodeId, branch, parentId, projectId } = this
      const data = await getRoughCTSFields({
        bomNodeId,
        branch,
        projectId,
        parentId,
      })
      this.setFieldsManualEntry(data.fields)
    } finally {
      this.setLoadingState({ name: 'fetchFields', state: false })
      this.setLoadingState({ name: 'canClick', state: true })
    }
  }

  @Action
  async fetchFieldsWithRefresh(payload: {
    fields: ResultField[]
    changedField?: Nullable<ResultField>
  }) {
    this.setLoadingState({ name: 'canClick', state: false })
    this.setLoadingState({ name: 'reFetchFields', state: true })

    try {
      const { fields, changedField } = payload
      const { bomNodeId, branch, projectId, fetchFieldsResponse, parentType } =
        this
      const dto = {
        ...fetchFieldsResponse!,
        fields: fields,
        parentType,
        refreshTrigger: changedField?.name ?? '',
        currency: displayCurrency.value,
      }
      const data = await fetchFieldsWithRefresh(
        bomNodeId,
        branch,
        projectId,
        dto
      )
      this.setFetchFieldsResponse(data)
      this.knowledgeFieldsOrganizer({
        fields: data.fields,
      })
    } finally {
      this.setLoadingState({ name: 'canClick', state: true })
      this.setLoadingState({ name: 'reFetchFields', state: false })
    }
  }

  @Action
  async restoreField(p: { field: ResultField }): Promise<void> {
    this.setLoadingState({ name: 'canClick', state: false })

    try {
      const { field } = p
      const { bomNodeId, branch, projectId } = this

      const dto: AddObjectRequest = {
        fields: this.fieldsManualEntry,
        parentId: this.parentId || '',
        entityClass: this.entityClass || '',
        entityType: this.entityType,
        bomNodeId,
        refreshTrigger: field.name,
      }
      const data = await restoreModalField(
        {
          bomNodeId,
          branchId: branch.id,
          projectId,
        },
        dto
      )
      this.setFieldsManualEntry(data.fields)
    } finally {
      this.setLoadingState({ name: 'canClick', state: true })
    }
  }

  @Action
  /**
   * Handle changing the selected item in the new masterdata select modal
   */
  changeSelectedMasterdataItem(payload: {
    headerKey: string
    headerClassificationKey: string
  }) {
    this.setSelectedSearchItem({
      composite: {
        key: payload.headerKey,
        type: 'NONE',
        year: 0,
        location: 'Global',
        mdClassificationKey: payload.headerClassificationKey,
      } as Composite,
    })
    return this.fetchFields({
      newMasterdata: {
        headerKey: payload.headerKey,
      },
    })
  }

  @Action
  changeSelectedSearchItem(payload: TableRow | null) {
    this.setSelectedSearchItem(payload)
    return this.fetchFields()
  }

  @Action
  initSearch() {
    this.setSelectedSearchItem(null)
    this.setSelectedTechnology(this.availableTechnology)
    this.setSearchTerm('')
    this.clearFilter()
    this.setPage(0)
  }

  @Action
  initStore() {
    this.setFieldsManualEntry([])
    this.setFieldsMasterdata([])
    this.initValues()
  }

  @Action
  initProjectId(projectId: string) {
    this.projectId = projectId
  }

  @Action
  async addObject(closeModal = true) {
    if (!this.isLoading.canClick) {
      return
    }
    const parentId =
      (this.correspondingFields.find((field) => field.metaInfo?.parentEntity)
        ?.value as string) ||
      this.parentId ||
      this.stepRootEntity?.id

    const baseCurrency = this.correspondingFields.find(
      (field) => field.name === 'baseCurrency'
    )?.value as Currency | undefined
    const currency: Currency = baseCurrency ?? displayCurrency.value

    const modularizedTechnology =
      this.selectedTab === 'calculationModule'
        ? this.selectedTechnology
        : undefined

    /**
     * For modularized materials we need to send the entityClass as a field.
     * For the old masterdata and the still existing MANUFACTURING_STEPS this is done in the getFields call,
     * for the new masterdata with modularized calculations, we need to append it here.
     */
    const modularizationFields: ResultField[] = []
    if (
      this.entityClass &&
      this.selectedTab === 'calculationModule' &&
      this.entityType === 'MATERIAL'
    ) {
      modularizationFields.push(
        newField(
          'entityClass',
          this.entityClass,
          'Text',
          undefined,
          undefined,
          'I'
        )
      )
    }

    /**
     * Here we set a default designation if the user adds an empty/manual entity
     * from a NuMasterdata enabled tab.
     */
    if (this.isNewMasterdataTab) {
      const existingDisplayDesignation =
        this.correspondingFieldsWithoutReadOnly.find(
          (f) => f.name === 'displayDesignation'
        )
      if (!existingDisplayDesignation) {
        this.fieldsMasterdata.push(
          newField(
            'displayDesignation',
            $t('knowledge.manualEntityType', {
              entityType: this.entityType,
            })
          )
        )
      } else if (!existingDisplayDesignation?.value) {
        existingDisplayDesignation.value = $t('knowledge.manualEntityType', {
          entityType: this.entityType,
        })
      }
    }

    const externalId = this.selectedSearchItem?.externalId
    const addDTO = {
      fields: [
        ...this.correspondingFieldsWithoutReadOnly,
        ...modularizationFields,
      ],
      masterDataKey: this.selectedMasterdataKey,
      entityType: this.entityType,
      entityClass: this.entityClass,
      parentId:
        this.entityType === 'COMPONENT_MATERIAL' ? this.parentId : parentId,
      currency,
      externalId,
      technology: modularizedTechnology,
      linkEntityId: this.linkEntityId,
      linkEntityField: this.linkEntityField,
    }

    /**
     * This is a strange mapper that combines field values with a currency in some cases.
     * I exclude it for new masterdata because it destroys array values with text and we don't need it there.
     */
    if (!this.isNewMasterdataTab) {
      addDTO.fields = getValueForFields(addDTO.fields, currency)
    }

    this.setLoadingState({ name: 'addObject', state: true })
    manufacturingStore.setManufacturingLoading(true)

    try {
      const designation = this.correspondingFields.find(
        ({ name }) => name === 'displayDesignation'
      )
      const entityClass =
        (this.correspondingFields.find(({ name }) => name === 'entityClass')
          ?.value as string) ?? this.entityClass

      const { bomNodeId, branch, destination, entityType, projectId } = this

      const { data } = await createObject(bomNodeId, branch, projectId, addDTO)

      if (closeModal) {
        await hide(knowledgeModalName)
      }

      await manufacturingStore.setNode({ node: data })
      // reset the tab to default value
      this.setSelectedTab('manually')
      this.initValues()

      if (destination && entityType) {
        const entityTypeTranslated = $t(`entityTypes.${entityType}`)
        const designationTranslated = designation
          ? $nutTextField(designation)
          : ''

        Notifications.success({
          title: $t('notifications.title.addObject', {
            type: entityTypeTranslated,
          }),
          message: $t('statics.addedObjectTo', {
            type: entityTypeTranslated,
            designation: designationTranslated,
            destination,
          }),
        })
      }

      return {
        designation,
        destination,
        entityType,
        entityClass,
        parentId,
        currentId: data.id,
        newObject: data,
      }
    } finally {
      this.setLoadingState({ name: 'addObject', state: false })
      manufacturingStore.setManufacturingLoading(false)
      this.clearEntityClass()
    }
  }

  @Action
  public async getRows({ initial = true }: { initial?: boolean } = {}): Promise<
    PaginatedResult<MasterdataSetup>
  > {
    const name = initial ? 'getRows' : 'lazyLoadRows'
    if (!initial) {
      this.setPage(this.page + 1)
    } else {
      this.setPage(0)
    }
    try {
      this.setLoadingState({ name, state: true })

      const data = await this.rowsApi.getRowsAsync(
        `/api/search/masterdata/${serializeEntityType(this.entityType)}`,
        // TODO: can we extend ApiFilter to cover this case?
        this.apiQueryFilter as unknown as ApiFilter
      )

      if (!data) {
        throw 'request canceled'
      }
      const currentPage = await this.setupData(data.currentPage) // await is necessary
      return { ...data, currentPage }
    } catch {
      return { currentPage: [], totalCount: 0 }
    } finally {
      this.setLoadingState({ name, state: false })
    }
  }

  @Action
  private setupData(rows: MasterdataList[]) {
    return rows.map((row) => {
      const displayDesignation = row.dto.fields.find(
        (field: ResultField) => field.name === 'displayDesignation'
      )
      if (row.type === 'ExternalSourceDataDto') {
        const dto = row.dto
        return {
          isTset: true,
          key: '',
          currentType: '',
          id: dto.externalId.externalId,
          refKey: '',
          composite: undefined,
          fields: dto.fields,
          displayDesignation: displayDesignation!,
          externalId: dto.externalId,
        }
      } else if (row.type === 'MasterDataForEditDto') {
        const dto = row.dto
        return {
          isTset: !!dto.currentGlobalId,
          key: dto.current.composite.key,
          currentType: dto.current.composite.type,
          id: dto.current.id,
          refKey: dto.refKey?.label ?? '',
          composite: dto.current.composite,
          fields: dto.fields,
          displayDesignation: displayDesignation!,
        }
      } else if (row.type === 'ModularizationDto') {
        // takes care of itself in KnowledgeCalculationModuleRight.vue
        return {} as MasterdataSetup
      } else {
        throw shouldNeverHappen(
          'Type missing for knowledgeStore.getRows',
          row.type
        )
      }
    })
  }

  @Action
  async assignApiQueryFilter(filter: ApiFilter) {
    this.saveFilters()
    await this.setApiQueryFilter(filter) // do NOT remove "Await", as the getter for ApiQueryFilter will not be ready
    this.setPage(0)
    return this.getRows()
  }

  @Action
  public async startBomUpload(currentUrl: string) {
    const { entityType, entityClass, projectId, bomNodeId, branch } = this
    const parentId = this.fieldsUploadBom.find(
      (f: ResultField) => f.name === 'stepId'
    )?.value
    const params = [
      { key: 'parentId', value: parentId },
      { key: 'entityType', value: entityType },
      { key: 'entityClass', value: entityClass },
      { key: 'projectId', value: projectId },
      { key: 'bomNodeId', value: bomNodeId },
      { key: 'branchId', value: branch.id },
      { key: 'replace', value: this.uploadBomReplaceCheckbox },
      { key: 'importId', value: '{importId}' },
      { key: 'state', value: '{state}' },
    ]
    const redirectUrl = new URL(currentUrl)
    await params.forEach((q) => {
      redirectUrl.searchParams.set(`${q.key}`, q.value as string)
    })
    const stringUrl = redirectUrl.href
      .toString()
      .replaceAll('%7B', '{')
      .replaceAll('%7D', '}')

    startBomUpload(
      projectId,
      bomNodeId,
      branch,
      parentId,
      entityType,
      entityClass,
      stringUrl
    ).then((r) => {
      window.location.replace(r.importRedirectURLStart)
    })
  }

  @Action({ root: true })
  public async finalizeBomUpload(query: RouteLocation['query']) {
    this.setLoadingBomUpload(true)
    try {
      const data = await finalizeBomUpload(query)
      navigationStore.clearBomUploadQueryParams({
        ...query,
        branch: data.branch.id,
      })
      manufacturingStore.setNodeInt(data)
    } catch {
      navigationStore.clearBomUploadQueryParams(query)
    } finally {
      this.setLoadingBomUpload(false)
    }
  }

  @Action
  public async addAdditionalCTS({
    nextStep,
    ctsType,
    parentId,
    activeStep,
  }: {
    nextStep: 'new' | 'additional'
    parentId?: string
    ctsType: CTSType
    activeStep?: Manufacturing
  }) {
    let objectParentId = parentId
    if (nextStep === 'new') {
      const lastCTSGroup = allEntitiesOfType(
        activeStep!,
        'CYCLETIME_STEP_GROUP'
      ).at(-1)
      if (!lastCTSGroup) {
        return
      }

      objectParentId = lastCTSGroup.id
    }
    await this.startAddObject({
      entityType: 'CYCLETIME_STEP',
      entityClass: 'CycleTimeStep',
      parentId: objectParentId,
      parentType: 'CYCLETIME_STEP_GROUP',
      ctsType: ctsType,
    })

    if (isDirectCTSType(ctsType)) {
      await this.fetchRoughCTSFields()
    } else {
      await this.fetchFields()
    }
  }

  @Action
  async overrideMasterdataDimensions(fields: ResultField[]) {
    const loadedNodeDimension = getField(
      manufacturingStore.loadedManufacturing,
      'dimension'
    ).value as string
    const loadedNodeUnit = await getUnitAndTypeByDimension(loadedNodeDimension)
    const updatedFields = fields
      .filter(
        (field) =>
          // Note: only these two fields can be overwritten (COST-26744)
          field.name === 'partsPerCycle' || field.name === 'setupScrapParts'
      )
      .map((field) => ({
        ...field,
        type: loadedNodeUnit.defaultQuantityUnitType,
        unit: loadedNodeUnit.defaultQuantityUnit,
      }))

    this.setFieldsMasterdataOverwritten(updatedFields)
  }

  @Action
  async openModal(props: { projectId: string; modalName: string }) {
    await show({
      component: KnowledgeModal,
      props,
    })
  }
  //#endregion ACTIONS
}

export default KnowledgeModule
