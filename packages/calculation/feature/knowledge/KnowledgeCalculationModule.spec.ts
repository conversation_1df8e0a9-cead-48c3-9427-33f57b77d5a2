import { knowledgeStore } from '@/store'
import { gMasterdataList } from '@tset/shared-utils/tests/generators/masterdataList'
import { gResultField } from '@tset/shared-utils/tests/generators/resultField'
import { withAxiosMock } from '@tset/shared-utils/tests/mocks/withAxiosMock'
import { mount } from '@vue/test-utils'
import KnowledgeCalculationModule from './KnowledgeCalculationModule.vue'
import KnowledgeLeftFirstStage from './KnowledgeLeftFirstStage.vue'
import KnowledgeLeftSecondStage from './KnowledgeLeftSecondStage.vue'

const ONE_FIRST_STAGE_ROW = [
  {
    dto: {
      key: 'first-id-0',
      fields: [
        gResultField({ name: 'displayDesignation', value: 'first-desig-0' }),
      ],
    },
  },
]

const MULTIPLE_FIRST_STAGE_ROWS = [
  {
    dto: {
      key: 'first-id-0',
      fields: [
        gResultField({ name: 'displayDesignation', value: 'first-desig-0' }),
      ],
      entityClass: 'testEntityClass',
    },
  },
  {
    dto: {
      key: 'first-id-1',
      fields: [
        gRes<PERSON><PERSON>ield({ name: 'displayDesignation', value: 'first-desig-1' }),
      ],
      entityClass: 'testEntityClass',
    },
  },
]

const SECOND_STAGE_ROWS = [
  {
    dto: {
      key: 'second-id-0',
      fields: [
        gResultField({
          name: 'displayDesignation',
          value: 'second-desig-0',
          source: 'M',
        }),
      ],
    },
  },
  {
    dto: {
      key: 'second-id-1',
      fields: [
        gResultField({
          name: 'displayDesignation',
          value: 'second-desig-1',
          source: 'M',
        }),
      ],
    },
  },
]

const mocks = vi.hoisted(() => ({
  selectedTechology: null,
  isNewMasterdataTab: false,
}))

//#region MOCKS
vi.mock('@/store', () => ({
  knowledgeStore: {
    initSearch: vi.fn(),
    setEntityClass: vi.fn(),
    setSelectedSearchItem: vi.fn(),
    setSelectedTechnology: vi.fn(),
    setSelectedMasterdataKey: vi.fn(),
    setFieldsCalculationModule: vi.fn(),
    setLoadingState: vi.fn(),
    selectedTechnology: mocks.selectedTechology,
    entityType: 'MANUFACTURING_STEP',
    isNewMasterdataTab: mocks.isNewMasterdataTab,
  },
}))
const { setMockResponse } = withAxiosMock()
//#endregion MOCKS

//#region SETUP FACTORY

type SetupOptions = {
  technologiesList?: Partial<MasterdataList>[]
  selectedMasterdataTechnology?: Nullable<string>
}
const setup = async ({
  technologiesList = MULTIPLE_FIRST_STAGE_ROWS,
  selectedMasterdataTechnology = null,
}: SetupOptions = {}) => {
  // first stage rows are requested on component mount
  setMockResponse({
    currentPage: technologiesList,
    totalCount: technologiesList.length,
  })
  const setSelectedSearchItemSpy = vi.spyOn(
    knowledgeStore,
    'setSelectedSearchItem'
  )
  const setSelectedTechnologySpy = vi.spyOn(
    knowledgeStore,
    'setSelectedTechnology'
  )
  const setFieldsCalculationModuleSpy = vi.spyOn(
    knowledgeStore,
    'setFieldsCalculationModule'
  )
  const getSetSelectedTechnologyCall = () =>
    setSelectedTechnologySpy.mock.lastCall![0]
  const getSetFieldsCalculationModuleCall = () =>
    setFieldsCalculationModuleSpy.mock.lastCall![0]
  const getSetSelectedSearchItemCall = () =>
    setSelectedSearchItemSpy.mock.lastCall![0]

  const wrapper = mount(KnowledgeCalculationModule, {
    props: {
      selectedMasterdataTechnology,
    },
    global: {
      stubs: [
        'KnowledgeLeftFirstStage',
        'KnowledgeLeftSecondStage',
        'KnowledgeCalculationModuleRight',
      ],
    },
    slots: {
      secondStage: '<div class="secondStageSlot"></div>',
    },
  })

  //#region HELPERS

  const getFirstStageComponent = () =>
    wrapper.findComponent(KnowledgeLeftFirstStage)
  const isFirstStageVisible = () => getFirstStageComponent().isVisible()
  const firstStageProps = () => getFirstStageComponent().props()
  const getFirstStageRow = (n: number) => firstStageProps().rows[n]
  const emitFirstStageEvent = (eventName: string, ...args: unknown[]) =>
    getFirstStageComponent().vm.$emit(eventName, ...args)

  const getSecondStageComponent = () =>
    wrapper.findComponent(KnowledgeLeftSecondStage)
  const getSecondStageProps = () => getSecondStageComponent().props()
  const getSecondStageRow = (n: number) => getSecondStageProps().childRows[n]
  const emitSecondStageEvent = (eventName: string, ...args: unknown[]) =>
    getSecondStageComponent().vm.$emit(eventName, ...args)

  const allPromisesFlushed = async () =>
    await new Promise((resolve) => setTimeout(resolve))

  const selectFirstStageRow = async (n: number) => {
    // after first stage row click, second stage rows should be loaded
    setMockResponse({
      currentPage: SECOND_STAGE_ROWS,
      totalCount: SECOND_STAGE_ROWS.length,
    })
    await emitFirstStageEvent('clicked-event', getFirstStageRow(n).id)
    await allPromisesFlushed()
  }
  const selectSecondStageRow = async (n: number) => {
    await emitSecondStageEvent('child-clicked-event', getSecondStageRow(n).id)
    await allPromisesFlushed()
  }
  //#endregion HELPERS

  await allPromisesFlushed()

  return {
    wrapper,
    getFirstStageComponent,
    isFirstStageVisible,
    firstStageProps,
    selectFirstStageRow,
    selectSecondStageRow,

    getSetSelectedSearchItemCall,
    getSetSelectedTechnologyCall,
    getSetFieldsCalculationModuleCall,
  }
}
//#endregion SETUP FACTORY

describe('KnowledgeCalculationModule', () => {
  describe('when first stage has multiple rows', async () => {
    it('- show the first stage', async () => {
      const { isFirstStageVisible } = await setup()
      expect(isFirstStageVisible()).toBe(true)
    })
    it('- provide rows to KnowledgeLeftFirstStage', async () => {
      const { firstStageProps } = await setup({
        technologiesList: [gMasterdataList(), gMasterdataList()],
      })

      expect(firstStageProps().rows[0]).toMatchObject({
        composite: expect.any(Object),
        currentType: '',
        displayDesignation: {
          name: 'displayDesignation',
          source: 'I',
          type: 'Text',
          value: 'value-5',
        },
        entityClass: undefined,
        fields: [
          {
            name: 'displayDesignation',
            source: 'I',
            type: 'Text',
            value: 'value-5',
          },
          {
            name: 'name-6',
            source: 'I',
            type: 'Text',
            value: 'value-6',
          },
        ],
        id: expect.any(String),
        isTset: true,
        key: undefined,
        refKey: '',
      })
    })
  })
  describe('when first stage has only 1 row', () => {
    it('- select this row', async () => {
      const { getSetSelectedSearchItemCall } = await setup({
        technologiesList: ONE_FIRST_STAGE_ROW,
      })

      expect(getSetSelectedSearchItemCall()).toMatchObject({
        key: 'first-id-0',
        fields: [{ name: 'displayDesignation', value: 'first-desig-0' }],
      })
    })
  })

  describe('when second stage row is chosen', () => {
    it('should show the secondStage slot', async () => {
      const { wrapper } = await setup({
        technologiesList: MULTIPLE_FIRST_STAGE_ROWS,
        selectedMasterdataTechnology: 'first-id-0',
      })

      // Find the slot element
      const secondStageSlot = wrapper.find('.secondStageSlot')
      expect(secondStageSlot.exists()).toBe(true)
    })
  })
})
