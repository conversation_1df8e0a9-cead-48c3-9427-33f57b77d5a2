// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`TheRoughManuSummary Component > should mount component properly 1`] = `
"<div>
  <div>
    <div>
      <div><span>tabs.summary</span><span>statics.overview</span></div>
    </div>
    <div></div>
  </div>
  <div>
    <div data-test="the-rough-manu-summary">
      <article id="statics.roughCalculation">
        <div>
          <div>
            <div data-test="card-layout-title-icon-container">statics.roughCalculation
              <!--v-if-->
              <!--v-if-->
            </div>
          </div>
          <div></div>
        </div>
        <!--v-if-->
        <!--v-if-->
        <collapse-transition-stub group="false">
          <div data-test="card-layout-content">
            <div data-test="loading-wave"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 96 84">
                <rect id="rect-first" width="8" height="48" x="1" y="18" fill="#645F91" rx="3"></rect>
                <rect id="rect-second" width="8" height="48" x="30" y="18" fill="#645F91" rx="3"></rect>
                <rect id="rect-third" width="8" height="48" x="59" y="18" fill="#645F91" rx="3"></rect>
                <rect id="rect-fourth" width="8" height="48" x="88" y="18" fill="#645F91" rx="3"></rect>
              </svg></div>
            <div>
              <div data-test="tset-kpi">
                <icon-cost-stub></icon-cost-stub>
                <div><span data-test="tset-kpi-title">statics.costPerPart</span>
                  <div><span data-test="tset-kpi-value"></span>
                    <!--v-if-->
                  </div>
                  <!--v-if-->
                </div>
              </div>
            </div>
          </div>
        </collapse-transition-stub>
      </article>
      <article id="statics.productionEnvironment">
        <div>
          <div>
            <div data-test="card-layout-title-icon-container">statics.productionEnvironment
              <!--v-if-->
              <!--v-if-->
            </div>
          </div>
          <div></div>
        </div>
        <!--v-if-->
        <!--v-if-->
        <collapse-transition-stub group="false">
          <div data-test="card-layout-content">
            <div data-test="loading-wave"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 96 84">
                <rect id="rect-first" width="8" height="48" x="1" y="18" fill="#645F91" rx="3"></rect>
                <rect id="rect-second" width="8" height="48" x="30" y="18" fill="#645F91" rx="3"></rect>
                <rect id="rect-third" width="8" height="48" x="59" y="18" fill="#645F91" rx="3"></rect>
                <rect id="rect-fourth" width="8" height="48" x="88" y="18" fill="#645F91" rx="3"></rect>
              </svg></div>
            <div>
              <the-production-environment-stub></the-production-environment-stub>
              <!--v-if-->
            </div>
          </div>
        </collapse-transition-stub>
      </article>
    </div>
  </div>
</div>"
`;

exports[`TheRoughManuSummary Component > show ThePartParameters if PCB type calculation 1`] = `
"<div>
  <div>
    <div>
      <div><span>tabs.summary</span><span>statics.overview</span></div>
    </div>
    <div></div>
  </div>
  <div>
    <div data-test="the-rough-manu-summary">
      <article id="statics.pcbCalculation">
        <div>
          <div>
            <div data-test="card-layout-title-icon-container">statics.pcbCalculation
              <!--v-if-->
              <!--v-if-->
            </div>
          </div>
          <div></div>
        </div>
        <!--v-if-->
        <!--v-if-->
        <collapse-transition-stub group="false">
          <div data-test="card-layout-content">
            <div data-test="loading-wave"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 96 84">
                <rect id="rect-first" width="8" height="48" x="1" y="18" fill="#645F91" rx="3"></rect>
                <rect id="rect-second" width="8" height="48" x="30" y="18" fill="#645F91" rx="3"></rect>
                <rect id="rect-third" width="8" height="48" x="59" y="18" fill="#645F91" rx="3"></rect>
                <rect id="rect-fourth" width="8" height="48" x="88" y="18" fill="#645F91" rx="3"></rect>
              </svg></div>
            <div>
              <div data-test="tset-kpi">
                <icon-cost-stub></icon-cost-stub>
                <div><span data-test="tset-kpi-title">statics.costPerPart</span>
                  <div><span data-test="tset-kpi-value"></span>
                    <!--v-if-->
                  </div>
                  <!--v-if-->
                </div>
              </div>
            </div>
          </div>
        </collapse-transition-stub>
      </article>
      <article id="statics.productionEnvironment">
        <div>
          <div>
            <div data-test="card-layout-title-icon-container">statics.productionEnvironment
              <!--v-if-->
              <!--v-if-->
            </div>
          </div>
          <div></div>
        </div>
        <!--v-if-->
        <!--v-if-->
        <collapse-transition-stub group="false">
          <div data-test="card-layout-content">
            <div data-test="loading-wave"><svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 96 84">
                <rect id="rect-first" width="8" height="48" x="1" y="18" fill="#645F91" rx="3"></rect>
                <rect id="rect-second" width="8" height="48" x="30" y="18" fill="#645F91" rx="3"></rect>
                <rect id="rect-third" width="8" height="48" x="59" y="18" fill="#645F91" rx="3"></rect>
                <rect id="rect-fourth" width="8" height="48" x="88" y="18" fill="#645F91" rx="3"></rect>
              </svg></div>
            <div>
              <the-production-environment-stub></the-production-environment-stub>
              <the-part-parameters-stub data-test="the-part-parameters"></the-part-parameters-stub>
            </div>
          </div>
        </collapse-transition-stub>
      </article>
    </div>
  </div>
</div>"
`;
