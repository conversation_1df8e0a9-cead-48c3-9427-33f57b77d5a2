import { navigationStore } from '@/store'
import { manufacturingStore } from '@domain/calculation/manufacturing.store'
import { useWizardCalculationStore } from '@domain/wizard/wizardCalculation.store'
import { createTestingPinia } from '@pinia/testing'
import type TsetActionMenu from '@tset/design/molecules/TsetActionMenu/TsetActionMenu.vue'
import { gBranch } from '@tset/shared-model/calculation/Branch'
import { CalculationType } from '@tset/shared-model/calculation/CalculationType'
import { CalculationCreationModalMode } from '@tset/shared-model/calculation/WizardCalculationEnums'
import { wizardDTOHelper } from '@tset/shared-utils/helpers/createManu'
import { useModal } from '@tset/shared-utils/plugins/vue-modal'
import { gBomNodeEntity } from '@tset/shared-utils/tests/generators/bomNodeEntity'
import { shallowMount, type ComponentMountingOptions } from '@vue/test-utils'
import { ref } from 'vue'
import EmptyBreakdownCard from './EmptyBreakdownCard.vue'

vi.mock('@/store', () => ({
  navigationStore: {
    navigateTo: vi.fn(),
    path: ['my-path'],
  },
  manufacturingStore: {
    loadedNode: vi.fn(),
  },
}))
vi.mock('@tset/shared-utils/helpers/createManu', () => ({
  wizardDTOHelper: vi.fn(() => 'helper-response'),
}))
vi.mock('@tset/shared-utils/plugins/vue-modal', () => {
  const $modal = {
    show: vi.fn(),
    hide: vi.fn(),
  }
  return { useModal: () => $modal, modalsPlugin: vi.fn() }
})

const navigateToMock = vi.mocked(navigationStore.navigateTo)
const wizardDTOHelperMock = vi.mocked(wizardDTOHelper)
const useModalMock = vi.mocked(useModal)
Object.defineProperties(manufacturingStore, {
  loadedNode: {
    configurable: true,
    get: vi.fn(() =>
      ref(
        gBomNodeEntity({
          id: 'bom-id',
          branch: gBranch({ id: 'branch-id' }),
          calculationType: {
            name: 'qwe',
            type: 'Text',
            source: 'C',
            value: CalculationType.MANUAL,
            systemValue: null,
          },
        })
      )
    ),
  },
})
//#endregion MOCKS

//#region SETUP FACTORY
const setup = () => {
  vi.clearAllMocks()
  const mountOptions: ComponentMountingOptions<typeof EmptyBreakdownCard> = {
    global: {
      plugins: [createTestingPinia()],
    },
  }

  const wrapper = shallowMount(EmptyBreakdownCard, mountOptions)

  //#region HELPERS
  const getAddMaterialTile = () =>
    wrapper.findByDataTest('empty-breakdown-card-add-material')
  const getAddStepTile = () =>
    wrapper.findByDataTest('empty-breakdown-card-add-step')
  const getAddInvestTile = () =>
    wrapper.findByDataTest('empty-breakdown-card-add-invest')
  const getChangeCalculationTypeButton = () =>
    wrapper.findByDataTest('empty-breakdown-card-change-calculation-type')
  const getActionMenu = () =>
    wrapper.findComponent<typeof TsetActionMenu>({ name: 'TsetActionMenu' })
  const clickAddMaterialTile = () => getAddMaterialTile().trigger('click')
  const clickAddStepTile = () => getAddStepTile().trigger('click')
  const clickAddInvestTile = () => getAddInvestTile().trigger('click')
  const clickChangeCalculationTypeButton = () =>
    getChangeCalculationTypeButton().trigger('click')
  const getActionMenuActions = () => getActionMenu().props('actions')

  const wizardCalculationStore = useWizardCalculationStore()
  const reopenModalSpy = vi.mocked(wizardCalculationStore.reopenModal)
  //#endregion HELPERS

  return {
    wrapper,
    when: {
      clickAddMaterialTile,
      clickAddStepTile,
      clickAddInvestTile,
      clickChangeCalculationTypeButton,
    },
    then: {
      getActionMenuActions,
      reopenModalSpy,
    },
  }
}
//#endregion SETUP FACTORY

//#region TESTS
describe('EmptyBreakdownCard', () => {
  describe('when clicking the tiles', () => {
    it('- opens the popup with provided actions', async () => {
      const { when, then } = setup()

      await when.clickAddMaterialTile()
      expect(then.getActionMenuActions()).toEqual([
        expect.objectContaining({ key: 'MATERIAL' }),
        expect.objectContaining({ key: 'CONSUMABLE' }),
        expect.objectContaining({ key: 'BOM_ENTRY' }),
        expect.objectContaining({ key: 'C_PART' }),
      ])
    })

    it('- navigates to the step page', async () => {
      const { when } = setup()

      await when.clickAddStepTile()
      expect(navigateToMock).toHaveBeenCalledWith({
        targetPage: 'project-id-manu-manufacturing',
        afterAction: expect.any(Function),
      })
    })

    it('- navigates to the invest page', async () => {
      const { when } = setup()

      await when.clickAddInvestTile()
      expect(navigateToMock).toHaveBeenCalledWith({
        targetPage: 'project-id-manu-invest',
        afterAction: expect.any(Function),
      })
    })
  })

  describe('when clicking on the change calc type button', () => {
    it('opens the modal', async () => {
      const { when, then } = setup()

      await when.clickChangeCalculationTypeButton()

      expect(wizardDTOHelperMock).toHaveBeenCalledWith(
        'bom-id',
        'branch-id',
        'EUR',
        ['my-path'],
        CalculationCreationModalMode.CHANGE,
        CalculationType.MANUAL
      )
      expect(then.reopenModalSpy).toHaveBeenCalledWith('helper-response')
      expect(useModalMock().show).toHaveBeenCalledWith('calculationTypeModal')
    })
  })
})
//#endregion TESTS
