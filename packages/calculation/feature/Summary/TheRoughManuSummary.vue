<template>
  <TsetPageWrapper>
    <template #title>
      {{ $t('tabs.summary') }}
    </template>
    <div
      class="relative flex flex-grow flex-col gap-16 xl:flex-row"
      data-test="the-rough-manu-summary"
    >
      <CardLayout
        :is-collapsible="false"
        :html-id="
          isPcbCalculation
            ? 'statics.pcbCalculation'
            : 'statics.roughCalculation'
        "
        class="flex-grow"
      >
        <template #content>
          <TsetWave v-show="isLoading" class="mx-auto" />
          <div
            v-show="!isLoading"
            class="mb-24 flex h-232 items-center justify-center"
          >
            <TsetKpi
              :formatted-value="kpiFormattedValue"
              :unit="kpiUnit"
              :title="kpiTitle"
              size="wrapper"
              class="gap-12"
            >
              <template #icon>
                <component :is="icon" class="biggest-kpi-icon" />
              </template>
            </TsetKpi>
          </div>
        </template>
      </CardLayout>
      <CardLayout
        html-id="statics.productionEnvironment"
        class="mr-16 max-w-348 flex-1 flex-shrink-0"
        :is-collapsible="false"
      >
        <template #content>
          <TsetWave v-show="isLoading" class="mx-auto" />
          <div v-show="!isLoading" class="px-16">
            <TheProductionEnvironment />
            <ThePartParameters
              v-if="isPcbCalculation"
              data-test="the-part-parameters"
            />
          </div>
        </template>
      </CardLayout>
    </div>
  </TsetPageWrapper>
</template>

<script setup lang="ts">
import { useAppMode } from '@/store/useAppMode'
import { $formatNumber } from '@shared/format/formatNumber'
import { $nutUnit, $t } from '@shared/translation/nuTranslation'
import TsetKpi from '@tset/design/atoms/TsetKpi'
import TsetPageWrapper from '@tset/design/atoms/TsetPageWrapper/TsetPageWrapper.vue'
import TsetWave from '@tset/design/atoms/TsetWave/TsetWave.vue'
import CardLayout from '@tset/shared-ui/layout/CardLayout.vue'
import { useDisplayCurrency } from '@tset/shared-utils/api/useDisplayCurrency'
import { useInjectedManufacturing } from '@tset/shared-utils/composables/useInjectedManufacturing'
import { getValue } from '@tset/shared-utils/helpers/getValue'
import { emptyField } from '@tset/shared-utils/helpers/manufacturing'
import { computed, inject, type Ref } from 'vue'
import ThePartParameters from './ThePartParameters.vue'
import TheProductionEnvironment from './TheProductionEnvironment.vue'

const { displayCurrency } = useDisplayCurrency()
//#region PROPS

withDefaults(defineProps<{ isPcbCalculation?: boolean }>(), {
  isPcbCalculation: false,
})
//#endregion PROPS

//#region INJECT
const loadedManufacturing = useInjectedManufacturing()
const isLoading = inject<Ref<boolean>>('manufacturingIsLoading')
//#endregion INJECT

const { appMode } = useAppMode()

const kpiName = appMode.computed('costPerPart', 'cO2PerPart')

const kpiTitle = appMode.computed(
  $t('statics.costPerPart'),
  $t('statics.cO2PerPart')
)

const icon = appMode.computed('IconCost', 'IconCO2')

const kpiField = computed(() => {
  return (
    loadedManufacturing.value?.fields.find(
      (field) => field.name === kpiName.value
    ) ?? emptyField()
  )
})

// Get the information for the TsetKpi
const kpiFormattedValue = computed((): string => {
  return $formatNumber(
    getValue(
      kpiField.value as ResultField<ResultFieldValueType>,
      displayCurrency
    ) as number
  )
})

const kpiUnit = computed((): string => {
  return $nutUnit(kpiField.value)
})
</script>
<script lang="ts">
export default {
  name: 'RoughManuSummary',
}
</script>
<style lang="postcss" scoped>
.biggest-kpi-icon {
  @apply h-50 w-auto text-primary-light  !important;
}
</style>
