import { manufacturingStore } from '@domain/calculation/manufacturing.store'
import { createTesting<PERSON>inia } from '@pinia/testing'
import TsetWave from '@tset/design/atoms/TsetWave/TsetWave.vue'
import CopyPasteToExcel from '@tset/shared-ui/copyPaste/CopyPasteToExcel.vue'
import CostBreakdownChart from '@tset/shared-ui/waterfall/CostBreakdownChart.vue'
import WaterfallLegend from '@tset/shared-ui/waterfall/CostBreakdownLegend.vue'
import { gBomNodeEntity } from '@tset/shared-utils/tests/generators/bomNodeEntity'
import { gManufacturing } from '@tset/shared-utils/tests/generators/manufacturing'
import { gResultField } from '@tset/shared-utils/tests/generators/resultField'
import { mount } from '@vue/test-utils'
import { nextTick } from 'vue'
import EmptyBreakdownCard from './EmptyBreakdownCard.vue'
import TheCostBreakdownTable from './TheCostBreakdownTable.vue'

vi.mock('@/store', () => ({
  userStore: {
    mode: 'cost',
  },
}))

const expectedRows = [
  {
    level: 0,
    key: 'material',
    value: '123',
    unit: 'm',
    fraction: 12.34,
  },
  {
    level: 0,
    key: 'overheads',
    value: '123',
    unit: 'm',
    fraction: 33.33,
  },
  {
    level: 1,
    key: 'overheads-1',
    value: '123',
    unit: 'm',
    fraction: 11.11,
  },
  {
    level: 1,
    key: 'overheads-2',
    value: '123',
    unit: 'm',
    fraction: 22.22,
  },
  {
    level: 0,
    key: 'Total',
    value: '246',
    unit: 'm',
    fraction: 45.67,
  },
]
const defaultCost = [
  {
    id: 'material',
    source: 'material',
    cost: gResultField({
      value: 123,
      type: 'Money',
      currencyInfo: { EUR: 123 },
    }),
    fraction: 12.34,
    nestedTable: undefined,
  },
  {
    id: 'overheads',
    source: 'overheads',
    cost: gResultField({
      value: 123,
      type: 'Money',
      currencyInfo: { EUR: 123 },
    }),
    fraction: 33.33,
    nestedTable: [
      {
        id: 'overheads-1',
        source: 'overheads-1',
        cost: gResultField({
          value: 123,
          type: 'Money',
          currencyInfo: { EUR: 123 },
        }),
        fraction: 11.11,
        nestedTable: undefined,
      },

      {
        id: 'overheads-2',
        source: 'overheads-2',
        cost: gResultField({
          value: 123,
          type: 'Money',
          currencyInfo: { EUR: 123 },
        }),
        fraction: 22.22,
        nestedTable: undefined,
      },
    ],
  },
]
const costWithoutEntities = [
  {
    id: 'material',
    source: 'material',
    cost: gResultField({
      value: 0,
      type: 'Money',
    }),
    fraction: 0,
    nestedTable: undefined,
  },
]

const setup = ({ withEntities = true } = {}) => {
  const mountOptions = {
    global: {
      plugins: [createTestingPinia()],
      stubs: [
        'NuTableRow',
        'IconContentCopy',
        'EmptyBreakdownCard',
        'IconChevronDown',
        'IconInfoDetails',
        'CollapseTransition',
      ],
    },
  }

  manufacturingStore.setNode({
    node: gBomNodeEntity({
      manufacturing: gManufacturing({
        fields: [gResultField({ name: 'costPerPart', unit: 'm' })],
      }),
      cbd: {
        co2: [],
        cost: withEntities ? defaultCost : costWithoutEntities,
      },
    }),
  })
  const wrapper = mount(TheCostBreakdownTable, mountOptions)

  const getTsetWave = () => wrapper.findComponent(TsetWave)
  const getCostBreakdownChart = () => wrapper.findComponent(CostBreakdownChart)
  const getEmptyBreakdownCard = () => wrapper.findComponent(EmptyBreakdownCard)
  const getChartLegend = () => wrapper.findComponent(WaterfallLegend)
  const copyPasteProps = () => wrapper.findComponent(CopyPasteToExcel).props()

  const setTableLoading = async () => {
    manufacturingStore.setCbdTableLoading(true)
    await nextTick()
  }

  return {
    wrapper,
    when: { setTableLoading },
    then: {
      getTsetWave,
      getCostBreakdownChart,
      getChartLegend,
      copyPasteProps,
      getEmptyBreakdownCard,
    },
  }
}

describe('TheCostBreakdownTable tests', () => {
  describe('Loading with TsetWave', () => {
    it('- shows `TsetWave` when first rendered', async () => {
      const { when, then } = setup()

      await when.setTableLoading()

      expect(then.getTsetWave().exists()).toBeTruthy()
    })

    it('- does not show a `TsetWave` when children of the chart have been requested', () => {
      const { then } = setup()

      expect(then.getTsetWave().exists()).toBeFalsy()
    })
  })

  describe('CostBreakdownChart interactions', () => {
    it('- shows a `CostBreakdownChart` when first rendered and the manufacturing has finished loading', () => {
      const { then } = setup()

      expect(then.getCostBreakdownChart().exists()).toBeTruthy()
    })

    it('- does not show a `CostBreakdownChart` when a manufacturing node is loading', async () => {
      const { when, then } = setup()

      await when.setTableLoading()

      expect(then.getCostBreakdownChart().exists()).toBeFalsy()
    })

    it('- shows a `ChartLegend` when manufacturing has finished loading', () => {
      const { then } = setup()
      expect(then.getChartLegend().exists()).toBeTruthy()
    })

    it('- does not show a `ChartLegend` when manufacturing node is loading', async () => {
      const { when, then } = setup()

      await when.setTableLoading()

      expect(then.getChartLegend().exists()).toBeFalsy()
    })
  })

  describe('check data for copy paste', () => {
    it('- prepares data for copy paste', () => {
      const { then } = setup()
      expect(then.copyPasteProps().rows).toStrictEqual(expectedRows)
    })
  })

  describe('when the table has no entities', () => {
    it('- shows empty card instead of a chart', () => {
      const { then } = setup({ withEntities: false })

      expect(then.getEmptyBreakdownCard().exists()).toBeTruthy()
    })
  })
})
