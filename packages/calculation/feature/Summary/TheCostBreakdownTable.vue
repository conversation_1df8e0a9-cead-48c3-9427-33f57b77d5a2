<template>
  <CardLayout :html-id="htmlId" :is-collapsible="false">
    <template #title>
      <span class="text-subheader-semibold text-black-default">
        {{ $t(isCostMode ? htmlId : 'statics.co2Breakdown') }}
      </span>
    </template>
    <template #content>
      <TsetWave v-if="showLoader" class="mx-auto mt-48" />
      <template v-else>
        <div v-if="hasEntities">
          <div class="mx-24 -mb-4 min-h-72">
            <CostBreakdownLegend
              v-data-test:breakdown-legend
              class="flex-wrap"
              size="small"
            />
          </div>
          <CostBreakdownChart
            v-if="!isCalculationBroken"
            v-data-test:chart
            :bom-node="bomNode"
            :is-loading-manufacturing="isLoadingManufacturing"
            :loaded-manufacturing="loadedManufacturing"
            :loaded-project="loadedProject"
            :mode="appMode.current"
          />
        </div>
        <EmptyBreakdownCard v-else />
        <div class="flex flex-col gap-8 px-8">
          <template v-for="(rowObj, rowKey) of dataByMode">
            <NuTableRow
              v-if="rowObj"
              :key="rowObj.source + rowKey"
              :row-item="rowObj"
              :row-key="rowKey"
              :total-length="
                isCostMode ? costTableData.length : co2TableData.length
              "
            />
          </template>
          <CopyPasteToExcel
            :rows="copyData"
            has-leading-column
            class="m-12 flex justify-end"
          />
        </div>
      </template>
    </template>
  </CardLayout>
</template>

<script setup lang="ts">
import { costStore } from '@/store/cost.store'
import { useAppMode } from '@/store/useAppMode'
import type Manufacturing from '@domain/calculation/Manufacturing'
import { manufacturingStore } from '@domain/calculation/manufacturing.store'
import { useCalculationIssues } from '@domain/calculation/utils/useCalculationIssues'
import { $nutUnit } from '@shared/translation/nuTranslation'
import TsetWave from '@tset/design/atoms/TsetWave/TsetWave.vue'
import CopyPasteToExcel from '@tset/shared-ui/copyPaste/CopyPasteToExcel.vue'
import CardLayout from '@tset/shared-ui/layout/CardLayout.vue'
import NuTableRow from '@tset/shared-ui/parts/NuTableRow.vue'
import CostBreakdownChart from '@tset/shared-ui/waterfall/CostBreakdownChart.vue'
import CostBreakdownLegend from '@tset/shared-ui/waterfall/CostBreakdownLegend.vue'
import { useDisplayCurrency } from '@tset/shared-utils/api/useDisplayCurrency'
import { getValue } from '@tset/shared-utils/helpers/getValue'
import { computed, ref, watch } from 'vue'
import EmptyBreakdownCard from './EmptyBreakdownCard.vue'

//#region TABLE DATA
const costTableData = ref<TableData[]>([])
const co2TableData = ref<TableData[]>([])
const childrenRequested = ref<boolean>(false)
const { displayCurrency } = useDisplayCurrency()
const { isCostMode, appMode, currentMode } = useAppMode()

const isCbdTableLoading = computed(() => manufacturingStore.cbdTableIsLoading)
const costsData = computed<TableData[]>(
  () => manufacturingStore.tableSetup.tableData
)
const co2Data = computed<TableData[]>(
  () => manufacturingStore.tableSetup.co2tableData
)

const dataByMode = appMode.computed(
  () => costTableData.value,
  () => co2TableData.value
)

const unit = computed<string>(() =>
  manufacturingStore.manufacturingUnitField
    ? $nutUnit(manufacturingStore.manufacturingUnitField)
    : ''
)

const htmlId = appMode.computed(
  'statics.costBreakdown',
  'statics.emissionBreakdown'
)

const showLoader = computed<boolean>(
  () =>
    (isLoadingManufacturing.value && childrenRequested.value) ||
    isCbdTableLoading.value
)

const copyData = computed<FractionBasedCopyToExcel[]>(() =>
  recursiveCopyDataExtraction(dataByMode.value)
)

const bomNode = computed<Nullable<BomNodeEntity>>(
  () => manufacturingStore.loadedNode
)

const calcIssues = useCalculationIssues({ appMode: currentMode, bomNode })
const isCalculationBroken = computed(() => calcIssues.isBroken)

function recursiveCopyDataExtraction(
  rows: TableRow[],
  level = 0,
  copyDataRows: FractionBasedCopyToExcel[] = []
) {
  rows.forEach((row) => {
    copyDataRows.push({
      level,
      key: row.source,
      value: String(getValue(row.cost, displayCurrency.value)),
      unit: unit.value,
      fraction: row.fraction,
    })
    if (row.nestedTable) {
      recursiveCopyDataExtraction(row.nestedTable, level + 1, copyDataRows)
    }
  })
  return copyDataRows
}

const hasEntities = computed<boolean>(() => {
  return dataByMode.value.some(
    (row) =>
      !!row.cost.value ||
      row.nestedTable?.some((nestedRow) => nestedRow.nestedTable?.length)
  )
})
//#endregion TABLE DATA

//#region CHART
const isLoadingManufacturing = computed<boolean>(
  () => manufacturingStore.manufacturingIsLoading
)
const loadedProject = computed<ProjectFolderNode>(
  () => costStore.loadedProject!
)
const loadedManufacturing = computed<Nullable<Manufacturing>>(
  () => manufacturingStore.loadedManufacturing
)
//#endregion CHART

//#region WATCHERS
watch(costsData, (newData) => (costTableData.value = newData), {
  immediate: true,
  deep: true,
})
watch(co2Data, (newData) => (co2TableData.value = newData), {
  immediate: true,
  deep: true,
})
//#endregion WATCHERS
</script>

<script lang="ts">
export default {
  name: 'TheCostBreakdownTable',
}
</script>
