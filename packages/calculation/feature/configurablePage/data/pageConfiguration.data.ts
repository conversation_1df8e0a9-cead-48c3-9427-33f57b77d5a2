import { keepPreviousData, queryOptions } from '@tanstack/vue-query'
import { toValue, type MaybeRef } from 'vue'
import { getPageConfigurationApi } from '../api/pageConfiguration.api'

export function pageConfigurationOptions(
  key: MaybeRef<UIConfigurationIdentifiers | undefined>
) {
  const keyValue = toValue(key)
  return queryOptions({
    queryKey: ['calc', 'configurations', 'page', keyValue],
    queryFn: () => getPageConfigurationApi(keyValue!),
    staleTime: 60 * 60 * 1000,
    refetchOnWindowFocus: false,
    placeholderData: keepPreviousData,
    enabled: !!keyValue,
  })
}
