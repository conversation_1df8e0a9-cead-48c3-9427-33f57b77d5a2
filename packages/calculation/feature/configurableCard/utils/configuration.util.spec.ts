/* eslint-disable max-lines */

import type Manufacturing from '@domain/calculation/Manufacturing'
import * as navigationHelpers from '@tset/shared-utils/helpers/navigation'
import { gManufacturing } from '@tset/shared-utils/tests/generators/manufacturing'
import {
  gManufacturingEntity,
  initManufacturingDbWithManufacturing,
} from '@tset/shared-utils/tests/generators/manufacturingEntity'
import { gResultField } from '@tset/shared-utils/tests/generators/resultField'
import { type Action } from '../data/actions.data'
import type { ColumnOptions } from '../model/columnOptions.model'
import { LocatorType, type EntityTableConfig } from '../model/entityTable.model'
import {
  CellType,
  type FieldTableConfig,
  type FieldTableRowDefinition,
} from '../model/fieldTable.model'
import { TableType, type EntityLocator } from '../model/utility.model'
import { getSectionFields, mergeActions } from '../utils/configuration.util'
import { getCompound<PERSON>ey, getTableData } from './configuration.util'

describe('getCompoundKey', () => {
  it('- stringifies the passed JSON', () => {
    const keys: UIConfigurationIdentifiers = {
      identifiers: {
        Foo: {
          groupKey: 'tset',
          version: '11.24',
          configKey: '23kde4',
          type: 'cost',
        },
        Bar: {
          groupKey: 'nu',
          version: '01.24',
          configKey: 'as234d',
          type: 'co2',
        },
      },
    }
    expect(getCompoundKey(keys)).toMatchInlineSnapshot(
      `"{"identifiers":{"Foo":{"groupKey":"tset","version":"11.24","configKey":"23kde4","type":"cost"},"Bar":{"groupKey":"nu","version":"01.24","configKey":"as234d","type":"co2"}}}"`
    )
  })
})

function setup(given: {
  config: FieldTableConfig | EntityTableConfig
  entity: Manufacturing
}) {
  const tableData = getTableData(given.config, given.entity)
  const rowAt = (index: number) => tableData.rows[index]
  const columnAt = (index: number) => tableData.columns[index]

  return {
    then: {
      tableData,
      rowAt,
      columnAt,
    },
  }
}
function getEntityTestData(args: { field?: ResultField } = {}) {
  const field = args.field ?? gResultField()
  const fields = [
    field,
    gResultField({ name: 'b', value: 'b' }),
    gResultField({ name: 'c', value: 'c' }),
  ]
  const entity = initManufacturingDbWithManufacturing(
    gManufacturingEntity({
      id: 'root',
      type: 'MANUFACTURING',
      fields,
      children: [
        gManufacturingEntity({
          id: 'favoriteChild',
          type: 'MANUFACTURING',
          fields,
        }),
        gManufacturingEntity({
          id: 'complicatedChild',
          type: 'CONSUMABLE',
          fields,
          children: [
            gManufacturingEntity({
              id: 'favoriteGrandChild',
              type: 'MACHINE',
              fields,
            }),
            gManufacturingEntity({
              id: 'dislikedGrandChild',
              type: 'CONSUMABLE',
              fields,
            }),
            gManufacturingEntity({
              id: 'somewhatOkayGrandChild',
              type: 'LABOR',
              fields,
            }),
          ],
        }),
        gManufacturingEntity({
          id: 'otherChild',
          type: 'CONSUMABLE',
          fields,
        }),
      ],
    })
  )
  return { field, entity, fieldName: field.name }
}
function getFieldTestData(
  args: { field?: ResultField; children?: ManufacturingDTO[] } = {}
) {
  const field = args.field ?? gResultField({ name: 'a', value: 'value' })
  const fieldName = field.name
  const entity = initManufacturingDbWithManufacturing(
    gManufacturingEntity({
      id: '1',
      children: args.children ?? [],
      fields: [
        field,
        gResultField({ name: 'b', value: 'b' }),
        gResultField({ name: 'c', value: 'c' }),
        gResultField({ name: 'd', value: 'd' }),
        gResultField({ name: 'e', value: 'e' }),
        gResultField({ name: 'f', value: 'f' }),
        gResultField({
          name: 'manuallyOverriddenField',
          source: 'I',
          systemValue: 5,
        }),
      ],
    })
  )

  return {
    fieldName,
    field,
    entity,
  }
}
function singleColumnLookupConfig(args: {
  fieldName: string
  columnId?: string
  columnOptions?: ColumnOptions
  collectFrom?: EntityLocator
}): {
  config: FieldTableConfig
  columnId: string
} {
  const fieldName = args.fieldName
  const columnId = args.columnId ?? fieldName
  const collectFrom = args.collectFrom ?? { type: 'self' }
  return {
    config: {
      type: 'field',
      rows: ['rowId'],
      rowDefinitions: {
        rowId: {
          id: 'rowId',
          cells: [
            {
              type: CellType.LOOKUP,
              columnId: columnId,
              collectFrom,
              fieldName,
            },
          ],
        },
      },
      columns: [{ id: columnId, options: args.columnOptions ?? undefined }],
    },
    columnId,
  }
}
function singleColumnValueConfig(args: {
  field: ResultField
  columnId?: string
  columnOptions?: ColumnOptions
  navigateToEntityLocator?: FieldTableRowDefinition['navigateToEntityLocator']
}): {
  config: FieldTableConfig
  columnId: string
} {
  const fieldName = args.field.name
  const columnId = args.columnId ?? fieldName
  return {
    config: {
      type: 'field',
      rows: ['rowId'],
      rowDefinitions: {
        rowId: {
          id: 'rowId',
          cells: [
            {
              type: CellType.VALUE,
              columnId: columnId,
              field: args.field,
            },
          ],
          navigateToEntityLocator: args.navigateToEntityLocator,
        },
      },
      columns: [{ id: columnId, options: args.columnOptions ?? undefined }],
    },
    columnId,
  }
}
function singleColumnChildConfig(args: {
  fieldName: string
  columnOptions?: ColumnOptions
}): {
  config: EntityTableConfig
  columnId: string
} {
  const columnId = 'columnId'
  const config: EntityTableConfig = {
    type: 'entity',
    rows: ['rowId'],
    rowDefinitions: {
      rowId: {
        id: 'rowId',
        collectBy: {
          type: LocatorType.CHILD,
          criteria: [
            {
              operator: 'eq',
              key: 'type',
              value: 'CONSUMABLE',
            },
          ],
        },
        actionKeys: {},
      },
    },
    columns: [
      {
        id: columnId,
        field: args.fieldName,
        options: args.columnOptions,
      },
    ],
  }
  return { config, columnId }
}
function singleColumnAncestorConfig(args: {
  fieldName: string
  columnOptions?: ColumnOptions
}): {
  config: EntityTableConfig
  columnId: string
} {
  const columnId = 'columnId'
  const config: EntityTableConfig = {
    type: 'entity',
    rows: ['rowId'],
    rowDefinitions: {
      rowId: {
        id: 'rowId',
        collectBy: {
          type: LocatorType.ANCESTOR,
          criteria: [
            {
              operator: 'eq',
              key: 'type',
              value: 'CONSUMABLE',
            },
          ],
        },
        actionKeys: {},
      },
    },
    columns: [
      { id: columnId, field: args.fieldName, options: args.columnOptions },
    ],
  }
  return { config, columnId }
}
describe('getTableData - FieldTable', () => {
  it('- sets unLink to `true` when a field has source `I`', () => {
    const { entity, fieldName } = getFieldTestData({
      field: gResultField({
        name: 'manuallyOverriddenField',
        source: 'I',
        systemValue: 5,
      }),
    })
    const { config } = singleColumnLookupConfig({ fieldName })
    const { then } = setup({ config, entity })
    expect(then.rowAt(0).unLink).toBe(true)
  })
  it('- overwrite the field readOnly to `true` when column has `editable: false`', () => {
    const { entity, fieldName } = getFieldTestData({
      field: gResultField({
        metaInfo: { readOnly: false },
      }),
    })
    const { config, columnId } = singleColumnLookupConfig({
      fieldName,
      columnOptions: { editable: false },
    })
    const { then } = setup({ entity, config })
    expect(then.rowAt(0)[columnId]).toEqual(
      expect.objectContaining({
        name: fieldName,
        metaInfo: expect.objectContaining({ readOnly: true }),
      })
    )
  })
  describe('collects lookup cells correctly', () => {
    it('- when collectFrom.type equals `self`', () => {
      const { entity, field, fieldName } = getFieldTestData({
        field: gResultField({}),
      })
      const { config, columnId } = singleColumnLookupConfig({ fieldName })
      const { then } = setup({ entity, config })
      expect(then.rowAt(0)[columnId]).toStrictEqual(
        expect.objectContaining(field)
      )
    })
    it('- when collectFrom.type equals `child`', () => {
      const field = gResultField()
      const { entity } = getFieldTestData({
        children: [gManufacturingEntity({ name: 'Vin', fields: [field] })],
      })
      const { config, columnId } = singleColumnLookupConfig({
        fieldName: field.name,
        collectFrom: {
          type: 'child',
          criteria: [{ operator: 'eq', value: 'Vin', key: 'name' }],
        },
      })
      const { then } = setup({ entity, config })
      expect(then.rowAt(0)[columnId]).toStrictEqual(
        expect.objectContaining(field)
      )
    })
    it('- when collectFrom.type equals `ancestor`', () => {
      const field = gResultField()
      const { entity } = getFieldTestData({
        children: [
          gManufacturingEntity({
            children: [gManufacturingEntity({ name: 'Vin', fields: [field] })],
          }),
        ],
      })
      const { config, columnId } = singleColumnLookupConfig({
        fieldName: field.name,
        collectFrom: {
          type: 'ancestor',
          criteria: [{ operator: 'eq', value: 'Vin', key: 'name' }],
        },
      })
      const { then } = setup({ entity, config })
      expect(then.rowAt(0)[columnId]).toStrictEqual(
        expect.objectContaining(field)
      )
    })
    it('- collects value cells correctly', () => {
      const { entity, field } = getFieldTestData({
        field: gResultField({}),
      })
      const { config, columnId } = singleColumnValueConfig({ field })
      const { then } = setup({ entity, config })
      expect(then.rowAt(0)[columnId]).toStrictEqual(field)
    })
  })
  describe('collects "value cells" correctly', () => {
    it('- does not populate the column if cell not found', () => {
      const { entity } = getFieldTestData({
        field: gResultField({ name: 'knownField' }),
      })
      const { config, columnId } = singleColumnLookupConfig({
        fieldName: 'unknownField',
      })
      const { then } = setup({ entity, config })
      expect(then.rowAt(0)[columnId]).toBeNull()
    })
  })
  describe('collects other cases correctly', () => {
    it('- does not populate the column if cellType is unknown', () => {
      const { entity, fieldName } = getFieldTestData({
        field: gResultField({}),
      })
      const config: FieldTableConfig = {
        type: TableType.FIELD,
        rows: ['rowId'],
        rowDefinitions: {
          rowId: {
            id: 'rowId',
            cells: [
              {
                // @ts-expect-error unknown type
                type: 'Unknown',
                columnId: 'columnId',
                collectFrom: { type: 'self' },
                fieldName,
              },
            ],
          },
        },
        columns: [{ id: 'columnId' }],
      }
      const { then } = setup({ entity, config })
      expect(then.rowAt(0).columnId).toBeUndefined()
    })
    it('- collects the sub-rows', () => {
      const { entity, fieldName, field } = getFieldTestData({
        field: gResultField({}),
      })
      const config: FieldTableConfig = {
        type: TableType.FIELD,
        rows: ['rowId'],
        rowDefinitions: {
          rowId: {
            id: 'rowId',
            cells: [
              {
                type: CellType.LOOKUP,
                columnId: 'columnId',
                collectFrom: { type: 'self' },
                fieldName,
              },
            ],
            rows: ['subRow'],
          },
          subRow: {
            id: 'subRow',
            cells: [
              {
                type: CellType.LOOKUP,
                columnId: 'columnId',
                collectFrom: { type: 'self' },
                fieldName,
              },
            ],
          },
        },
        columns: [{ id: 'columnId' }],
      }
      const { then } = setup({ entity, config })
      expect(then.rowAt(0)).toHaveProperty('rows')
      const subRow = then.tableData.rows[0].rows[0]
      expect(subRow['columnId']).toStrictEqual(expect.objectContaining(field))
    })
    it('- returns the correct data', () => {
      const config: FieldTableConfig = {
        type: TableType.FIELD,
        rows: ['1', '2'],
        rowDefinitions: {
          1: {
            id: '1',
            cells: [
              {
                type: CellType.LOOKUP,
                collectFrom: { type: 'self' },
                fieldName: 'a',
                columnId: '1',
              },
              {
                type: CellType.VALUE,
                field: gResultField({ name: 'cell', value: 'value' }),
                columnId: '2',
              },
            ],
            rows: ['3'],
          },
          2: {
            id: '2',
            cells: [
              {
                type: CellType.LOOKUP,
                collectFrom: { type: 'self' },
                fieldName: 'b',
                columnId: '1',
              },
              {
                type: CellType.VALUE,
                field: gResultField({ name: 'cell2', value: 'value2' }),
                columnId: '2',
              },
            ],
          },
          3: {
            id: '3',
            cells: [
              {
                type: CellType.LOOKUP,
                collectFrom: { type: 'self' },
                fieldName: 'c',
                columnId: '1',
              },
              {
                type: CellType.VALUE,
                field: gResultField({ name: 'cell3', value: 'value 3' }),
                columnId: '2',
              },
            ],
          },
        },
        columns: [
          { id: '1', options: { sortable: { mode: 'ASC' } } },
          { id: '2', options: { hasTotal: true, editable: false } },
          { id: '3', options: { displayDesignation: 'Overwritten' } },
        ],
      }
      const { entity } = getFieldTestData()
      const { then } = setup({ entity, config })

      expect(then.tableData).toMatchSnapshot()
    })
  })

  describe('getTableData - EntityTable', () => {
    it('- overwrite the field readOnly to `true` when column has `editable: false`', () => {
      const { entity, fieldName } = getEntityTestData({
        field: gResultField({ metaInfo: { readOnly: false } }),
      })
      const { config, columnId } = singleColumnChildConfig({
        fieldName,
        columnOptions: { editable: false },
      })

      const { then } = setup({ config, entity })
      expect(then.rowAt(0)[columnId]).toEqual(
        expect.objectContaining({
          metaInfo: expect.objectContaining({ readOnly: true }),
        })
      )
    })
    it('- collects all children which match the criteria', () => {
      const { entity, fieldName } = getEntityTestData({
        field: gResultField(),
      })
      const { config } = singleColumnChildConfig({ fieldName })

      const { then } = setup({ config, entity })
      expect(then.tableData.rows).toHaveLength(2)
    })
    it('- collect all ancestors which match the criteria', () => {
      const { entity, fieldName } = getEntityTestData({
        field: gResultField(),
      })
      const { config } = singleColumnAncestorConfig({ fieldName })

      const { then } = setup({ config, entity })
      expect(then.tableData.rows).toHaveLength(3)
    })
    it('- extracts the fields correctly', () => {
      const { entity, fieldName } = getEntityTestData({
        field: gResultField(),
      })
      const { config, columnId } = singleColumnChildConfig({ fieldName })

      const { then } = setup({ config, entity })
      expect(then.tableData.rows).toEqual([
        expect.objectContaining({
          [columnId]: expect.objectContaining({ name: fieldName }),
        }),
        expect.objectContaining({
          [columnId]: expect.objectContaining({ name: fieldName }),
        }),
      ])
    })
    it('- does not add a field if not found', () => {
      const { entity, field } = getEntityTestData({
        field: gResultField({ name: 'knownField' }),
      })
      const { config, columnId } = singleColumnChildConfig({
        fieldName: 'unknownField',
      })

      const { then } = setup({ config, entity })
      expect(then.tableData.rows).not.toEqual([
        expect.objectContaining({ [columnId]: field }),
        expect.objectContaining({ [columnId]: field }),
      ])
    })
    it('- populates sub-rows correctly', () => {
      const { entity, fieldName } = getEntityTestData({ field: gResultField() })
      const config: EntityTableConfig = {
        type: 'entity',
        rows: ['rowId'],
        rowDefinitions: {
          rowId: {
            id: 'rowId',
            collectBy: {
              type: LocatorType.ANCESTOR,
              criteria: [
                {
                  operator: 'eq',
                  key: 'type',
                  value: 'CONSUMABLE',
                },
              ],
            },
            actionKeys: {},
            rows: ['subRowId'],
          },
          subRowId: {
            id: 'subRowId',
            collectBy: {
              type: LocatorType.CHILD,
              criteria: [{ operator: 'eq', key: 'type', value: 'MACHINE' }],
            },
            actionKeys: {},
          },
        },
        columns: [{ id: 'columnId', field: fieldName }],
      }
      const { then } = setup({ entity, config })
      expect(then.rowAt(0)).toHaveProperty('rows')
    })
    it('- returns empty array for unknown LocatorType', () => {
      const { entity, fieldName } = getEntityTestData({
        field: gResultField(),
      })
      const config: EntityTableConfig = {
        type: 'entity',
        rows: ['rowId'],
        rowDefinitions: {
          rowId: {
            id: 'rowId',
            collectBy: {
              //@ts-expect-error unknown type
              type: 'unknown',
              criteria: [
                {
                  operator: 'eq',
                  key: 'type',
                  value: 'CONSUMABLE',
                },
              ],
            },
          },
        },
        columns: [{ id: 'columnId', field: fieldName }],
      }
      const { then } = setup({ config, entity })
      expect(then.tableData.rows).toHaveLength(0)
    })
  })
  describe('getTableData - column transformations', () => {
    describe('- main column', () => {
      it(`- has css class of 'tset-main-column'`, () => {
        const { entity, fieldName } = getEntityTestData()
        const { config } = singleColumnChildConfig({
          fieldName,
          columnOptions: { mainColumn: true },
        })
        const { then } = setup({ config, entity })
        expect(then.columnAt(0).cssClasses).toContain('tset-main-column')
      })
      it(`- has iconsBefore`, () => {
        const { entity, fieldName } = getEntityTestData()
        const { config } = singleColumnChildConfig({
          fieldName,
          columnOptions: { mainColumn: true },
        })
        const { then } = setup({ config, entity })
        const expectedArray = [
          { fieldName: 'unLink' },
          { fieldName: 'currency' },
          { fieldName: 'entityType' },
        ]
        expect(then.columnAt(0).iconsBefore).toStrictEqual(expectedArray)
      })
      it(`- has 'navigatable'`, () => {
        const { entity, fieldName } = getEntityTestData()
        const { config } = singleColumnChildConfig({
          fieldName,
          columnOptions: { mainColumn: true },
        })
        const { then } = setup({ config, entity })
        expect(then.columnAt(0).navigatable).toBe(true)
      })
    })
    describe('navigatable cell', () => {
      describe('when table is EntityTable', () => {
        it('- triggers getNavigationRequest helper ', async () => {
          const { entity, fieldName } = getEntityTestData()
          const { config } = singleColumnAncestorConfig({
            fieldName,
            columnOptions: { mainColumn: true },
          })
          const getNavigationRequestSpy = vi.spyOn(
            navigationHelpers,
            'getNavigationRequest'
          )

          setup({ config, entity })
          expect(getNavigationRequestSpy).toHaveBeenCalledTimes(3)
        })
        it('- does not exist on columns without `mainColumn` option', () => {
          const { entity, fieldName } = getEntityTestData()
          const { config } = singleColumnAncestorConfig({
            fieldName,
          })
          const { then } = setup({ config, entity })
          expect(then.columnAt(0)).not.toHaveProperty('navigatable')
        })
        it('- does exist on columns with `mainColumn` option', () => {
          const { entity, fieldName } = getEntityTestData()
          const { config } = singleColumnAncestorConfig({
            fieldName,
            columnOptions: { mainColumn: true },
          })
          const { then } = setup({ config, entity })
          expect(then.columnAt(0)).toHaveProperty('navigatable')
        })
      })
      describe('when table is FieldTable', () => {
        it('- triggers getNavigationRequest when row contains `navigateToEntityLocator`', () => {
          const { entity, field } = getFieldTestData({
            children: [gManufacturingEntity({ name: 'Vin' })],
          })
          const { config } = singleColumnValueConfig({
            field,
            navigateToEntityLocator: {
              type: 'child',
              criteria: [{ operator: 'eq', value: 'Vin', key: 'name' }],
            },
          })
          const getNavigationRequestSpy = vi.spyOn(
            navigationHelpers,
            'getNavigationRequest'
          )
          setup({ config, entity })
          expect(getNavigationRequestSpy).toHaveBeenCalledTimes(1)
        })
        it('- doesn`t call getNavigationRequest when row contains no `navigateToEntityLocator`', () => {
          const { entity, field } = getFieldTestData({
            children: [gManufacturingEntity({ name: 'Vin' })],
          })
          const { config } = singleColumnValueConfig({
            field,
          })
          const getNavigationRequestSpy = vi.spyOn(
            navigationHelpers,
            'getNavigationRequest'
          )
          setup({ config, entity })
          expect(getNavigationRequestSpy).toHaveBeenCalledTimes(0)
        })
        it('- does not exist on columns without `mainColumn` option', () => {
          const { entity, field } = getFieldTestData({
            children: [gManufacturingEntity({ name: 'Vin' })],
          })
          const { config } = singleColumnValueConfig({
            field,
            navigateToEntityLocator: {
              type: 'child',
              criteria: [{ operator: 'eq', value: 'Vin', key: 'name' }],
            },
          })
          const { then } = setup({ config, entity })
          expect(then.columnAt(0)).not.toHaveProperty('navigatable')
        })
        it('- does exist on columns with `mainColumn` option', () => {
          const { entity, field } = getFieldTestData({
            children: [gManufacturingEntity({ name: 'Vin' })],
          })
          const { config } = singleColumnValueConfig({
            field,
            columnOptions: { mainColumn: true },
            navigateToEntityLocator: {
              type: 'child',
              criteria: [{ operator: 'eq', value: 'Vin', key: 'name' }],
            },
          })
          const { then } = setup({ config, entity })
          expect(then.columnAt(0)).toHaveProperty('navigatable')
        })
      })
    })
    describe('icons on cell', () => {
      it(` - do not exist if 'mainColumn' isn't set`, () => {
        const { entity, fieldName } = getEntityTestData()
        const { config } = singleColumnAncestorConfig({
          fieldName,
        })
        const { then } = setup({ config, entity })
        expect(then.columnAt(0)).not.toHaveProperty('iconsBefore')
      })
    })
    describe('editable cell', () => {
      it('- is `true` by default ', () => {
        const { entity, fieldName } = getEntityTestData()
        const { config } = singleColumnAncestorConfig({
          fieldName,
        })
        const { then } = setup({ config, entity })
        expect(then.columnAt(0).editable).toBe(true)
      })
      it('- is `false` if options.editable is `false`', () => {
        const { entity, fieldName } = getEntityTestData()
        const { config } = singleColumnAncestorConfig({
          fieldName,
          columnOptions: { editable: false },
        })
        const { then } = setup({ config, entity })
        expect(then.columnAt(0).editable).toBe(false)
      })
    })
  })
  describe('getTableData', () => {
    it('- throws when configuration is of unknown type', () => {
      expect(() =>
        getTableData(
          {
            // @ts-expect-error we enforce an error here
            type: 'unknown',
          },
          gManufacturing()
        )
      ).toThrow()
    })
  })
})

describe('- Actions', () => {
  describe('mergeActions', () => {
    it('returns default actions when no overrides are provided', () => {
      const defaultActions: Action[] = [
        { key: 'a1', label: 'Action 1' },
        { key: 'a2', label: 'Action 2' },
      ]

      expect(mergeActions(defaultActions)).toEqual(defaultActions)
    })

    it('merges overrides into existing actions', () => {
      const defaultActions: Action[] = [
        { key: 'a1', label: 'Action 1', disabled: true },
        { key: 'a2', label: 'Action 2', disabled: false },
      ]

      const overrides = {
        a1: { disabled: false },
        a2: { label: 'Modified Action 2' },
      }

      expect(mergeActions(defaultActions, overrides)).toEqual([
        { key: 'a1', label: 'Action 1', disabled: false },
        { key: 'a2', label: 'Modified Action 2', disabled: false },
      ])
    })

    it('adds new actions from overrides if not present in default actions', () => {
      const defaultActions: Action[] = [{ key: 'a1', label: 'Action 1' }]

      const overrides = {
        a2: { key: 'a2', label: 'Action 2', disabled: true },
      }

      expect(mergeActions(defaultActions, overrides)).toEqual([
        { key: 'a1', label: 'Action 1' },
        { key: 'a2', label: 'Action 2', disabled: true },
      ])
    })

    it('handles an empty default actions array', () => {
      const overrides = {
        a1: { key: 'a1', label: 'New Action', disabled: true },
      }

      expect(mergeActions([], overrides)).toEqual([
        { key: 'a1', label: 'New Action', disabled: true },
      ])
    })

    it('handles an empty overrides object', () => {
      const defaultActions: Action[] = [{ key: 'a1', label: 'Action 1' }]

      expect(mergeActions(defaultActions, {})).toEqual(defaultActions)
    })
  })
})

describe('getSectionFields', () => {
  const manufacturing = initManufacturingDbWithManufacturing(
    gManufacturingEntity({
      fields: [
        gResultField({ name: 'f-1', metaInfo: { objectView: 'system' } }),
        gResultField({
          name: 'f-2',
          metaInfo: { objectView: 'system', fieldIndex: 4 },
        }),
        gResultField({
          name: 'f-3',
          metaInfo: { objectView: 'tool', fieldIndex: 3 },
        }),
        gResultField({
          name: 'f-4',
          metaInfo: { objectView: 'tool', fieldIndex: 0 },
        }),
        gResultField({
          name: 'f-5',
          metaInfo: { objectView: 'system', fieldIndex: 1 },
        }),
      ],
    })
  )
  describe('getting simple field list', () => {
    it('- return fields with', () => {
      const result = getSectionFields(
        { fieldNames: ['f-1', 'f-4', 'f-2'] },
        manufacturing
      )
      expect(result).toStrictEqual([
        expect.objectContaining({ name: 'f-1' }),
        expect.objectContaining({ name: 'f-4' }),
        expect.objectContaining({ name: 'f-2' }),
      ])
    })
  })
  describe('get with field metaInfo criteria', () => {
    describe('- returns fields matching the criteria and sorted', () => {
      it('- for tool', () => {
        const result = getSectionFields(
          {
            fieldNames: [
              {
                operator: 'and',
                criteria: [
                  { key: 'objectView', operator: 'eq', value: 'tool' },
                  {
                    key: 'fieldIndex',
                    operator: 'nin',
                    value: [null, undefined, ''],
                  },
                ],
              },
            ],
          },
          manufacturing
        )
        expect(result).toStrictEqual([
          expect.objectContaining({ name: 'f-4' }),
          expect.objectContaining({ name: 'f-3' }),
        ])
      })
      it('- for system', () => {
        const result = getSectionFields(
          {
            fieldNames: [
              {
                operator: 'and',
                criteria: [
                  {
                    operator: 'or',
                    criteria: [
                      { key: 'objectView', operator: 'eq', value: 'system' },
                    ],
                  },
                  {
                    key: 'fieldIndex',
                    operator: 'nin',
                    value: [null, undefined, ''],
                  },
                ],
              },
            ],
          },
          manufacturing
        )
        expect(result).toStrictEqual([
          expect.objectContaining({ name: 'f-5' }),
          expect.objectContaining({ name: 'f-2' }),
        ])
      })
      it('- for tool and system', () => {
        const result = getSectionFields(
          {
            fieldNames: [
              {
                operator: 'and',
                criteria: [
                  {
                    operator: 'or',
                    criteria: [
                      { key: 'objectView', operator: 'eq', value: 'system' },
                      { key: 'objectView', operator: 'eq', value: 'tool' },
                    ],
                  },
                  {
                    key: 'fieldIndex',
                    operator: 'nin',
                    value: [null, undefined, ''],
                  },
                ],
              },
            ],
          },
          manufacturing
        )
        expect(result).toStrictEqual([
          expect.objectContaining({ name: 'f-4' }),
          expect.objectContaining({ name: 'f-5' }),
          expect.objectContaining({ name: 'f-3' }),
          expect.objectContaining({ name: 'f-2' }),
        ])
      })
    })
  })
})
