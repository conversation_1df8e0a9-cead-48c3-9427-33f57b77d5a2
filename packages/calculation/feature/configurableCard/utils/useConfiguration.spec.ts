import type { IUseTabulatorReturn } from '@tset/design/molecules/TsetTable/tabulator/composables/interfaces'
import { gResultField } from '@tset/shared-utils/tests/generators/resultField'
import type { Ref } from 'vue'
import { ref } from 'vue'
import { type Configuration } from '../model/configuration.model'
import { LocatorType, type EntityTableConfig } from '../model/entityTable.model'
import { CellType } from '../model/fieldTable.model'
import { useConfiguration } from './useConfiguration'

const key: UIConfigurationIdentifiers = {
  identifiers: {
    Foo: {
      groupKey: 'tset',
      version: '11.24',
      configKey: '23kde4',
      type: 'cost',
    },
    Bar: {
      groupKey: 'nu',
      version: '01.24',
      configKey: 'as234d',
      type: 'co2',
    },
  },
}

const rowConfig: EntityTableConfig = {
  type: 'entity',
  rows: ['rowId'],
  rowDefinitions: {
    rowId: {
      id: 'rowId',
      collectBy: {
        type: LocatorType.ANCESTOR,
        criteria: [
          {
            operator: 'eq',
            key: 'type',
            value: 'CONSUMABLE',
          },
        ],
      },
      actionKeys: { myAction: { info: 'some action' } },
    },
  },
  columns: [
    { id: '456', field: 'f456' },
    { id: '123', field: 'f123' },
  ],
}
const config: Configuration = {
  cards: {
    myCard: {
      title: { cost: 'myCardCostTitle', co2: 'myCardCo2Title' },
      tableVariations: {
        cost: ['myCardCostVariation'],
        co2: ['myCardCo2Variation'],
      },
      views: ['cost', 'co2'],
    },
  },
  key,
  tableConfigs: { myCard_cost_myCardCostVariation: rowConfig },
}

const tabulator = ref({
  clearData: vi.fn(),
}) as unknown as Ref<IUseTabulatorReturn<TableRow>>

vi.mock('../api/configuration.api', () => {
  return {
    hardCodedConfig: {
      cards: {
        machineHourlyRate: {
          views: ['cost', 'co2'],
          title: {
            cost: 'Hourly rate per machine',
            co2: 'Hourly rate per machine',
          },
          tableVariations: {
            co2: ['default'],
            cost: ['default'],
          },
        },
      },
      tableConfigs: {
        machineHourlyRate_cost_default: {
          type: 'field',
          rows: ['cO2DuringProduction', 'cO2NonOccupancy'],
          rowDefinitions: {
            fixedCO2DuringProduction: {
              id: 'fixedCO2DuringProduction',
              cells: [
                {
                  type: CellType.VALUE,
                  field: gResultField({
                    name: 'displayDesignation',
                    value: 'Fixed emission',
                  }),
                  columnId: '1',
                },
                {
                  type: CellType.LOOKUP,
                  fieldName: 'fixedCO2DuringProductionPerYear',
                  collectFrom: { type: 'self' },
                  columnId: '2',
                },
                {
                  type: CellType.LOOKUP,
                  fieldName: 'fixedCO2DuringProductionPerPart',
                  collectFrom: { type: 'self' },
                  columnId: '3',
                },
              ],
            },
          },
          columns: [
            { id: '1', options: { displayDesignation: 'displayDesignation' } },
            {
              id: '2',
              options: { displayDesignation: 'emission', hasTotal: true },
            },
            {
              id: '3',
              options: { displayDesignation: 'emission', hasTotal: true },
            },
          ],
        },
      },
    },
  }
})

describe('card', () => {
  describe('get a card out of the config with cardId', () => {
    it('- returns a card without modes', () => {
      const actual = useConfiguration(
        ref(config),
        'myCard',
        ref('cost'),
        tabulator
      )

      expect(actual.card.value).toStrictEqual({
        tableVariations: ['myCardCostVariation'],
        title: 'myCardCostTitle',
        views: ['cost', 'co2'],
      })
    })
  })
  describe('get a tableConfig out of the config', () => {
    it('- returns a tableConfig', () => {
      const actual = useConfiguration(
        ref(config),
        'myCard',
        ref('cost'),
        tabulator
      )

      expect(actual.tableConfig.value).toMatchInlineSnapshot(`
        {
          "columns": [
            {
              "field": "f456",
              "id": "456",
            },
            {
              "field": "f123",
              "id": "123",
            },
          ],
          "rowDefinitions": {
            "rowId": {
              "actionKeys": {
                "myAction": {
                  "info": "some action",
                },
              },
              "collectBy": {
                "criteria": [
                  {
                    "key": "type",
                    "operator": "eq",
                    "value": "CONSUMABLE",
                  },
                ],
                "type": "ancestor",
              },
              "id": "rowId",
            },
          },
          "rows": [
            "rowId",
          ],
          "type": "entity",
        }
      `)
    })
  })
  describe('get a tableConfig out of the hardCodedConfig', () => {
    it('- returns a tableConfig hardcoded', () => {
      const actual = useConfiguration(
        ref(config),
        'machineHourlyRate',
        ref('cost'),
        tabulator
      )

      expect(actual.tableConfig.value).toMatchInlineSnapshot(`
        {
          "columns": [
            {
              "id": "1",
              "options": {
                "displayDesignation": "displayDesignation",
              },
            },
            {
              "id": "2",
              "options": {
                "displayDesignation": "emission",
                "hasTotal": true,
              },
            },
            {
              "id": "3",
              "options": {
                "displayDesignation": "emission",
                "hasTotal": true,
              },
            },
          ],
          "rowDefinitions": {
            "fixedCO2DuringProduction": {
              "cells": [
                {
                  "columnId": "1",
                  "field": {
                    "name": "displayDesignation",
                    "source": "I",
                    "type": "Text",
                    "value": "Fixed emission",
                  },
                  "type": "value",
                },
                {
                  "collectFrom": {
                    "type": "self",
                  },
                  "columnId": "2",
                  "fieldName": "fixedCO2DuringProductionPerYear",
                  "type": "lookup",
                },
                {
                  "collectFrom": {
                    "type": "self",
                  },
                  "columnId": "3",
                  "fieldName": "fixedCO2DuringProductionPerPart",
                  "type": "lookup",
                },
              ],
              "id": "fixedCO2DuringProduction",
            },
          },
          "rows": [
            "cO2DuringProduction",
            "cO2NonOccupancy",
          ],
          "type": "field",
        }
      `)
    })
  })
  describe('activeTableVariation', () => {
    it('- gets the current Table variation', () => {
      const actual = useConfiguration(
        ref(config),
        'myCard',
        ref('cost'),
        tabulator
      )
      expect(actual.activeTableVariation.value).toBe('myCardCostVariation')
    })
    it('- set the current Table variation', () => {
      const actual = useConfiguration(
        ref(config),
        'myCard',
        ref('cost'),
        tabulator
      )
      actual.activeTableVariation.value = 'myOtherCostVariation'
      expect(actual.activeTableVariation.value).toBe('myOtherCostVariation')
    })
  })
})
