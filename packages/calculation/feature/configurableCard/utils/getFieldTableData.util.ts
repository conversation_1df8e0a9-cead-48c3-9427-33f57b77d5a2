import type Manufacturing from '@domain/calculation/Manufacturing'
import { getNavigationRequest } from '@tset/shared-utils/helpers/navigation'
import { checkCriteria } from '@tset/shared-utils/helpers/object'
import { isFieldManuallyOverridden } from '@tset/shared-utils/helpers/overriden'
import { isResultField } from '@tset/shared-utils/helpers/typeGuard/isResultField'
import { shouldNeverHappen } from '@tset/shared-utils/helpers/typescript'
import type {
  FieldTableCellDefinition,
  FieldTableColumnDefinition,
  FieldTableConfig,
  FieldTableRowDefinition,
  LookupCell,
} from '../model/fieldTable.model'
import type { EntityLocator } from '../model/utility.model'
import { getColOptionsFromColumn } from './columnOptions.util'

function getFieldFromEntity(
  contextEntity: Manufacturing | undefined,
  cellDefinition: LookupCell,
  config: FieldTableConfig
) {
  if (!contextEntity) {
    return
  }
  return contextEntity.getField(cellDefinition.fieldName, (field) =>
    config.columns.find(({ id }) => id === cellDefinition.columnId)?.options
      ?.editable === false
      ? { ...field, metaInfo: { ...field.metaInfo, readOnly: true } }
      : field
  )
}

function getEntityForLocator(
  contextEntity: Manufacturing | undefined,
  locator: EntityLocator
) {
  if (!contextEntity) {
    return
  }

  const { type, criteria } = locator
  switch (type) {
    case 'ancestor': {
      return contextEntity
        .getSubEntities()
        .find((entity) => checkCriteria(entity, criteria))
    }
    case 'child': {
      return contextEntity
        .getChildren()
        .find((entity) => checkCriteria(entity, criteria))
    }
    case 'self':
      return contextEntity
    default:
      throw shouldNeverHappen('unkown collectFrom type', type)
  }
}

function lookupCell(
  contextEntity: Manufacturing,
  cellDefinition: LookupCell,
  config: FieldTableConfig
) {
  return getFieldFromEntity(
    getEntityForLocator(contextEntity, cellDefinition.collectFrom),
    cellDefinition,
    config
  )
}

function getFieldForCell(
  contextEntity: Manufacturing,
  cellDefinition: FieldTableCellDefinition,
  config: FieldTableConfig
) {
  const type = cellDefinition.type
  switch (type) {
    case 'lookup':
      return lookupCell(contextEntity, cellDefinition, config)
    case 'value':
      return cellDefinition.field
    default:
      throw shouldNeverHappen('unknown cell type', type)
  }
}

function getRowWithFields({
  row,
  contextEntity,
  rowTemplate,
  config,
}: {
  row: FieldTableRowDefinition
  contextEntity: Manufacturing
  rowTemplate: TableRow
  config: FieldTableConfig
}): TableRow {
  return row.cells.reduce<Partial<Record<string, Nullable<ResultField>>>>(
    (result, cellDefinition) => {
      try {
        const field = getFieldForCell(contextEntity, cellDefinition, config)
        result[cellDefinition.columnId] = field
      } catch (e: unknown) {
        console.error(e)
      }
      return result
    },
    rowTemplate
  )
}

function getRowDefinition(
  id: FieldTableRowDefinition['id'],
  collection: FieldTableConfig['rowDefinitions']
) {
  return collection[id]
}

function getRowTemplate(
  config: FieldTableConfig,
  rowDefinition: FieldTableRowDefinition,
  contextEntity: Manufacturing
) {
  const navigationRequest: TableRow['navigationRequest'] =
    rowDefinition.navigateToEntityLocator
      ? getNavigationRequest(
          getEntityForLocator(
            contextEntity,
            rowDefinition.navigateToEntityLocator
          )
        )
      : { type: 'skip' }
  return {
    isSummable: config.columns.some(({ options }) => options?.hasTotal),
    navigationRequest,
  }
}

function evaluateUnlink(row: TableRow) {
  return Object.values(row).some(
    (value) => isResultField(value) && isFieldManuallyOverridden(value)
  )
}

function getRow({
  rowId,
  contextEntity,
  config,
}: {
  rowId: FieldTableRowDefinition['id']
  contextEntity: Manufacturing
  config: FieldTableConfig
}): TableRow {
  const rowDefinition = getRowDefinition(rowId, config.rowDefinitions)
  const subRows = rowDefinition.rows
  const rowTemplate = getRowTemplate(config, rowDefinition, contextEntity)
  const rowWithFields = getRowWithFields({
    row: rowDefinition,
    contextEntity,
    rowTemplate,
    config,
  })
  rowWithFields.unLink = evaluateUnlink(rowWithFields)
  if (!subRows) {
    return rowWithFields
  }
  return {
    ...rowWithFields,
    id: rowId,
    rows: subRows.map((subRow) =>
      getRow({ rowId: subRow, contextEntity, config })
    ),
  }
}

function getRows({
  contextEntity,
  config,
}: {
  contextEntity: Manufacturing
  config: FieldTableConfig
}) {
  return config.rows.map((rowId) => getRow({ rowId, contextEntity, config }))
}

function configurationColumnToTableColumn(
  column: FieldTableColumnDefinition
): ColOptions {
  return getColOptionsFromColumn(column)
}

export function getFieldTableData(
  config: FieldTableConfig,
  contextEntity: Manufacturing
) {
  return {
    rows: getRows({ contextEntity, config }),
    columns: config.columns.map(configurationColumnToTableColumn),
    options: config.options,
  }
}
