// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`getTableData - FieldTable > collects other cases correctly > - returns the correct data 1`] = `
{
  "columns": [
    {
      "alias": "1",
      "align": "left",
      "editable": true,
      "fieldName": "1",
      "sortable": {
        "disabled": false,
        "mode": "ASC",
      },
      "translatedAlias": true,
      "visible": true,
    },
    {
      "alias": "2",
      "align": "left",
      "editable": false,
      "fieldName": "2",
      "hasTotal": true,
      "sortable": {
        "disabled": true,
        "mode": "ASC",
      },
      "translatedAlias": true,
      "visible": true,
    },
    {
      "alias": "Overwritten",
      "align": "left",
      "displayDesignation": "Overwritten",
      "editable": true,
      "fieldName": "3",
      "sortable": {
        "disabled": true,
        "mode": "ASC",
      },
      "translatedAlias": true,
      "visible": true,
    },
  ],
  "options": undefined,
  "rows": [
    {
      "1": {
        "metaInfo": {
          "overwriteParentInfo": {
            "className": "CLASS_NAME",
            "id": "1",
            "ref": "ManufacturingRef",
            "type": "MANUFACTURING",
          },
        },
        "name": "a",
        "source": "I",
        "type": "Text",
        "value": "value",
      },
      "2": {
        "name": "cell",
        "source": "I",
        "type": "Text",
        "value": "value",
      },
      "id": "1",
      "isSummable": true,
      "navigationRequest": {
        "type": "skip",
      },
      "rows": [
        {
          "1": {
            "metaInfo": {
              "overwriteParentInfo": {
                "className": "CLASS_NAME",
                "id": "1",
                "ref": "ManufacturingRef",
                "type": "MANUFACTURING",
              },
            },
            "name": "c",
            "source": "I",
            "type": "Text",
            "value": "c",
          },
          "2": {
            "name": "cell3",
            "source": "I",
            "type": "Text",
            "value": "value 3",
          },
          "isSummable": true,
          "navigationRequest": {
            "type": "skip",
          },
          "unLink": false,
        },
      ],
      "unLink": false,
    },
    {
      "1": {
        "metaInfo": {
          "overwriteParentInfo": {
            "className": "CLASS_NAME",
            "id": "1",
            "ref": "ManufacturingRef",
            "type": "MANUFACTURING",
          },
        },
        "name": "b",
        "source": "I",
        "type": "Text",
        "value": "b",
      },
      "2": {
        "name": "cell2",
        "source": "I",
        "type": "Text",
        "value": "value2",
      },
      "isSummable": true,
      "navigationRequest": {
        "type": "skip",
      },
      "unLink": false,
    },
  ],
}
`;
