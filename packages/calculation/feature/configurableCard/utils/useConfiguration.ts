import type {
  CardConfig,
  CardOption,
  Configuration,
  DetailKey,
  FieldConfig,
} from '@calculation/configurable-card/model/configuration.model'
import { useWritableComputed } from '@tset/shared-utils/composables/useWritableComputed/useWriteableComputed'
import { computed, nextTick, type Ref } from 'vue'
import type { IUseTabulatorReturn } from '@tset/design/molecules/TsetTable/tabulator/composables/interfaces'
import { hardCodedConfig } from '../api/configuration.api'

type CardConfigMode = {
  title: string
  kpi?: ResultField['name']
  fields?: FieldConfig
  tableVariations?: string[]
  options?: CardOption
  views: Mode[]
}

export function useConfiguration(
  config: Ref<Configuration> | Ref<undefined>,
  cardId: string,
  mode: Ref<Mode>,
  tabulator: Ref<IUseTabulatorReturn<TableRow>>
) {
  const card = computed(() => {
    if (!config.value) {
      return
    }

    const card_ = getCard({ config, cardId })

    return card_ ? getRidOfByMode(card_) : undefined
  })

  function getCard(args: { config: Ref<Configuration>; cardId: string }) {
    const {
      config: { value: config },
      cardId,
    } = args
    if (cardId in config.cards) {
      return config.cards[cardId]
    } else if (cardId in hardCodedConfig.cards) {
      return hardCodedConfig.cards[cardId]
    }
  }

  function getRidOfByMode(card: CardConfig) {
    return Object.entries(card).reduce((newCard, [key, value]) => {
      if (key === 'views') {
        newCard[key] = value as Mode[]
      } else {
        newCard[key as DetailKey] = (value as any)[mode.value]
      }
      return newCard
    }, {} as CardConfigMode)
  }

  const tableId = computed(() => {
    const variation = activeTableVariation.value
    return `${cardId}_${mode.value}_${variation}` as const
  })

  const tableConfig = computed(() => {
    const localConfig = config.value
    if (!localConfig) {
      return
    }

    return (
      localConfig.tableConfigs[tableId.value] ??
      hardCodedConfig.tableConfigs[tableId.value]
    )
  })

  const activeTableVariation = useWritableComputed('', {
    get: (val: string) => val || (card.value?.tableVariations?.[0] ?? ''),
    set: (val: string, oldVal?: string) => {
      if (val === oldVal) {
        // noop
        return oldVal
      }
      nextTick(() => tabulator.value.clearData())
      return val
    },
  })

   const localStorageKeyColumVisibility = computed(
    () =>
      `tabulator_${cardId}_${mode.value}_${activeTableVariation.value}_ColumnVisibility`
  )
  return {
    tableConfig,
    card,
    activeTableVariation,
    tableId,
    localStorageKeyColumVisibility
  }
}
