import { gManufacturing } from '@tset/shared-utils/tests/generators/manufacturing'
import { gResultField } from '@tset/shared-utils/tests/generators/resultField'
import { getGroupObject } from './getEntityTableData.util'

describe('getGroupObject', () => {
  describe('check groupField', () => {
    it('- will return without groupField', () => {
      const actual = getGroupObject(gManufacturing(), [])
      expect(actual).toStrictEqual({})
    })
  })
  describe('check groupId', () => {
    it('- will return without finding groupId', () => {
      const actual = getGroupObject(gManufacturing(), [], 'someField')
      expect(actual).toStrictEqual({})
    })
  })
  describe('check return', () => {
    describe('check id', () => {
      it('- will return groupId', () => {
        const actual = getGroupObject(
          gManufacturing({
            fields: [gResultField({ name: 'groupId', value: 'my-group-id' })],
          }),
          [],
          'groupId'
        )
        expect(actual.id).toBe('my-group-id')
      })
    })
    describe('check outOfSyncGroupParams', () => {
      it('- will return the field named by the param', () => {
        const actual = getGroupObject(
          gManufacturing({
            fields: [
              gResultField({ name: 'groupId', value: 'my-group-id' }),
              gResultField({
                name: 'inconsistencyField',
                value: 'my-inconsistency',
              }),
            ],
          }),
          [],
          'groupId',
          'inconsistencyField'
        )
        expect(actual.outOfSyncGroupParams).toBe('my-inconsistency')
      })
    })
    describe('check isFirst', () => {
      it('- will return true, when last manufacturing', () => {
        const actual = getGroupObject(
          gManufacturing({
            id: 'my-id',
            fields: [gResultField({ name: 'groupId', value: 'my-group-id' })],
          }),
          [
            gManufacturing({
              fields: [gResultField({ name: 'groupId', value: 'my-group-id' })],
            }),
            gManufacturing({
              id: 'my-id',
              fields: [gResultField({ name: 'groupId', value: 'my-group-id' })],
            }),
          ],
          'groupId'
        )
        expect(actual.isFirst).toBeTruthy()
      })
      it('- will return false, when some other manufacturing', () => {
        const actual = getGroupObject(
          gManufacturing({
            id: 'my-id',
            fields: [gResultField({ name: 'groupId', value: 'my-group-id' })],
          }),
          [
            gManufacturing({
              fields: [gResultField({ name: 'groupId', value: 'my-group-id' })],
            }),
            gManufacturing({
              id: 'my-id',
              fields: [gResultField({ name: 'groupId', value: 'my-group-id' })],
            }),
            gManufacturing({
              fields: [gResultField({ name: 'groupId', value: 'my-group-id' })],
            }),
          ],
          'groupId'
        )
        expect(actual.isFirst).toBeFalsy()
      })
    })
    describe('check isLast', () => {
      it('- will return true, when first manufacturing', () => {
        const actual = getGroupObject(
          gManufacturing({
            id: 'my-id',
            fields: [gResultField({ name: 'groupId', value: 'my-group-id' })],
          }),
          [
            gManufacturing({
              id: 'my-id',
              fields: [gResultField({ name: 'groupId', value: 'my-group-id' })],
            }),
            gManufacturing({
              fields: [gResultField({ name: 'groupId', value: 'my-group-id' })],
            }),
          ],
          'groupId'
        )
        expect(actual.isLast).toBeTruthy()
      })
      it('- will return false, when some other manufacturing', () => {
        const actual = getGroupObject(
          gManufacturing({
            id: 'my-id',
            fields: [gResultField({ name: 'groupId', value: 'my-group-id' })],
          }),
          [
            gManufacturing({
              fields: [gResultField({ name: 'groupId', value: 'my-group-id' })],
            }),
            gManufacturing({
              id: 'my-id',
              fields: [gResultField({ name: 'groupId', value: 'my-group-id' })],
            }),
            gManufacturing({
              fields: [gResultField({ name: 'groupId', value: 'my-group-id' })],
            }),
          ],
          'groupId'
        )
        expect(actual.isLast).toBeFalsy()
      })
    })
  })
})
