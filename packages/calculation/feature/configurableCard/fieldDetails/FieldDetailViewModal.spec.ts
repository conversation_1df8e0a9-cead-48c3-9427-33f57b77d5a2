import {
  gManufacturingEntity,
  initManufacturingDbWithManufacturing,
} from '@tset/shared-utils/tests/generators/manufacturingEntity'
import { withAxiosMock } from '@tset/shared-utils/tests/mocks/withAxiosMock'
import { createMockComponent } from '@tset/shared-utils/tests/mocks/withLocalVue'
import { withModalsMock } from '@tset/shared-utils/tests/mocks/withModalsMock'
import { mount, type ComponentMountingOptions } from '@vue/test-utils'
import { computed } from 'vue'
import ConfigurableCard from '../components/ConfigurableCard.vue'
import FieldDetailViewModal from './FieldDetailViewModal.vue'

withAxiosMock()
const { hideSpy } = withModalsMock()

//#region SETUP FACTORY
const given = () => {
  const manufacturing = initManufacturingDbWithManufacturing(
    gManufacturingEntity()
  )
  const props: InstanceType<typeof FieldDetailViewModal>['$props'] = {
    entity: computed(() => manufacturing),
    modalName: 'processed-material',
    cardId: 'PRODUCTION',
  }
  const mountOptions: ComponentMountingOptions<typeof FieldDetailViewModal> = {
    props: {
      ...props,
    },
    global: {
      stubs: {
        ConfigurableCard: true,
        IconClose: true,
        BaseModal: createMockComponent('BaseModal', {
          header: {},
          content: {},
        }),
      },
    },
  }

  const wrapper = mount(FieldDetailViewModal, mountOptions)

  //#region HELPERS
  const hasConfigurableCard = () =>
    wrapper.findComponent(ConfigurableCard).isVisible()
  const clickCloseButton = () =>
    wrapper.findByDataTest('button-close-modal').trigger('click')
  //#endregion HELPERS

  return {
    wrapper,
    then: { hasConfigurableCard },
    when: { clickCloseButton },
  }
}
//#endregion SETUP FACTORY

//#region TESTS
describe('Test component functionality', () => {
  it('- closes modal on click', async () => {
    const { when } = given()
    await when.clickCloseButton()
    expect(hideSpy).toHaveBeenLastCalledWith('processed-material')
  })

  it('- renders a configurable card', () => {
    const { then } = given()
    expect(then.hasConfigurableCard()).toBe(true)
  })
})
//#endregion TESTS
