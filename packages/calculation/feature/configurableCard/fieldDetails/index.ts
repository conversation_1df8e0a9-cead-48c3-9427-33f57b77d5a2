import { isMissingValueField } from '@domain/calculation/utils/isMissingFieldValue'
import { computed } from 'vue'
import { getSectionFields } from '../utils/configuration.util'
import {
  customConfigs,
  hardCodedFieldDetailsConfig,
} from './fieldDetails.config'
import {
  navigateToObjectPage,
  resolveFieldContextEntity,
} from './fieldDetails.utils'
import { openCardModal, openCustomModal } from './fieldDetailViewOpener'

export function openFieldDetailsView(field: ResultField) {
  // check if the detailsCardId is present in the custom config hardcoded on the frontend.
  // if it is not present we can assume that the detailsCardId was injected by the backend and there is a corresponding cardId present on the parentEntity uiConfigKeys
  const config = customConfigs.find((c) =>
    [c.key, c.cardId].includes(
      field.metaInfo?.navigateToObject ?? field.metaInfo?.detailsCardId
    )
  )
  // the if block is to prevent opening an empty card modal because backend are currently not injecting detailsCardId
  // it will be removed when backend starts injecting detailsCardId and hasMissingDetails
  if (!config) {
    return
  }

  const { contextEntity, type, cardId } = config
  const entity = computed(
    () => resolveFieldContextEntity(field, contextEntity)!
  )

  if (!entity.value) {
    return
  }
  if (type === 'customComponent') {
    openCustomModal(config.component, entity.value)
  } else if (type === 'navigation') {
    navigateToObjectPage(entity.value, { stepId: entity.value.id })
  } else {
    // fallback for all, open a configurable with the provided cardId
    openCardModal(cardId ?? field.metaInfo?.detailsCardId, entity)
  }
}

/**
 * Receives a field and it augments the field with details metaInfo if there is a declared field details config
 */
export function getFieldDetailConfig(field: ResultField) {
  const config = customConfigs.find(
    (config) =>
      field.name === config.key ||
      field.metaInfo?.navigateToObject === config.key
  )

  if (!config) {
    return field
  }
  const newField = { ...field }
  newField.metaInfo ??= {}
  newField.metaInfo.hasPopup = true
  newField.metaInfo.detailsLabel ??= config.detailsLabel
  newField.metaInfo.detailsCardId ??= config.cardId ?? config.key

  return newField
}

function checkMissingFieldInConfig(field: ResultField, mode: Mode): boolean {
  const config = customConfigs.find((c) =>
    [c.key, c.cardId].includes(
      field.metaInfo?.navigateToObject ?? field.metaInfo?.detailsCardId
    )
  )
  if (!config) {
    return false
  }
  const { contextEntity, type, cardId } = config
  const entity = resolveFieldContextEntity(field, contextEntity)
  if (!entity) {
    return false
  }
  if (type === 'card') {
    return Object.values(
      hardCodedFieldDetailsConfig[cardId].fields?.[mode] ?? {}
    )
      .flatMap((sections) => sections)
      .flatMap((section) => getSectionFields(section, entity))
      .some((f) => f.name !== field.name && isMissingValueField(field))
  } else if (type === 'customComponent') {
    return entity
      .getChildren()
      .some((e) => e.hasMissingFields({ checkChildren: false }))
  }
  return false
}

export function getFieldDetailSeverity(field: ResultField, mode: Mode) {
  const hasMissingField = checkMissingFieldInConfig(field, mode)
  if (hasMissingField) {
    field = { ...field }
    field.metaInfo ??= {}
    field.metaInfo.hasMissingDetails = hasMissingField
  }
  return field
}
