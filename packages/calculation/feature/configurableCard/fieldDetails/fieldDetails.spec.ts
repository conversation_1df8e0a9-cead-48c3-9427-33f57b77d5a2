import { navigationStore } from '@/store'
import { manufacturingStore } from '@domain/calculation/manufacturing.store'
import { PAGE } from '@tset/shared-model/navigation/navigation'
import ExchangeRatesModal from '@tset/shared-ui/manufacturing/ExchangeRatesModal.vue'
import LocationCostFactorsModal from '@tset/shared-ui/manufacturing/LocationCostFactorsModal.vue'
import ShapeSelectionModal from '@tset/shared-ui/manufacturing/ShapeSelectionModal.vue'
import ProcessedMaterials from '@tset/shared-ui/step/ProcessedMaterials.vue'
import { gBomNodeEntity } from '@tset/shared-utils/tests/generators/bomNodeEntity'
import { gManufacturingEntity } from '@tset/shared-utils/tests/generators/manufacturingEntity'
import { gResultField } from '@tset/shared-utils/tests/generators/resultField'
import { withModalsMock } from '@tset/shared-utils/tests/mocks/withModalsMock'
import { openFieldDetailsView } from '.'
import FieldDetailViewModal from './FieldDetailViewModal.vue'

const modalMock = withModalsMock()

describe('Field details view handler', () => {
  beforeEach(() => {
    expect.hasAssertions()
  })

  describe('Custom component', () => {
    it('- opens processed materials', () => {
      setup({
        field: gResultField({
          metaInfo: {
            detailsCardId: 'processedMaterials',
            parentEntity: 'entity-id',
          },
        }),
        parentEntity: gManufacturingEntity({ id: 'entity-id' }),
      })
      expect(modalMock.showSpy).toHaveBeenCalledWith(
        expect.objectContaining({ component: ProcessedMaterials })
      )
    })
    it('- opens exchange rates', () => {
      setup({
        field: gResultField({
          metaInfo: { detailsCardId: 'baseCurrency' },
        }),
        parentEntity: gManufacturingEntity({
          id: 'entity-id',
          type: 'MD_EXCHANGERATE_PARENT',
        }),
      })
      expect(modalMock.showSpy).toHaveBeenCalledWith(
        expect.objectContaining({ component: ExchangeRatesModal })
      )
    })
    it('- opens location cost factor', () => {
      setup({
        field: gResultField({
          metaInfo: { detailsCardId: 'location' },
        }),
        parentEntity: gManufacturingEntity({
          id: 'entity-id',
          type: 'MD_COSTFACTORS_PARENT',
        }),
      })
      expect(modalMock.showSpy).toHaveBeenCalledWith(
        expect.objectContaining({ component: LocationCostFactorsModal })
      )
    })
    it('- opens shape selection modal', () => {
      setup({
        field: gResultField({
          metaInfo: { detailsCardId: 'shapeId' },
        }),
        parentEntity: gManufacturingEntity({ id: 'entity-id' }),
      })
      expect(modalMock.showSpy).toHaveBeenCalledWith(
        expect.objectContaining({ component: ShapeSelectionModal })
      )
    })
  })

  describe('Configurable card', () => {
    it('- opens the field view card modal', () => {
      setup({
        field: gResultField({ metaInfo: { detailsCardId: 'PRODUCTION' } }),
        parentEntity: gManufacturingEntity({ id: 'entity-id' }),
      })
      expect(modalMock.showSpy).toHaveBeenCalledWith(
        expect.objectContaining({ component: FieldDetailViewModal })
      )
    })
  })

  describe('Navigation', () => {
    describe('when the field has SYSTEM_PARAMETER object view', () => {
      describe('for parentEntity of type', () => {
        describe('MACHINE', () => {
          it('- navigate to PAGE.MANU_MACHINE', async () =>
            await forParentType('MACHINE').shouldNavigateTo(PAGE.MANU_MACHINE))
        })

        describe('LABOR', () => {
          it('- navigate to PAGE.MANU_LABOR', async () =>
            await forParentType('LABOR').shouldNavigateTo(PAGE.MANU_LABOR))
        })

        describe('TOOL', () => {
          it('- navigate to PAGE.MANU_TOOL', async () =>
            await forParentType('TOOL').shouldNavigateTo(PAGE.MANU_TOOL))
        })

        describe('CYCLETIME_STEP', () => {
          it('- navigate to PAGE.MANU_CYCLE', async () =>
            await forParentType('CYCLETIME_STEP').shouldNavigateTo(
              PAGE.MANU_CYCLE
            ))
        })

        describe('MANUFACTURING_STEP', () => {
          it('- navigate to PAGE.MANU_SYSTEMPARAMETERS', async () =>
            await forParentType('MANUFACTURING_STEP').shouldNavigateTo(
              PAGE.MANU_SYSTEMPARAMETERS
            ))
        })

        describe('for any other type', () => {
          it('- navigate to PAGE.MANU_STEP', async () =>
            await forParentType('MATERIAL').shouldNavigateTo(PAGE.MANU_STEP))
        })

        function forParentType(parentType: ManufacturingEntityType) {
          return {
            async shouldNavigateTo(page: PAGE) {
              const { getLastNavigationTarget } = setup({
                field: gResultField({
                  metaInfo: {
                    navigateToObject: 'SYSTEM_PARAMETER',
                  },
                }),
                parentEntity: gManufacturingEntity({ type: parentType }),
              })

              expect(getLastNavigationTarget()).toEqual(page)
            },
          }
        }
      })
    })
  })
})

function setup({
  field,
  parentEntity,
}: {
  field: ResultField
  parentEntity: ManufacturingDTO
}) {
  manufacturingStore.setNodeInt(
    gBomNodeEntity({
      manufacturing: parentEntity,
    })
  )
  field.metaInfo ??= {}
  field.metaInfo.parentEntity = parentEntity.id
  const navigateToSpy = vi
    .spyOn(navigationStore, 'navigateTo')
    .mockImplementation(vi.fn())

  const getLastNavigationTarget = () =>
    navigateToSpy.mock.lastCall?.[0]?.targetPage

  openFieldDetailsView(field)

  return { getLastNavigationTarget }
}
