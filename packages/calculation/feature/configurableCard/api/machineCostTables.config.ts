import { gResultField } from '@tset/shared-utils/tests/generators/resultField'
import { CellType, type FieldTableConfig } from '../model/fieldTable.model'

export const machineCostTableCost: FieldTableConfig = {
  type: 'field',
  rows: ['costDuringProduction', 'costDuringSetUp', 'costDuringNonOccupancy'],
  rowDefinitions: {
    costDuringProduction: {
      id: 'costDuringProduction',
      cells: [
        {
          type: CellType.VALUE,
          columnId: '1',
          field: gResultField({
            name: 'displayDesignation',
            value: 'Cost during production',
          }),
        },
        {
          type: CellType.LOOKUP,
          columnId: '2',
          collectFrom: { type: 'self' },
          fieldName: 'costDuringProductionPerYear',
        },
        {
          type: CellType.LOOKUP,
          columnId: '3',
          collectFrom: { type: 'self' },
          fieldName: 'costDuringProductionPerPart',
        },
      ],
      rows: ['fixedCostDuringProduction', 'variableCostDuringProduction'],
    },
    fixedCostDuringProduction: {
      id: 'fixedCostDuringProduction',
      cells: [
        {
          type: CellType.VALUE,
          columnId: '1',
          field: gResultField({
            name: 'displayDesignation',
            value: 'Fixed cost',
          }),
        },
        {
          type: CellType.LOOKUP,
          columnId: '2',
          collectFrom: { type: 'self' },
          fieldName: 'fixedCostDuringProductionPerYear',
        },
        {
          type: CellType.LOOKUP,
          columnId: '3',
          collectFrom: { type: 'self' },
          fieldName: 'fixedCostDuringProductionPerPart',
        },
      ],
    },
    variableCostDuringProduction: {
      id: 'variableCostDuringProduction',
      cells: [
        {
          type: CellType.VALUE,
          columnId: '1',
          field: gResultField({
            name: 'displayDesignation',
            value: 'Variable cost',
          }),
        },
        {
          type: CellType.LOOKUP,
          columnId: '2',
          collectFrom: { type: 'self' },
          fieldName: 'variableCostDuringProductionPerYear',
        },
        {
          type: CellType.LOOKUP,
          columnId: '3',
          collectFrom: { type: 'self' },
          fieldName: 'variableCostDuringProductionPerPart',
        },
      ],
    },
    costDuringSetUp: {
      id: 'costDuringSetUp',
      cells: [
        {
          type: CellType.VALUE,
          columnId: '1',
          field: gResultField({
            name: 'displayDesignation',
            value: 'Cost during setup',
          }),
        },
        {
          type: CellType.LOOKUP,
          columnId: '2',
          collectFrom: { type: 'self' },
          fieldName: 'costDuringSetUpPerYear',
        },
        {
          type: CellType.LOOKUP,
          columnId: '3',
          collectFrom: { type: 'self' },
          fieldName: 'costDuringSetUpPerPart',
        },
      ],
      rows: ['fixedCostDuringSetUp', 'variableCostDuringSetUp'],
    },
    fixedCostDuringSetUp: {
      id: 'fixedCostDuringSetUp',
      cells: [
        {
          type: CellType.VALUE,
          columnId: '1',
          field: gResultField({
            name: 'displayDesignation',
            value: 'Fixed cost',
          }),
        },
        {
          type: CellType.LOOKUP,
          columnId: '2',
          collectFrom: { type: 'self' },
          fieldName: 'fixedCostDuringSetUpPerYear',
        },
        {
          type: CellType.LOOKUP,
          columnId: '3',
          collectFrom: { type: 'self' },
          fieldName: 'fixedCostDuringSetUpPerPart',
        },
      ],
    },
    variableCostDuringSetUp: {
      id: 'variableCostDuringSetUp',
      cells: [
        {
          type: CellType.VALUE,
          columnId: '1',
          field: gResultField({
            name: 'displayDesignation',
            value: 'Variable cost',
          }),
        },
        {
          type: CellType.LOOKUP,
          columnId: '2',
          collectFrom: { type: 'self' },
          fieldName: 'variableCostDuringSetUpPerYear',
        },
        {
          type: CellType.LOOKUP,
          columnId: '3',
          collectFrom: { type: 'self' },
          fieldName: 'variableCostDuringSetUpPerPart',
        },
      ],
    },
    costDuringNonOccupancy: {
      id: 'costDuringNonOccupancy',
      cells: [
        {
          type: CellType.VALUE,
          columnId: '1',
          field: gResultField({
            name: 'displayDesignation',
            value: 'Cost during non occupancy',
          }),
        },
        {
          type: CellType.LOOKUP,
          columnId: '2',
          collectFrom: { type: 'self' },
          fieldName: 'costDuringNonOccupancyPerYear',
        },
        {
          type: CellType.LOOKUP,
          columnId: '3',
          collectFrom: { type: 'self' },
          fieldName: 'costDuringNonOccupancyPerPart',
        },
      ],
      rows: ['fixedCostNonOccupancy', 'variableCostNonOccupancy'],
    },
    fixedCostNonOccupancy: {
      id: 'fixedCostNonOccupancy',
      cells: [
        {
          type: CellType.VALUE,
          columnId: '1',
          field: gResultField({
            name: 'displayDesignation',
            value: 'Fixed cost',
          }),
        },
        {
          type: CellType.LOOKUP,
          columnId: '2',
          collectFrom: { type: 'self' },
          fieldName: 'fixedCostNonOccupancyPerYear',
        },
        {
          type: CellType.LOOKUP,
          columnId: '3',
          collectFrom: { type: 'self' },
          fieldName: 'fixedCostNonOccupancyPerPart',
        },
      ],
    },
    variableCostNonOccupancy: {
      id: 'variableCostNonOccupancy',
      cells: [
        {
          type: CellType.VALUE,
          columnId: '1',
          field: gResultField({
            name: 'displayDesignation',
            value: 'Variable cost',
          }),
        },
        {
          type: CellType.LOOKUP,
          columnId: '2',
          collectFrom: { type: 'self' },
          fieldName: 'variableCostNonOccupancyPerYear',
        },
        {
          type: CellType.LOOKUP,
          columnId: '3',
          collectFrom: { type: 'self' },
          fieldName: 'variableCostNonOccupancyPerPart',
        },
      ],
    },
  },
  columns: [
    { id: '1', options: { displayDesignation: 'displayDesignation' } },
    {
      id: '2',
      options: {
        hasTotal: true,
        displayDesignation: 'Cost',
      },
    },
    { id: '3', options: { hasTotal: true, displayDesignation: 'Cost' } },
  ],
}

export const machineCostTableCo2: FieldTableConfig = {
  type: 'field',
  rows: ['cO2DuringProduction', 'cO2NonOccupancy'],
  rowDefinitions: {
    cO2DuringProduction: {
      id: 'cO2DuringProduction',
      cells: [
        {
          type: CellType.VALUE,
          field: gResultField({
            name: 'displayDesignation',
            value: 'Emission during production',
            metaInfo: { translationSection: 'fields' },
          }),
          columnId: '1',
        },
        {
          type: CellType.LOOKUP,
          fieldName: 'cO2DuringProductionPerYear',
          collectFrom: { type: 'self' },
          columnId: '2',
        },
        {
          type: CellType.LOOKUP,
          fieldName: 'cO2DuringProductionPerPart',
          collectFrom: { type: 'self' },
          columnId: '3',
        },
      ],
      rows: ['fixedCO2DuringProduction', 'variableCO2DuringProduction'],
    },
    fixedCO2DuringProduction: {
      id: 'fixedCO2DuringProduction',
      cells: [
        {
          type: CellType.VALUE,
          field: gResultField({
            name: 'displayDesignation',
            value: 'Fixed emission',
          }),
          columnId: '1',
        },
        {
          type: CellType.LOOKUP,
          fieldName: 'fixedCO2DuringProductionPerYear',
          collectFrom: { type: 'self' },
          columnId: '2',
        },
        {
          type: CellType.LOOKUP,
          fieldName: 'fixedCO2DuringProductionPerPart',
          collectFrom: { type: 'self' },
          columnId: '3',
        },
      ],
    },
    variableCO2DuringProduction: {
      id: 'variableCO2DuringProduction',
      cells: [
        {
          type: CellType.VALUE,
          field: gResultField({
            name: 'displayDesignation',
            value: 'Variable emission',
          }),
          columnId: '1',
        },
        {
          type: CellType.LOOKUP,
          fieldName: 'variableCO2DuringProductionPerYear',
          collectFrom: { type: 'self' },
          columnId: '2',
        },
        {
          type: CellType.LOOKUP,
          fieldName: 'variableCO2DuringProductionPerPart',
          collectFrom: { type: 'self' },
          columnId: '3',
        },
      ],
    },
    cO2NonOccupancy: {
      id: 'cO2NonOccupancy',
      cells: [
        {
          type: CellType.VALUE,
          field: gResultField({
            name: 'displayDesignation',
            value: 'Emission non occupancy',
          }),
          columnId: '1',
        },
        {
          type: CellType.LOOKUP,
          fieldName: 'cO2NonOccupancyPerYear',
          collectFrom: { type: 'self' },
          columnId: '2',
        },
        {
          type: CellType.LOOKUP,
          fieldName: 'cO2NonOccupancyPerPart',
          collectFrom: { type: 'self' },
          columnId: '3',
        },
      ],
      rows: ['fixedCO2NonOccupancy'],
    },
    fixedCO2NonOccupancy: {
      id: 'fixedCO2NonOccupancy',
      cells: [
        {
          type: CellType.VALUE,
          field: gResultField({
            name: 'displayDesignation',
            value: 'Fixed emission',
          }),
          columnId: '1',
        },
        {
          type: CellType.LOOKUP,
          fieldName: 'fixedCO2NonOccupancyPerYear',
          collectFrom: { type: 'self' },
          columnId: '2',
        },
        {
          type: CellType.LOOKUP,
          fieldName: 'fixedCO2NonOccupancyPerPart',
          collectFrom: { type: 'self' },
          columnId: '3',
        },
      ],
    },
  },
  columns: [
    { id: '1', options: { displayDesignation: 'displayDesignation' } },
    { id: '2', options: { displayDesignation: 'emission', hasTotal: true } },
    { id: '3', options: { displayDesignation: 'emission', hasTotal: true } },
  ],
}
