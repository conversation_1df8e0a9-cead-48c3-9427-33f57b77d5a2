import { gResultField } from '@tset/shared-utils/tests/generators/resultField'
import type { FieldTableConfig } from '../model/fieldTable.model'
import { CellType } from '../model/fieldTable.model'

export const machineHourlyRateTableCost: FieldTableConfig = {
  type: 'field',
  rows: ['fixedCost', 'variableCost'],
  rowDefinitions: {
    fixedCost: {
      id: 'fixedCost',
      cells: [
        {
          type: CellType.VALUE,
          columnId: '1',
          field: gResultField({
            name: 'displayDesignation',
            value: 'Fixed Cost',
            source: 'C',
          }),
        },
        {
          type: CellType.LOOKUP,
          columnId: '2',
          collectFrom: { type: 'self' },
          fieldName: 'fixedCostPerYear',
        },
        {
          type: CellType.LOOKUP,
          columnId: '3',
          collectFrom: { type: 'self' },
          fieldName: 'fixedCostPerHour',
        },
      ],
      rows: ['depreciation', 'interest', 'space'],
    },
    depreciation: {
      id: 'depreciation',
      cells: [
        {
          type: CellType.VALUE,
          columnId: '1',
          field: gResultField({
            name: 'displayDesignation',
            value: 'Depreciation',
            source: 'C',
          }),
        },
        {
          type: CellType.LOOKUP,
          columnId: '2',
          collectFrom: { type: 'self' },
          fieldName: 'depreciationCostPerYear',
        },
        {
          type: CellType.LOOKUP,
          columnId: '3',
          collectFrom: { type: 'self' },
          fieldName: 'depreciationCostPerHour',
        },
      ],
    },
    interest: {
      id: 'interest',
      cells: [
        {
          type: CellType.VALUE,
          columnId: '1',
          field: gResultField({
            name: 'displayDesignation',
            value: 'Interest',
            source: 'C',
          }),
        },
        {
          type: CellType.LOOKUP,
          columnId: '2',
          collectFrom: { type: 'self' },
          fieldName: 'interestCostPerYear',
        },
        {
          type: CellType.LOOKUP,
          columnId: '3',
          collectFrom: { type: 'self' },
          fieldName: 'interestCostPerHour',
        },
      ],
    },
    space: {
      id: 'space',
      cells: [
        {
          type: CellType.VALUE,
          columnId: '1',
          field: gResultField({
            name: 'displayDesignation',
            value: 'Space',
            source: 'C',
          }),
        },
        {
          type: CellType.LOOKUP,
          columnId: '2',
          collectFrom: { type: 'self' },
          fieldName: 'surfaceCostPerYear',
        },
        {
          type: CellType.LOOKUP,
          columnId: '3',
          collectFrom: { type: 'self' },
          fieldName: 'surfaceCostPerHour',
        },
      ],
    },
    variableCost: {
      id: 'variableCost',
      cells: [
        {
          type: CellType.VALUE,
          columnId: '1',
          field: gResultField({
            name: 'displayDesignation',
            value: 'Variable Cost',
            source: 'C',
          }),
        },
        {
          type: CellType.LOOKUP,
          columnId: '2',
          collectFrom: { type: 'self' },
          fieldName: 'variableCostPerYear',
        },
        {
          type: CellType.LOOKUP,
          columnId: '3',
          collectFrom: { type: 'self' },
          fieldName: 'variableCostPerHour',
        },
      ],
      rows: ['electricity', 'maintenance', 'consumable'],
    },
    electricity: {
      id: 'electricity',
      cells: [
        {
          type: CellType.VALUE,
          columnId: '1',
          field: gResultField({
            name: 'displayDesignation',
            value: 'Electricity',
            source: 'C',
          }),
        },
        {
          type: CellType.LOOKUP,
          columnId: '2',
          collectFrom: { type: 'self' },
          fieldName: 'energyCostPerYear',
        },
        {
          type: CellType.LOOKUP,
          columnId: '3',
          collectFrom: { type: 'self' },
          fieldName: 'energyCostPerHour',
        },
      ],
    },
    maintenance: {
      id: 'maintenance',
      cells: [
        {
          type: CellType.VALUE,
          columnId: '1',
          field: gResultField({
            name: 'displayDesignation',
            value: 'Maintenance',
            source: 'C',
          }),
        },
        {
          type: CellType.LOOKUP,
          columnId: '2',
          collectFrom: { type: 'self' },
          fieldName: 'maintenanceCostPerYear',
        },
        {
          type: CellType.LOOKUP,
          columnId: '3',
          collectFrom: { type: 'self' },
          fieldName: 'maintenanceCostPerHour',
        },
      ],
    },
    consumable: {
      id: 'consumable',
      cells: [
        {
          type: CellType.VALUE,
          columnId: '1',
          field: gResultField({
            name: 'displayDesignation',
            value: 'Consumable',
            source: 'C',
          }),
        },
        {
          type: CellType.LOOKUP,
          columnId: '2',
          collectFrom: { type: 'self' },
          fieldName: 'consumableCostPerYear',
        },
        {
          type: CellType.LOOKUP,
          columnId: '3',
          collectFrom: { type: 'self' },
          fieldName: 'consumableCostPerHour',
        },
      ],
    },
  },
  columns: [
    {
      id: '1',
      options: { displayDesignation: 'displayDesignation', mainColumn: true },
    },
    { id: '2', options: { displayDesignation: 'cost', hasTotal: true } },
    {
      id: '3',
      options: { displayDesignation: 'hourlyRate', hasTotal: true },
    },
  ],
}

export const machineHourlyRateTableCo2: FieldTableConfig = {
  type: 'field',
  rows: ['fixedCO2', 'variableCO2'],
  rowDefinitions: {
    fixedCO2: {
      id: 'fixedCO2',
      cells: [
        {
          type: CellType.VALUE,
          columnId: '1',
          field: gResultField({
            name: 'displayDesignation',
            value: 'Fixed emission',
            source: 'C',
          }),
        },
        {
          type: CellType.LOOKUP,
          columnId: '2',
          collectFrom: { type: 'self' },
          fieldName: 'fixedCO2PerYear',
        },
        {
          type: CellType.LOOKUP,
          columnId: '3',
          collectFrom: { type: 'self' },
          fieldName: 'fixedCO2PerHour',
        },
      ],
    },
    variableCO2: {
      id: 'variableCO2',
      cells: [
        {
          type: CellType.VALUE,
          columnId: '1',
          field: gResultField({
            name: 'displayDesignation',
            value: 'Variable emission',
            source: 'C',
          }),
        },
        {
          type: CellType.LOOKUP,
          columnId: '2',
          collectFrom: { type: 'self' },
          fieldName: 'variableCO2PerYear',
        },
        {
          type: CellType.LOOKUP,
          columnId: '3',
          collectFrom: { type: 'self' },
          fieldName: 'variableCO2PerHour',
        },
      ],
      rows: ['electricEnergyCO2', 'gasEnergyActiveCO2', 'gasEnergyPassiveCO2'],
    },
    electricEnergyCO2: {
      id: 'electricEnergyCO2',
      cells: [
        {
          type: CellType.VALUE,
          columnId: '1',
          field: gResultField({
            name: 'displayDesignation',
            value: 'Electric energy emission',
            source: 'C',
          }),
        },
        {
          type: CellType.LOOKUP,
          columnId: '2',
          collectFrom: { type: 'self' },
          fieldName: 'electricEnergyCO2PerYear',
        },
        {
          type: CellType.LOOKUP,
          columnId: '3',
          collectFrom: { type: 'self' },
          fieldName: 'electricEnergyCO2PerHour',
        },
      ],
    },
    gasEnergyActiveCO2: {
      id: 'gasEnergyActiveCO2',
      cells: [
        {
          type: CellType.VALUE,
          columnId: '1',
          field: gResultField({
            name: 'displayDesignation',
            value: 'Gas energy emission (active)',
            source: 'C',
          }),
        },
        {
          type: CellType.LOOKUP,
          columnId: '2',
          collectFrom: { type: 'self' },
          fieldName: 'gasEnergyActiveCO2PerYear',
        },
        {
          type: CellType.LOOKUP,
          columnId: '3',
          collectFrom: { type: 'self' },
          fieldName: 'gasEnergyActiveCO2PerHour',
        },
      ],
    },
    gasEnergyPassiveCO2: {
      id: 'gasEnergyPassiveCO2',
      cells: [
        {
          type: CellType.VALUE,
          columnId: '1',
          field: gResultField({
            name: 'displayDesignation',
            value: 'Gas energy emission (passive)',
            source: 'C',
          }),
        },
        {
          type: CellType.LOOKUP,
          columnId: '2',
          collectFrom: { type: 'self' },
          fieldName: 'gasEnergyPassiveCO2PerYear',
        },
        {
          type: CellType.LOOKUP,
          columnId: '3',
          collectFrom: { type: 'self' },
          fieldName: 'gasEnergyPassiveCO2PerHour',
        },
      ],
    },
  },
  columns: [
    { id: '1', options: { displayDesignation: 'displayDesignation' } },
    { id: '2', options: { hasTotal: true, displayDesignation: 'emission' } },
    { id: '3', options: { hasTotal: true, displayDesignation: 'hourlyRate' } },
  ],
}
