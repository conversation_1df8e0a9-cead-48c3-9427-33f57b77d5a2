import { manufacturingStore } from '@domain/calculation/manufacturing.store'
import * as naviHelper from '@tset/shared-utils/helpers/navigation'
import { gBomNodeEntity } from '@tset/shared-utils/tests/generators/bomNodeEntity'
import type { RowComponent } from 'tabulator-tables'
import { actionsRegistry } from './actions.data'

vi.mock('@tset/shared-utils/helpers/navigation', () => ({
  getPathForTableRow: () => 'newPath',
  navigateToMasterData: vi.fn(() => {}),
  useSetupCustomNavGuard: vi.fn(() => ({
    openNavGuard: vi.fn(() => {}),
  })),
}))

const mockRow = (data: unknown, selectedRows = [] as TableRow[]) =>
  ({
    getData: () => data,
    getTable: () => ({ getSelectedRows: () => selectedRows }),
  }) as unknown as RowComponent

describe('actionsRegistry', () => {
  it('should create all actions with correct properties', () => {
    expect(actionsRegistry()).toHaveProperty('group')
    expect(actionsRegistry()).toHaveProperty('unGroup')
    expect(actionsRegistry()).toHaveProperty('duplicate')
    expect(actionsRegistry()).toHaveProperty('openInNewTab')
    expect(actionsRegistry()).toHaveProperty('copyLinkToClipboard')
    expect(actionsRegistry()).toHaveProperty('gotoMasterData')
    expect(actionsRegistry()).toHaveProperty('delete')
  })

  it('should enable group action only if row is copyable and multiple rows are selected', () => {
    expect(
      actionsRegistry().group.isEnabled?.(mockRow({ copyable: true }, [{}, {}]))
    ).toBe(true)
    expect(
      actionsRegistry().group.isEnabled?.(
        mockRow({ copyable: false }, [{}, {}])
      )
    ).toBe(false)
    expect(
      actionsRegistry().group.isEnabled?.(mockRow({ copyable: true }, [{}]))
    ).toBe(false)
  })

  it('should enable unGroup action only if row has a stepGroup id', () => {
    expect(
      actionsRegistry().unGroup.isEnabled?.(mockRow({ stepGroup: { id: 123 } }))
    ).toBe(true)
    expect(
      actionsRegistry().unGroup.isEnabled?.(mockRow({ stepGroup: null }))
    ).toBe(false)
    expect(actionsRegistry().unGroup.isEnabled?.(mockRow({}))).toBe(false)
  })

  it('should enable duplicate action only if row is duplicatable and only one row is selected', () => {
    expect(
      actionsRegistry().duplicate.isEnabled?.(
        mockRow({ isDuplicatable: true }, [{}])
      )
    ).toBe(true)
    expect(
      actionsRegistry().duplicate.isEnabled?.(
        mockRow({ isDuplicatable: true }, [{}, {}])
      )
    ).toBe(false)
    expect(
      actionsRegistry().duplicate.isEnabled?.(
        mockRow({ isDuplicatable: false }, [{}])
      )
    ).toBe(false)
  })

  it('should enable openInNewTab action only if navigationRequest exists', () => {
    expect(
      actionsRegistry().openInNewTab.isEnabled?.(
        mockRow({ navigationRequest: true })
      )
    ).toBe(true)
    expect(actionsRegistry().openInNewTab.isEnabled?.(mockRow({}))).toBe(false)
  })

  it('should enable gotoMasterData action only if row is master data', () => {
    expect(
      actionsRegistry().gotoMasterData.isEnabled?.(
        mockRow({ isMasterData: true })
      )
    ).toBe(true)
    expect(
      actionsRegistry().gotoMasterData.isEnabled?.(
        mockRow({ isMasterData: false })
      )
    ).toBe(false)
  })

  it('should enable delete action only if row is deletable', () => {
    expect(
      actionsRegistry().delete.isEnabled?.(mockRow({ deleteable: true }))
    ).toBe(true)
    expect(
      actionsRegistry().delete.isEnabled?.(mockRow({ deleteable: false }))
    ).toBe(false)
  })

  it('should call action functions when executed', async () => {
    const consoleSpy = vi.spyOn(console, 'error')
    const row = mockRow({})
    const rowWithData = mockRow({ navigationRequest: true })
    const windowSpy = vi.spyOn(window, 'open')
    actionsRegistry().group.action?.(row)
    expect(consoleSpy).toHaveBeenCalledWith(
      'Grouping: Not yet implemented/overwritten',
      row
    )

    actionsRegistry().unGroup.action?.(row)
    expect(consoleSpy).toHaveBeenCalledWith(
      'Ungrouping: Not yet implemented/overwritten',
      row
    )

    actionsRegistry().duplicate.action?.(row)
    expect(consoleSpy).toHaveBeenCalledWith(
      'Duplicating: Not yet implemented/overwritten',
      row
    )

    actionsRegistry().openInNewTab.action?.(rowWithData)
    expect(windowSpy).toHaveBeenCalledWith('newPath', '_blank')
  })
  describe('navigateToMasterData', () => {
    const navigateToMasterDataSpy = vi.spyOn(naviHelper, 'navigateToMasterData')
    const useSetupCustomNavGuardSpy = vi.spyOn(
      naviHelper,
      'useSetupCustomNavGuard'
    )
    describe('has conflicts', () => {
      it('- does NOT navigate', async () => {
        manufacturingStore.conflicts.masterDataChanged = true
        const rowWithData = mockRow({})

        await actionsRegistry().gotoMasterData.action?.(rowWithData)

        expect(navigateToMasterDataSpy).not.toHaveBeenCalled()
        expect(useSetupCustomNavGuardSpy).not.toHaveBeenCalled()
      })
    })
    describe('has pendingChanges', () => {
      it('- openNavGuard', async () => {
        manufacturingStore.setNodeInt(
          gBomNodeEntity({
            // @ts-expect-error not a branch
            branch: { global: false },
          })
        )
        const rowWithData = mockRow({})

        await actionsRegistry().gotoMasterData.action?.(rowWithData)

        expect(useSetupCustomNavGuardSpy).toHaveBeenCalled()
      })
    })
    describe('can just navigate', () => {
      it('- navigateToMasterData', async () => {
        manufacturingStore.setNodeInt(
          gBomNodeEntity({
            // @ts-expect-error not a branch
            branch: { global: true },
          })
        )
        const rowWithData = mockRow({})

        await actionsRegistry().gotoMasterData.action?.(rowWithData)

        expect(navigateToMasterDataSpy).toHaveBeenCalled()
      })
    })
  })
})

describe('createActions factory', () => {
  it('- fulfills snapshot', () => {
    const actions = actionsRegistry()

    expect(actions).toMatchInlineSnapshot(`
      {
        "copyLinkToClipboard": {
          "action": [Function],
          "closeAfter": true,
          "icon": "IconCopyLink",
          "isEnabled": [Function],
          "key": "copyLinkToClipboard",
          "label": "actions.copyLinkToClipboard",
          "tooltip": "actions.copyLinkToClipboard",
        },
        "delete": {
          "action": [Function],
          "closeAfter": true,
          "disabledTooltip": "tooltip.cannotDeleteMasterdata",
          "icon": "IconDelete",
          "isEnabled": [Function],
          "key": "delete",
          "label": "actions.deleteEntry",
          "textColor": "error-default",
        },
        "duplicate": {
          "action": [Function],
          "icon": "IconControlsCopy",
          "isEnabled": [Function],
          "key": "duplicate",
          "label": "actions.duplicateEntry",
        },
        "gotoMasterData": {
          "action": [Function],
          "icon": "IconProductMasterdata",
          "isEnabled": [Function],
          "key": "gotoMasterData",
          "label": "actions.goToMasterData",
        },
        "group": {
          "action": [Function],
          "icon": "IconGroup",
          "isEnabled": [Function],
          "key": "group",
          "label": "group",
          "tooltip": "tooltip",
        },
        "openInNewTab": {
          "action": [Function],
          "closeAfter": true,
          "icon": "IconOpenInNew",
          "isEnabled": [Function],
          "key": "openInNewTab",
          "label": "actions.openInNewTab",
          "tooltip": "actions.openInNewTab",
        },
        "unGroup": {
          "action": [Function],
          "icon": "IconArrowLeft",
          "isEnabled": [Function],
          "key": "unGroup",
          "label": "ungroup",
          "tooltip": "tooltip",
        },
      }
    `)
  })
})
