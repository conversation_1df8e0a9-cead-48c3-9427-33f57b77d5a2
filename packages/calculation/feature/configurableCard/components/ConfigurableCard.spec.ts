import { getEntityTableData } from '@calculation/configurable-card/utils/getEntityTableData.util'
import { manufacturingStore } from '@domain/calculation/manufacturing.store'
import { VueQueryPlugin } from '@tanstack/vue-query'
import NotificationDot from '@tset/design/atoms/NotificationDot/NotificationDot.vue'
import TsetButton from '@tset/design/atoms/TsetButton'
import TsetButtonToggle from '@tset/design/molecules/TsetButtonToggle/TsetButtonToggle.vue'
import TsetTable from '@tset/design/molecules/TsetTable/TsetTable.vue'
import TsetBaseCell from '@tset/design/molecules/TsetTable/components/cells/TsetBaseCell.vue'
import TableKpiField from '@tset/shared-ui/layout/TableKpiField.vue'
import { wait } from '@tset/shared-utils/tests/general'
import {
  gManufacturingEntity,
  initManufacturingDbWithManufacturing,
} from '@tset/shared-utils/tests/generators/manufacturingEntity'
import { gResultField } from '@tset/shared-utils/tests/generators/resultField'
import { createMockComponent } from '@tset/shared-utils/tests/mocks/withLocalVue'
import {
  flushPromises,
  mount,
  type ComponentMountingOptions,
} from '@vue/test-utils'
import { createPinia, setActivePinia } from 'pinia'
import { createRouter, createWebHistory } from 'vue-router'
import { cardId } from '../api/__mocks__/mocks.config'
import { withMockApi } from '../api/configuration.api.mock'
import ConfigurableCard, { type Props } from './ConfigurableCard.vue'
import ConfigurableFields from './ConfigurableFields.vue'

//#region MOCKS
vi.mock('@/store', () => ({
  manufacturingStore: {
    updateManufacturingOverwrite: vi.fn(),
    rearrangeSteps: vi.fn(),
  },
}))

vi.mock('@calculation/configurable-card/utils/getEntityTableData.util', {
  spy: true,
})

const updateManufacturingOverwriteSpy = vi
  .spyOn(manufacturingStore, 'updateManufacturingOverwrite')
  .mockResolvedValue()
const rearrangeStepsSpy = vi
  .spyOn(manufacturingStore, 'rearrangeSteps')
  .mockResolvedValue()
//#endregion MOCKS

//#region SETUP FACTORY
function setup(
  p: Partial<Props> = {},
  withStubbedTable = false,
  entity?: ManufacturingDTO
) {
  vi.clearAllMocks()
  const props: Props = {
    contextEntity: initManufacturingDbWithManufacturing(
      entity ??
        gManufacturingEntity({
          fields: [
            gResultField({ name: 'Co2 kpi' }),
            gResultField({ name: 'Cost kpi' }),
            gResultField({ name: 'costPerPart' }),
            gResultField({ name: 'cO2PerPart' }),
          ],
        })
    ),
    cardId,
    mode: 'cost',
    ...p,
  }
  const stubbedTable = {
    CellFormatter: createMockComponent('CellFormatter', {
      default: { cell: {} },
    }),
    TsetTable: createMockComponent('TsetTable'),
    TsetBaseCell: true,
  }
  const mountOptions: ComponentMountingOptions<typeof ConfigurableCard> = {
    props,
    global: {
      plugins: [
        VueQueryPlugin,
        createRouter({
          history: createWebHistory(),
          routes: [{ path: '/', component: {} }],
        }),
      ],
      stubs: {
        ...(withStubbedTable ? stubbedTable : {}),
        ConfigurableFields: true,
        IconChevronDown: true,
        IconTableSort: true,
        IconTableSortAsc: true,
        IconChevronRight: true,
        IconUnlink: true,
        IconSpinner: true,
        IconInfoDetails: true,
      },
    },
  }

  const wrapper = mount(ConfigurableCard, mountOptions)

  //#region HELPERS
  const getTable = () => wrapper.findComponent(TsetTable)
  const getTableProps = () => getTable().props()
  // @ts-expect-error honestly i don't know why it complains
  const getVariationSwitcher = () => wrapper.findComponent(TsetButtonToggle)
  const getVariationButtons = () => wrapper.findAllComponents(TsetButton)
  const clickButton = (i: number) =>
    getVariationButtons()?.at(i)?.trigger('click')
  const getKpi = () => wrapper.findComponent(TableKpiField)

  const getConfigurableFields = () =>
    wrapper.findAllComponents(ConfigurableFields)
  const getFieldConfig = () =>
    getConfigurableFields()?.map((it) => it.props('config'))
  const getCell = () => wrapper.findComponent(TsetBaseCell)
  const emitCellChange = (event: FieldCellEditEvent) =>
    getCell().vm.$emit('change', event)
  const hasNotificationDot = () =>
    wrapper.findComponent(NotificationDot).exists()
  //#endregion HELPERS

  return {
    wrapper,
    when: {
      clickButton,
      emitCellChange,
    },
    then: {
      getTable,
      getTableProps,
      getVariationSwitcher,
      getKpi,
      getFieldConfig,
      hasNotificationDot,
    },
  }
}
//#endregion SETUP FACTORY

//#region TESTS
describe('ConfigurableCard', () => {
  beforeEach(() => {
    setActivePinia(createPinia())
  })
  describe('- Table', () => {
    it('when config is loading', async () => {
      withMockApi({
        waitTime: 5,
      })
      const { then } = setup()
      expect(then.getTable().exists()).toBe(false)
      await wait(10)
      expect(then.getTable().exists()).toBe(true)
    })
    describe('hasTable', () => {
      describe('with zero tableVariations for given mode', () => {
        it(' - shows NO Table', async () => {
          withMockApi({
            cardConfig: {
              views: ['cost', 'co2'],
              title: { cost: '', co2: '' },
              tableVariations: { cost: [], co2: ['1', '2'] },
            },
          })
          const { then } = setup()
          await flushPromises()
          expect(then.getTable().exists()).toBe(false)
        })
        describe('with one or more tableVariations for given mode', () => {
          it(' - shows Table', async () => {
            withMockApi({
              cardConfig: {
                views: ['cost', 'co2'],
                title: { cost: '', co2: '' },
                tableVariations: { cost: ['hello'], co2: ['1', '2'] },
              },
            })
            const { then } = setup()
            await flushPromises()
            expect(then.getTable().exists()).toBe(true)
          })

          describe('when emitting a cell change', () => {
            describe('with update action', () => {
              it(' - dispatches the update action', async () => {
                withMockApi({
                  cardConfig: {
                    views: ['cost', 'co2'],
                    title: { cost: '', co2: '' },
                    tableVariations: { cost: ['hello'], co2: ['1', '2'] },
                  },
                })
                const { when } = setup({}, true)
                await flushPromises()

                const updatedField = gResultField()
                const parentEntity = gManufacturingEntity()
                when.emitCellChange({
                  action: 'updateManufacturingOverwrite',
                  field: updatedField,
                  parent: parentEntity,
                })
                expect(updateManufacturingOverwriteSpy).toHaveBeenCalledWith(
                  expect.objectContaining({
                    name: updatedField.name,
                    value: updatedField.value,
                    entityId: parentEntity.id,
                  })
                )
              })
            })

            describe('with rearrange action', () => {
              it(' - dispatches the rearrange action', async () => {
                withMockApi({
                  cardConfig: {
                    views: ['cost', 'co2'],
                    title: { cost: '', co2: '' },
                    tableVariations: { cost: ['hello'], co2: ['1', '2'] },
                  },
                })
                const { when } = setup({}, true)
                await flushPromises()

                const updatedField = gResultField()
                const parentEntity = gManufacturingEntity()
                when.emitCellChange({
                  action: 'rearrangeSteps',
                  field: updatedField,
                  parent: parentEntity,
                })
                expect(rearrangeStepsSpy).toHaveBeenCalledWith({
                  parentId: updatedField.value,
                  entityId: parentEntity.id,
                })
              })
            })
          })
        })
      })
    })
    describe('missing hint', () => {
      const mockedTableData = vi.mocked(getEntityTableData)
      it('- show hint if table row has missing fields', async () => {
        mockedTableData.mockReturnValueOnce({
          rows: [{ entityHasMissingFields: true }, {}],
          columns: [],
          options: {},
        })
        withMockApi({
          cardConfig: {
            views: ['cost', 'co2'],
            title: { cost: '', co2: '' },
            tableVariations: { cost: ['hello'], co2: ['1', '2'] },
          },
          tableConfigs: {
            [`${cardId}_cost_hello`]: {
              type: 'entity',
              columns: [],
              rows: [],
              rowDefinitions: {},
            },
          },
        })
        const { then } = setup()
        await flushPromises()
        await wait(0)
        expect(then.hasNotificationDot()).toBe(true)
      })
      it('- no if table row has no missing fields', async () => {
        mockedTableData.mockReturnValueOnce({
          rows: [{}, {}],
          columns: [],
          options: {},
        })
        withMockApi({
          cardConfig: {
            views: ['cost', 'co2'],
            title: { cost: '', co2: '' },
            tableVariations: { cost: ['hello'], co2: ['1', '2'] },
          },
          tableConfigs: {
            [`${cardId}_cost_hello`]: {
              type: 'entity',
              columns: [],
              rows: [],
              rowDefinitions: {},
            },
          },
        })
        const { then } = setup()
        await flushPromises()
        await wait(0)
        expect(then.hasNotificationDot()).toBe(false)
      })
    })
  })
  describe('- Variations toggle', () => {
    it('- only visible when configuration has multiple variations', async () => {
      withMockApi({
        cardConfig: {
          views: ['cost', 'co2'],
          title: { cost: '', co2: '' },
          tableVariations: { cost: ['1', '2'], co2: ['1', '2'] },
        },
      })
      const { then } = setup()
      await flushPromises()
      await wait(0)
      expect(then.getVariationSwitcher().exists()).toBe(true)
    })
    it('- not visible when configuration has single variation', async () => {
      withMockApi({
        cardConfig: {
          views: ['cost', 'co2'],
          title: { cost: '', co2: '' },
          tableVariations: { cost: ['1'], co2: ['1'] },
        },
      })
      const { then } = setup()
      await flushPromises()
      expect(then.getVariationSwitcher().exists()).toBe(false)
    })
    it('- clicked variations changes the (table) variation', async () => {
      withMockApi({
        cardConfig: {
          views: ['cost', 'co2'],
          title: { cost: '', co2: '' },
          tableVariations: { cost: ['default', 'extended'], co2: ['1'] },
        },
      })
      const { when, then } = setup()
      await wait(500)
      expect(then.getTable().text()).toContain('Designationper yearper part')
      expect(then.getTable().text()).not.toContain('EXTENDED')
      await when.clickButton(1)
      await wait(500)
      expect(then.getTable().text()).toContain('EXTENDED')
    })
    it('- clicking the active variation changes nothing', async () => {
      withMockApi({
        cardConfig: {
          views: ['cost', 'co2'],
          title: { cost: '', co2: '' },
          tableVariations: { cost: ['default', 'extended'], co2: ['1'] },
        },
      })
      const { when, then } = setup()
      await wait(500)
      expect(then.getTable().text()).toContain('Designationper yearper part')
      expect(then.getTable().text()).not.toContain('EXTENDED')
      await when.clickButton(0)
      await wait(500)
      // nothing changed
      expect(then.getTable().text()).toContain('Designationper yearper part')
      expect(then.getTable().text()).not.toContain('EXTENDED')
    })
  })
  describe('- Kpi', () => {
    it('- visible when kpi in config', async () => {
      withMockApi({
        cardConfig: {
          views: ['cost', 'co2'],
          title: { cost: 'needed', co2: '' },
          kpi: { cost: 'foo', co2: 'bar' },
        },
      })
      const { then } = setup({
        contextEntity: initManufacturingDbWithManufacturing(
          gManufacturingEntity({
            fields: [gResultField({ name: 'foo' })],
          })
        ),
      })
      // don't know why... but it works only with wait()
      await wait(100)
      expect(then.getKpi().exists()).toBe(true)
    })
    it('- invisible when no kpi in config', async () => {
      withMockApi({
        cardConfig: { views: ['cost'], title: { cost: '', co2: '' } },
      })
      const { then } = setup({
        contextEntity: initManufacturingDbWithManufacturing(
          gManufacturingEntity({
            fields: [gResultField({ name: 'foo' })],
          })
        ),
      })
      // don't know why... but it works only with wait()
      await wait(0)
      expect(then.getKpi().exists()).toBe(false)
    })
  })
  describe('- Fields', () => {
    it('- shows all field referenced in the config', async () => {
      const fields = [
        gResultField({ name: 'oneField' }),
        gResultField({ name: 'secondField' }),
      ]

      withMockApi({
        cardConfig: {
          views: ['cost'],
          title: { cost: '', co2: '' },
          fields: {
            cost: {
              left: [{ fieldNames: ['oneField'] }],
              right: [{ fieldNames: ['secondField'] }],
            },
            co2: { left: [] },
          },
        },
      })
      const { then } = setup({
        mode: 'cost',
        contextEntity: initManufacturingDbWithManufacturing(
          gManufacturingEntity({
            fields,
          })
        ),
      })
      await wait(0)

      expect(
        then
          .getFieldConfig()
          .flatMap((it) =>
            [...it.left, ...it.right!].flatMap((at) =>
              at.fieldNames?.map((f) => f.toString())
            )
          )
      ).toEqual(fields.map(({ name }) => name))
    })
  })
  describe('Specific Entity', () => {
    it('- calls api with specific field value', () => {
      const mock = withMockApi()
      setup(
        {},
        true,
        gManufacturingEntity({
          fields: [
            gResultField({ name: 'costPerPart' }),
            gResultField({ name: 'uiConfigKeys', value: 'my-specific-value' }),
          ],
        })
      )
      expect(mock).toHaveBeenCalledWith('my-specific-value')
    })
  })
})
//#endregion TESTS
