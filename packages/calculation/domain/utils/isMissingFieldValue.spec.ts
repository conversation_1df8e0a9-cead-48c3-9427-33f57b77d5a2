import { manufacturingStore } from '@domain/calculation/manufacturing.store'
import { gBomNodeEntity } from '@tset/shared-utils/tests/generators/bomNodeEntity'
import { gResultField } from '@tset/shared-utils/tests/generators/resultField'
import { isMissingValueField } from './isMissingFieldValue'

function given(args: {
  field: ResultField
  mode: Mode
  loadedNode: BomNodeEntity
}) {
  manufacturingStore.setNode({ node: args.loadedNode })

  return {
    then: {
      isMissingValueField: isMissingValueField(args.field, args.mode),
    },
  }
}

const readOnlyField = gResultField({
  metaInfo: {
    readOnly: true,
  },
  value: null,
})
const fieldWithValue = gResultField({
  metaInfo: {},
  value: 'value',
})

const requiredCo2NullField = gResultField({
  metaInfo: {
    isRequiredFor: ['co2'],
  },
  value: null,
})

const missingFieldForCost = gResultField({
  metaInfo: {
    isRequiredFor: ['cost'],
  },
  value: null,
})
const missingFieldForCo2 = gResultField({
  metaInfo: {
    isRequiredFor: ['co2'],
  },
  value: null,
})

const brokenBomNodeEntity = gBomNodeEntity({
  kpi: {
    costPerPart: {
      newValue: null,
      oldValue: null,
      broken: true,
      percentageDifference: null,
    },
    co2PerPart: {
      newValue: null,
      oldValue: null,
      broken: true,
      percentageDifference: null,
    },
  },
})
const calculatedBomNodeEntity = gBomNodeEntity({
  kpi: {
    costPerPart: {
      newValue: null,
      oldValue: null,
      broken: false,
      percentageDifference: null,
    },
    co2PerPart: {
      newValue: null,
      oldValue: null,
      broken: false,
      percentageDifference: null,
    },
  },
})

describe('returns false', () => {
  it('if field is readonly', () => {
    const { then } = given({
      field: readOnlyField,
      mode: 'cost',
      loadedNode: calculatedBomNodeEntity,
    })
    expect(then.isMissingValueField).toBe(false)
  })

  it('if field value is set', () => {
    const { then } = given({
      field: fieldWithValue,
      mode: 'cost',
      loadedNode: calculatedBomNodeEntity,
    })
    expect(then.isMissingValueField).toBe(false)
  })

  it('if field is not required for current app mode', () => {
    const { then } = given({
      field: requiredCo2NullField,
      mode: 'cost',
      loadedNode: calculatedBomNodeEntity,
    })
    expect(then.isMissingValueField).toBe(false)
  })
  describe('if the calculation is not broken for the current app mode', () => {
    it('in cost mode', () => {
      const { then } = given({
        field: missingFieldForCost,
        mode: 'cost',
        loadedNode: calculatedBomNodeEntity,
      })
      expect(then.isMissingValueField).toBe(false)
    })
    it('in co2 mode', () => {
      const { then } = given({
        field: missingFieldForCo2,
        mode: 'co2',
        loadedNode: calculatedBomNodeEntity,
      })
      expect(then.isMissingValueField).toBe(false)
    })
  })
})

describe('returns true', () => {
  it('if field is empty and required for current mode and not readonly', () => {
    const { then } = given({
      field: missingFieldForCost,
      mode: 'cost',
      loadedNode: brokenBomNodeEntity,
    })
    expect(then.isMissingValueField).toBe(true)
  })
})
