import { getRouter } from '@/router'
import { useCalculationMissingFields } from '@domain/calculation/utils/useCalculationIssues'
import { CalculationType } from '@tset/shared-model/calculation/CalculationType'
import { PAGE } from '@tset/shared-model/navigation/navigation'
import { gBomNodeEntity } from '@tset/shared-utils/tests/generators/bomNodeEntity'
import { gManuCurrentKpi } from '@tset/shared-utils/tests/generators/manuCurrentKpi'
import { gManufacturingEntity } from '@tset/shared-utils/tests/generators/manufacturingEntity'
import { gResultField } from '@tset/shared-utils/tests/generators/resultField'
import { computed } from 'vue'
import { withManufacturingStoreMock } from '../manufacturing.mock'
import { manufacturingStore } from '../manufacturing.store'

describe('return list of missing inputs', () => {
  it('- empty for rough calculations', () => {
    const { missingFields } = given(
      gManufacturingEntity({
        children: [
          gManufacturingEntity({
            type: 'MANUFACTURING_STEP',
            fields: [
              gResultField({
                metaInfo: { isRequiredFor: ['cost'] },
                value: null,
              }),
            ],
          }),
        ],
      }),
      CalculationType.ROUGH
    )
    expect(missingFields.value).toEqual([])
  })

  it('- for entities', () => {
    const { missingFields } = given(
      gManufacturingEntity({
        children: [
          gManufacturingEntity({
            type: 'MANUFACTURING_STEP',
            fields: [
              gResultField({
                name: 'field-1',
                label: 'Field 1',
                value: null,
              }),
              gResultField({
                name: 'field-2',
                label: 'Field 2',
                metaInfo: { isRequiredFor: ['cost'] },
                value: null,
              }),
            ],
          }),

          gManufacturingEntity({
            type: 'MATERIAL',
            fields: [
              gResultField({
                name: 'field-3',
                label: 'Field 3',
                value: null,
              }),
              gResultField({
                name: 'field-4',
                label: 'Field 4',
                metaInfo: { isRequiredFor: ['cost'] },
                value: null,
              }),
            ],
            children: [
              gManufacturingEntity({
                fields: [
                  gResultField({
                    name: 'field-vin',
                    value: null,
                    metaInfo: { isRequiredFor: ['cost'] },
                  }),
                ],
              }),
            ],
          }),
          gManufacturingEntity({
            type: 'MATERIAL',
            fields: [
              gResultField({
                name: 'field-5',
                label: 'Field 5',
                value: null,
              }),
              gResultField({
                name: 'field-6',
                label: 'Field 6',
                value: null,
              }),
            ],
          }),
        ],
      })
    )
    expect(missingFields.value).toEqual([
      expect.objectContaining({
        entity: expect.objectContaining({ type: 'MANUFACTURING_STEP' }),
        fields: expect.arrayContaining([
          expect.objectContaining({ name: 'field-2' }),
        ]),
        type: 'entity',
      }),
      expect.objectContaining({
        entity: expect.objectContaining({ type: 'MATERIAL' }),
        fields: expect.arrayContaining([
          expect.objectContaining({ name: 'field-4' }),
          expect.objectContaining({ name: 'field-vin' }),
        ]),
        type: 'entity',
      }),
    ])
  })
  it('- for children of MD_COSTFACTORS_PARENT', () => {
    const { missingFields } = given(
      gManufacturingEntity({
        children: [
          gManufacturingEntity({
            type: 'MD_COSTFACTORS_PARENT',
            children: [
              gManufacturingEntity({
                fields: [
                  gResultField({
                    name: 'missing 1',
                    value: null,
                    metaInfo: { isRequiredFor: ['cost', 'co2'] },
                  }),
                ],
              }),
              gManufacturingEntity({
                fields: [
                  gResultField({
                    name: 'missing 2',
                    value: null,
                    metaInfo: { isRequiredFor: ['cost', 'co2'] },
                  }),
                ],
              }),
              gManufacturingEntity({
                fields: [
                  gResultField({
                    name: 'missing 3',
                    value: null,
                    metaInfo: { isRequiredFor: ['cost', 'co2'] },
                  }),
                ],
              }),
            ],
          }),
        ],
      })
    )
    expect(missingFields.value).toEqual([
      expect.objectContaining({
        type: 'modal',
        fields: [
          expect.objectContaining({ name: 'missing 1' }),
          expect.objectContaining({ name: 'missing 2' }),
          expect.objectContaining({ name: 'missing 3' }),
        ],
      }),
    ])
  })
  it('- for MD_EXCHANGERATE', () => {
    const { missingFields } = given(
      gManufacturingEntity({
        children: [
          gManufacturingEntity({
            type: 'MD_EXCHANGERATE_PARENT',
            children: [
              gManufacturingEntity({
                type: 'MD_EXCHANGERATE',
                fields: [
                  gResultField({
                    name: 'missing 1',
                    value: null,
                    metaInfo: { isRequiredFor: ['cost', 'co2'] },
                  }),
                ],
              }),
              gManufacturingEntity({
                type: 'MD_EXCHANGERATE',
                fields: [
                  gResultField({
                    name: 'missing 2',
                    value: null,
                    metaInfo: { isRequiredFor: ['cost', 'co2'] },
                  }),
                ],
              }),
              gManufacturingEntity({
                type: 'MD_EXCHANGERATE',
                fields: [
                  gResultField({
                    name: 'not missing',
                    value: 0.123,
                    metaInfo: { isRequiredFor: ['cost', 'co2'] },
                  }),
                ],
              }),
            ],
          }),
        ],
      })
    )
    expect(missingFields.value).toEqual([
      expect.objectContaining({
        type: 'modal',
        fields: [
          expect.objectContaining({ name: 'missing 1' }),
          expect.objectContaining({ name: 'missing 2' }),
        ],
      }),
    ])
  })

  it('- considers invisible fields based on conditions', () => {
    const { missingFields } = given(
      gManufacturingEntity({
        children: [
          gManufacturingEntity({
            type: 'MATERIAL',
            fields: [
              gResultField({
                name: 'field-1',
                metaInfo: { isRequiredFor: ['cost'] },
                value: null,
              }),
              gResultField({
                name: 'field-2',
                metaInfo: { isRequiredFor: ['cost'] },
                value: 'field-val',
              }),
              gResultField({
                name: 'field-3',
                metaInfo: {
                  isRequiredFor: ['cost'],
                  condition: {
                    field: 'field-1',
                    operator: 'eq',
                    value: 'value',
                  },
                },
                value: null,
              }),
              gResultField({
                name: 'field-4',
                metaInfo: {
                  isRequiredFor: ['cost'],
                  condition: {
                    field: 'field-2',
                    operator: 'eq',
                    value: 'field-val',
                  },
                },
                value: null,
              }),
            ],
          }),
        ],
      })
    )

    expect(missingFields.value).toStrictEqual(
      expect.arrayContaining([
        expect.objectContaining({
          fields: expect.arrayContaining([
            expect.objectContaining({ name: 'field-1' }),
            expect.objectContaining({ name: 'field-4' }),
            expect.not.objectContaining({ name: 'field-2' }),
            expect.not.objectContaining({ name: 'field-3' }),
          ]),
        }),
      ])
    )
  })
})

describe('special fields (shapeId & netWeightPerPart)', () => {
  it('- not shown for ManualManufacturing', () => {
    const { missingFields } = given(
      gManufacturingEntity({
        fields: [
          gResultField({
            name: 'technologyModel',
            value: 'ManualManufacturing',
          }),
          gResultField({
            name: 'shapeId',
            value: null,
            metaInfo: { isRequiredFor: ['cost', 'co2'] },
          }),
          gResultField({
            name: 'netWeightPerPart',
            value: null,
            metaInfo: { isRequiredFor: ['cost', 'co2'] },
          }),
        ],
      }),
      CalculationType.MANUAL
    )
    expect(missingFields.value).toEqual([])
  })
  it('- not shown for ManufacturingPrintedCircuitBoardAssembly', () => {
    const { missingFields } = given(
      gManufacturingEntity({
        fields: [
          gResultField({
            name: 'technologyModel',
            value: 'ManufacturingPrintedCircuitBoardAssembly',
          }),
          gResultField({
            name: 'shapeId',
            value: null,
            metaInfo: { isRequiredFor: ['cost', 'co2'] },
          }),
          gResultField({
            name: 'netWeightPerPart',
            value: null,
            metaInfo: { isRequiredFor: ['cost', 'co2'] },
          }),
        ],
      }),
      CalculationType.DETAILED
    )
    expect(missingFields.value).toEqual([])
  })
  it('- not shown for PCB', () => {
    const { missingFields } = given(
      gManufacturingEntity({
        fields: [
          gResultField({
            name: 'technology',
            value: 'PCB',
          }),
          gResultField({
            name: 'shapeId',
            value: null,
            metaInfo: { isRequiredFor: ['cost', 'co2'] },
          }),
          gResultField({
            name: 'netWeightPerPart',
            value: null,
            metaInfo: { isRequiredFor: ['cost', 'co2'] },
          }),
        ],
      }),
      CalculationType.DETAILED
    )
    expect(missingFields.value).toEqual([])
  })
  it('- shown for any other value', () => {
    const { missingFields } = given(
      gManufacturingEntity({
        fields: [
          gResultField({
            name: 'technologyModel',
            value: 'Whatever you wish for',
          }),
          gResultField({
            name: 'shapeId',
            value: null,
            metaInfo: { isRequiredFor: ['cost', 'co2'] },
          }),
          gResultField({
            name: 'netWeightPerPart',
            value: null,
            metaInfo: { isRequiredFor: ['cost', 'co2'] },
          }),
        ],
      }),
      CalculationType.DETAILED
    )
    expect(missingFields.value).toEqual([
      expect.objectContaining({
        fields: [
          expect.objectContaining({ name: 'netWeightPerPart' }),
          expect.objectContaining({ name: 'shapeId' }),
        ],
        type: 'page',
      }),
    ])
  })
})

describe('missing field on page', () => {
  it('- indicates missing fields on part tab', () => {
    const { hasMissingFieldOnPage } = given(
      gManufacturingEntity({
        fields: [
          gResultField({
            name: 'field-4',
            label: 'Field 4',
            metaInfo: { isRequiredFor: ['cost'], dynamicField: true },
            value: null,
          }),
        ],
        children: [gManufacturingEntity({})],
      })
    )
    expect(hasMissingFieldOnPage(PAGE.MANU_PART, router)).toBe(true)
    expect(hasMissingFieldOnPage(PAGE.MANU_MAIN, router)).toBe(false)
  })
  it('- indicates missing fields on material', () => {
    const { hasMissingFieldOnPage } = given(
      gManufacturingEntity({
        children: [
          gManufacturingEntity({
            type: 'MATERIAL',
            fields: [
              gResultField({
                name: 'field-4',
                label: 'Field 4',
                metaInfo: { isRequiredFor: ['cost'] },
                value: null,
              }),
            ],
          }),
        ],
      })
    )
    expect(hasMissingFieldOnPage(PAGE.MANU_MATERIAL, router)).toBe(true)
    expect(hasMissingFieldOnPage(PAGE.MANU_STEP, router)).toBe(false)
    expect(hasMissingFieldOnPage(PAGE.MANU_MAIN, router)).toBe(false)
  })
  it('- indicates missing fields on manufacturing step', () => {
    const { hasMissingFieldOnPage } = given(
      gManufacturingEntity({
        children: [
          gManufacturingEntity({
            type: 'MANUFACTURING_STEP',
            fields: [
              gResultField({
                name: 'field-4',
                label: 'Field 4',
                metaInfo: { isRequiredFor: ['cost'] },
                value: null,
              }),
            ],
          }),
        ],
      })
    )
    expect(hasMissingFieldOnPage(PAGE.MANU_STEP, router)).toBe(true)
    expect(hasMissingFieldOnPage(PAGE.MANU_STEPS, router)).toBe(true)
    expect(hasMissingFieldOnPage(PAGE.MANU_MATERIAL, router)).toBe(false)
  })
})

const router = getRouter()
const { setMockLoadedNode } = withManufacturingStoreMock()

function given(
  manufacturing: ManufacturingDTO,
  calculationType?: CalculationType
) {
  setMockLoadedNode(
    gBomNodeEntity({
      calculationType: gResultField({
        value: calculationType ?? CalculationType.MANUAL,
      }),
      id: 'loaded-node-id',
      kpi: {
        costPerPart: gManuCurrentKpi({
          broken: true,
        }),
        co2PerPart: gManuCurrentKpi({
          broken: true,
        }),
      },
      manufacturing,
    })
  )

  const missing = useCalculationMissingFields(
    computed(() => ({
      loadedManufacturing: manufacturingStore.loadedManufacturing,
      technology: manufacturingStore.technology,
      calculationType: gResultField({
        value: calculationType ?? CalculationType.MANUAL,
      }),
    }))
  )
  return missing
}
