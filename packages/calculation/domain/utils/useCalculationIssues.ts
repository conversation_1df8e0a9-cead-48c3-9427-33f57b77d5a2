import type Manufacturing from '@domain/calculation/Manufacturing'
import { $nutTextField, $t } from '@shared/translation/nuTranslation'
import { CalculationType } from '@tset/shared-model/calculation/CalculationType'
import { masterdataEntityTypesInCost } from '@tset/shared-model/masterdata/NuMasterdata'
import { PAGE } from '@tset/shared-model/navigation/navigation'
import { openExchangeRatesModal } from '@tset/shared-ui/manufacturing/exchangeRatesModalHelper'
import { openLocationCostFactorModal } from '@tset/shared-ui/manufacturing/locationCostFactorsModalHelper'
import { isFieldVisible } from '@tset/shared-utils/helpers/field/isFieldVisible'
import { getDynamicFields } from '@tset/shared-utils/helpers/manufacturing'
import { gResultField } from '@tset/shared-utils/tests/generators/resultField'
import { merge } from 'lodash'
import type { ComputedRef, Ref } from 'vue'
import { computed, reactive } from 'vue'
import type { Router } from 'vue-router'
import { isMissingValueField } from './isMissingFieldValue'

export function useCalculationIssues({
  appMode,
  bomNode,
}: {
  appMode: Ref<Mode>
  bomNode: ComputedRef<Nullable<BomNodeEntity>>
}) {
  const calcIssues = computed(() =>
    getCalculationIssues(bomNode.value, appMode.value)
  )

  return reactive({
    isBroken: computed(() => calcIssues.value.isBroken),
    isCostBroken: computed(() => calcIssues.value.isCostBroken),
    isCo2Broken: computed(() => calcIssues.value.isCo2Broken),
  })
}

function getCalculationIssues(
  calc: Pick<BomNodeEntity, 'kpi'> | null,
  mode: Mode
) {
  const isCostBroken = calc?.kpi?.costPerPart?.broken
  const isCo2Broken = calc?.kpi?.co2PerPart?.broken
  const isBroken = mode === 'cost' ? isCostBroken : isCo2Broken

  return {
    isBroken,
    isCostBroken,
    isCo2Broken,
  }
}

export type MissingFieldsEntry = {
  id: string
  title: string
  fields: ResultField[]
} & (
  | {
      type: 'entity'
      entity: Manufacturing
    }
  | {
      type: 'page'
      page: PAGE
      icon: string
    }
  | {
      type: 'modal'
      fieldName: string
      page: PAGE
      modalOpener: () => void
    }
)

export function useCalculationMissingFields(
  props: ComputedRef<{
    loadedManufacturing: Manufacturing | null
    technology: string | undefined
    calculationType: ResultField
  }>
) {
  const missingFields = computed(() => {
    const manufacturing = props.value.loadedManufacturing
    const hideSpecialFields = [
      'ManualManufacturing',
      'ManufacturingPrintedCircuitBoardAssembly',
      'PCB',
    ].includes(props.value.technology ?? '')

    const isRoughCalculation =
      props.value?.calculationType.value === CalculationType.ROUGH

    if (!manufacturing || isRoughCalculation) {
      return []
    }

    const maybeModularizedEntities = manufacturing.getEntities({
      type: [
        'MANUFACTURING_STEP',
        'MATERIAL',
        'CYCLETIME_STEP',
        'CYCLETIME_STEP_GROUP',
        ...masterdataEntityTypesInCost,
      ],
    })
    const missingEntityEntries = maybeModularizedEntities.flatMap(
      (entity) => getMissingFieldsEntry(entity) ?? []
    )
    const result: MissingFieldsEntry[] = missingEntityEntries

    const partTabEntry = getPartTabEntry(manufacturing, hideSpecialFields)
    if (partTabEntry.fields.length) {
      result.unshift(partTabEntry)
    }

    const missingCostFactorsEntry = getMissingCostFactorsEntry(manufacturing)
    if (missingCostFactorsEntry.fields.length) {
      result.unshift(missingCostFactorsEntry)
    }

    const missingExchangeRatesEntry =
      getMissingExchangeRatesEntry(manufacturing)
    if (missingExchangeRatesEntry.fields.length) {
      result.unshift(missingExchangeRatesEntry)
    }

    return result
  })

  const numberOfMissingFields = computed(() =>
    missingFields.value.reduce((a, b) => a + b.fields.length, 0)
  )

  const hasMissingFields = computed(() => numberOfMissingFields.value > 0)

  const hasMissingFieldOnPage = (page: PAGE, router: Router) => {
    return missingFields.value.some((entry) => {
      let targetPage: PAGE | undefined
      if (entry.type === 'page' || entry.type === 'modal') {
        targetPage = entry.page
      } else if (entry.type === 'entity') {
        targetPage = getEntryTargetPage(entry.entity)
      }
      return (
        targetPage &&
        (page === targetPage ||
          isOnAMatchingPageRoute(page, targetPage, router))
      )
    })
  }

  return {
    missingFields,
    numberOfMissingFields,
    hasMissingFields,
    hasMissingFieldOnPage,
  }
}

function isOnAMatchingPageRoute(
  page: PAGE,
  possibleChildPage: PAGE,
  router: Router
): boolean {
  if (page === PAGE.MANU_MAIN) {
    return false // ignore the summary page
  }
  if (page === PAGE.MANU_STEPS) {
    page = PAGE.MANU_STEP
  }
  const parent = router.resolve(page)
  const child = router.resolve(possibleChildPage)
  return child.href.startsWith(parent.href)
}

export function getEntryTargetPage(entity: Manufacturing) {
  switch (entity.type) {
    case 'MANUFACTURING_STEP':
      return PAGE.MANU_STEP
    case 'MATERIAL':
      return PAGE.MANU_MATERIALDETAIL
    case 'CONSUMABLE':
      return PAGE.MANU_CONSUMABLEDETAIL
    case 'MANUFACTURING':
      return PAGE.MANU_PART
    case 'CYCLETIME_STEP':
      return PAGE.MANU_CYCLE
    case 'CYCLETIME_STEP_GROUP':
      return PAGE.MANU_CYCLEGROUP
  }
}

function getPartTabEntry(
  manufacturing: Manufacturing,
  hideSpecialFields: boolean
): MissingFieldsEntry {
  let partTabAdditionalFields: ResultField[] = []
  if (!hideSpecialFields) {
    partTabAdditionalFields =
      (manufacturing?.getFields(['netWeightPerPart', 'shapeId']) ?? []).map(
        (f) => merge(f, { metaInfo: { readOnly: false } })
      ) ?? []
  }

  const partTabDynamicFields = getDynamicFields(manufacturing)

  const inPartTab: MissingFieldsEntry = {
    id: 'part',
    type: 'page',
    page: PAGE.MANU_PART,
    icon: 'IconTabPart',
    title: $t('statics.part'),
    fields: getMissingFields([
      ...partTabAdditionalFields,
      ...partTabDynamicFields,
    ]),
  }
  return inPartTab
}

function getMissingCostFactorsEntry(
  manufacturing: Manufacturing
): MissingFieldsEntry {
  return {
    id: 'costFactors',
    type: 'modal',
    page: PAGE.MANU_PARAMETERS,
    fieldName: 'location',
    modalOpener: () => openLocationCostFactorModal(manufacturing),
    title: $t('statics.locationCostFactors'),
    fields: getMissingFields(
      manufacturing
        .getEntities({ type: 'MD_COSTFACTORS_PARENT' })
        ?.flatMap((e) => e.getChildren())
        ?.flatMap((entity) => entity.getFields())
    ),
  }
}

function getMissingExchangeRatesEntry(
  manufacturing: Manufacturing
): MissingFieldsEntry {
  return {
    id: 'exchangeRates',
    type: 'modal',
    page: PAGE.MANU_PARAMETERS,
    fieldName: 'baseCurrency',
    modalOpener: () =>
      openExchangeRatesModal(
        manufacturing.getEntities({
          type: 'MD_EXCHANGERATE_PARENT',
        })[0]
      ),
    title: $t('statics.exchangeRates'),
    fields: getMissingFields(
      manufacturing
        .getEntities({ type: 'MD_EXCHANGERATE' })
        .flatMap((entity) => entity.getFields())
    ),
  }
}

function getMissingFieldsEntry(
  entity: Manufacturing
): MissingFieldsEntry | undefined {
  const fields =
    // we do this in order to have price component missing inputs within the materials card
    // TODO: establish a strategy based way to include child fields or not. it shouldn't depend on an entity type
    entity.type === 'MATERIAL'
      ? entity.fields.concat(
          entity.getChildren().flatMap((child) => child.getFields())
        )
      : entity.fields
  const missingFields = getMissingFields(fields)
  if (missingFields.length) {
    return {
      id: entity.id,
      type: 'entity',
      entity,
      title: $nutTextField(
        entity.getField<string>('displayDesignation') ?? gResultField()
      ),
      fields: missingFields,
    }
  }
}

function getMissingFields(fields: ResultField[]) {
  return fields.filter(
    (field) => isMissingValueField(field) && isFieldVisible(field, fields)
  )
}

export function checkEntityForMissingField(
  fieldNames: string[],
  entity: Manufacturing | Pick<ManufacturingDTO, 'fields' | 'id'>
) {
  return fieldNames
    .flatMap(
      (field) =>
        ('getField' in entity
          ? entity.getField(field)
          : entity.fields.find((f) => f.name === field)) ?? []
    )
    .some(
      (field, _, arr) =>
        field && isMissingValueField(field) && isFieldVisible(field, arr)
    )
}
