/* eslint-disable max-lines */
import { useBomExplorerBus } from '@/buses/useBomExplorerBus'
import { getPiniaInstance } from '@/piniaInstance'
import { getCurrentRoute, getRouter } from '@/router'
import { navigationStore } from '@/store'
import { costStore } from '@/store/cost.store'
import { useAppMode } from '@/store/useAppMode'
import { DirtyChildLoadingFlag } from '@tset/shared-model/calculation/DirtyLoading'
import { MergeSource } from '@tset/shared-model/calculation/Merge'
import { UploadType } from '@tset/shared-model/ui/FileUpload'
import { isProcessing } from '@tset/shared-utils/api/ConcurrencyHandler'
import {
  createObject,
  type CreateObjectDto,
} from '@tset/shared-utils/api/knowledge'
import { postUpload } from '@tset/shared-utils/api/staticResource'
import { useDisplayCurrency } from '@tset/shared-utils/api/useDisplayCurrency'
import { isAxiosError } from '@tset/shared-utils/helpers/api'
import {
  newCurrencyInfo,
  sumMoneyFields,
} from '@tset/shared-utils/helpers/field'
import {
  createFormDataFiles,
  isEntityProtected,
  isMimeTypeImage,
} from '@tset/shared-utils/helpers/general'
import { getValue } from '@tset/shared-utils/helpers/getValue'
import {
  allEntitiesOfType,
  emptyField,
  getField,
  isNotDirtyChildLoadingException,
  newField,
} from '@tset/shared-utils/helpers/manufacturing'
import {
  discardChanges as _discardChanges,
  publishBomNode,
} from '@tset/shared-utils/helpers/navGuard/navGuard'
import { createPromiseObserver } from '@tset/shared-utils/helpers/promise-observer'
import { isAbortError } from '@tset/shared-utils/helpers/typeGuard/isAbortError'
import { isDirtyChildError } from '@tset/shared-utils/helpers/typeGuard/isDirtyChildError'
import { shouldNeverHappen } from '@tset/shared-utils/helpers/typescript'
import { defineStore } from 'pinia'
import { computed, ref, type Ref } from 'vue'
import type { RouteLocation } from 'vue-router'
import type Manufacturing from './Manufacturing'
import type { IField } from './ManufacturingDb'
import ManufacturingDb from './ManufacturingDb'
import {
  duplicateBranch as _duplicateBranch,
  mergeBranch as _mergeBranch,
  copyMain,
} from './api/branch'
import { deleteEntity, reorderCycleTimeSteps, reorderSteps } from './api/entity'
import {
  deleteRootManufacturing as _deleteRootManufacturing,
  // TODO rename underscore imports (https://tsetplatform.atlassian.net/browse/COST-73262)
  redoChanges as _redoChanges,
  renameProjectBranch as _renameProjectBranch,
  undoChanges as _undoChanges,
  updateAndUseAsMain as _updateAndUseAsMain,
  fetchBranchesApi,
  fetchManufacturing,
  fetchMasterDataUpdates,
  manuallyRecalculate,
  restoreField,
  savePublicVariant,
  updateAndPublish,
  updateField,
} from './api/manufacturing'
import {
  addAutomatedLine as _addAutomatedLine,
  revertAutomatedLine as _revertAutomatedLine,
} from './api/manufacturingStep'
import { setBroken } from './utils/isMissingFieldValue'

const bomExplorerBus = useBomExplorerBus()
const { displayCurrency, setLoading: setLoadingCurrency } = useDisplayCurrency()
const { appMode } = useAppMode()

type BranchInfoChangedPayload = {
  branch: Branch
  node?: BomNodeEntity
  updateUrl: boolean
  status?: string
  name?: string
}

// TODO: T0085 handle catch(error: any) typing better. We need a type guard in the api layer which for now wraps the axios.isAxiosError type guard to react correctly
// Or we can extract API layer error handling into the API layer functions. This is probably the best option to abstract the API layer at best

export const useManufacturingStore = defineStore('manufacturing', () => {
  /** COST-44802: BomNode is the value that stores the actual data for the current bomNode on the application
   * It's only updated inside this store. To obey the encapsulation principle, we use the `loadedNode` getter
   * to expose it outside of the store. That's why this property is private
   */
  const loadedNode: Ref<Nullable<BomNodeEntity>> = ref(null) //! private
  const soonToBeLoadedBomNodeBranch: Ref<Nullable<BomNodeVariant>> = ref(null)
  const tableSetup: Ref<TableSetup> = ref({
    columns: ['source', 'cost', 'fraction'],
    tableData: [],
    co2tableData: [],
  })
  const branches: Ref<Branch[]> = ref([])
  const fetchingId: Ref<Nullable<BomNodeId>> = ref(null)
  //FIXME T0021 use null instead of undefined
  const activeStepId: Ref<string | undefined> = ref('') //! private
  const manufacturingIsLoading = ref(false)
  const manufacturingIsLoadingChanges = ref(false)
  const cbdTableIsLoading = ref(false)
  const ctsTableIsReordering = ref(false)
  const clickedNodePath: Ref<Nullable<string[]>> = ref([])
  const exchangeRates: Ref<CurrencyInfo> = ref({})
  const loadManufacturingObserver = createPromiseObserver() //! private
  const loadedManufacturingId: Ref<Nullable<Manufacturing['id']>> = ref(null) //! private

  /**
   * In some cases (e.g. the cost-breakdown-table) the backend does not provide units/denominatorUnits for fields.
   * For these cases, the unit/denominatorUnit is taken from another field on the loadedManufacturing depending on the mode
   */
  const manufacturingUnitField = computed(() => {
    if (!loadedManufacturing.value) {
      return
    }
    return loadedManufacturing.value.getField(
      appMode.isCost ? 'costPerPart' : 'cO2PerPart'
    )
  })

  const calculationCurrency = computed<Currency>(() => {
    return (
      (loadedNode.value?.manufacturing.fields.find(
        (field) => field.name === 'baseCurrency'
      )?.value as Currency) ?? 'EUR'
    )
  })

  const calculationType = computed(
    () => loadedNode.value?.calculationType ?? emptyField()
  )

  const isProtectedCalculation = computed(() =>
    isEntityProtected(loadedNode.value)
  )

  const loadedManufacturing = computed(() => {
    if (!loadedManufacturingId.value) {
      return null
    }
    return ManufacturingDb.getEntity(loadedManufacturingId.value)
  })

  const sourceBranchId = computed(
    () => loadedNode.value?.branch.sourceBranch ?? null
  )

  const activeStep = computed(() => {
    if (!loadedManufacturing.value || !activeStepId.value) {
      return
    }
    return loadedManufacturing.value.getEntity(activeStepId.value)
  })

  const stepRootEntity = computed(() => {
    if (!loadedManufacturing.value) {
      return
    }
    return (
      loadedManufacturing.value.children.find(
        (child: ManufacturingDTO) => child.type === 'PROCESSED_MATERIAL'
      ) || loadedManufacturing.value
    )
  })

  const conflictStatus = computed(() => conflicts.value)

  const costPerPart = computed(
    () =>
      loadedManufacturing?.value?.fields.find(
        (field) => field.name === 'costPerPart'
      ) ?? emptyField()
  )

  const loadedNodeHasUnsavedState = computed(() =>
    Boolean(loadedNode.value && !loadedNode.value.branch.global)
  )

  const technology = computed(
    () =>
      (
        loadedManufacturing?.value?.getField<string>('technologyModel') ??
        loadedManufacturing?.value?.getField<string>('technology')
      )?.value
  )

  const isCo2Broken = computed(() => loadedNode.value?.kpi?.co2PerPart.broken)

  const isCostBroken = computed(() => loadedNode.value?.kpi?.costPerPart.broken)

  function setManufacturingLoading(payload: boolean) {
    manufacturingIsLoading.value = payload
  }

  function setManufacturingLoadingChanges(payload: boolean) {
    //! private
    manufacturingIsLoadingChanges.value = payload
  }

  function setCbdTableLoading(payload: boolean) {
    //! private
    cbdTableIsLoading.value = payload
  }

  function setFetchingId(payload: BomNodeId) {
    //! private besides tests
    fetchingId.value = payload
  }

  function resetFetchingId() {
    fetchingId.value = null
  }

  function setStepId(id: string | undefined) {
    activeStepId.value = id
  }

  function setco2TableSetup(setup: TableData[]) {
    //! private
    tableSetup.value.co2tableData = setup
  }

  function setClickedNodePath(path: Nullable<string[]>) {
    // only used in NuBomExpNode, seems like could be replaced by route there!!
    clickedNodePath.value = path
  }

  function setSoonToBeLoadedBomNodeBranch(payload: BomNodeVariant) {
    //! private besides tests
    const { branchId, bomNodeId, variantId } = payload
    soonToBeLoadedBomNodeBranch.value = {
      bomNodeId,
      branchId: branchId ?? variantId,
    }
  }

  function clearSoonToBeLoadedBomNodeBranch() {
    //! private besides tests
    soonToBeLoadedBomNodeBranch.value = null
  }

  const isMasterWorkingCopy = computed(() => {
    //non global branch created from master = masterWorking Copy
    if (!loadedNode.value?.branch) {
      return false
    }
    const { global, fromMaster } = loadedNode.value.branch
    return !global && fromMaster
  })

  const isMaster = computed(() => {
    //non global branch created from master = masterWorking Copy
    if (!loadedNode.value?.branch) {
      return false
    }
    const { global, master } = loadedNode.value.branch
    return global && !master
  })

  const conflicts = computed<ConflictsSources>(() => {
    if (!loadedNode.value?.openMergesAvailable) {
      return {
        masterChanged: false,
        variantChanged: false,
        masterDataChanged: false,
        masterDataChildrenChanged: false,
      }
    }

    const findTempMerge = (sourceType: MergeSource, merges?: string[]) =>
      merges?.some((merge) => merge === sourceType) || false

    const { openMergesAvailable } = loadedNode.value
    const masterChanged = findTempMerge(MergeSource.MASTER, openMergesAvailable)
    const masterDataChanged = findTempMerge(
      MergeSource.MASTERDATA,
      openMergesAvailable
    )
    const masterDataChildrenChanged = findTempMerge(
      MergeSource.MASTERDATA_CHILDREN,
      openMergesAvailable
    )

    const variantChanged =
      !isMasterWorkingCopy.value && !isMaster.value
        ? findTempMerge(MergeSource.BRANCH, openMergesAvailable)
        : false

    return {
      masterChanged,
      variantChanged,
      masterDataChanged,
      masterDataChildrenChanged,
    }
  })

  async function addAutomatedLine({ stepIds }: { stepIds: string[] }) {
    if (!loadedNode.value || !costStore.loadedProject) {
      return
    }

    try {
      setManufacturingLoading(true)
      const resp = await _addAutomatedLine({
        bomNodeId: loadedNode.value.id,
        branchId: loadedNode.value.branch.id,
        dirtyChildLoading: true,
        projectId: costStore.loadedProject?._id,
        stepIds,
      })
      await setNode({ node: resp })
    } finally {
      setManufacturingLoading(false)
    }
  }

  async function revertAutomatedLine({ groupId }: { groupId: string }) {
    if (!loadedNode.value || !costStore.loadedProject) {
      return
    }

    try {
      setManufacturingLoading(true)
      const resp = await _revertAutomatedLine({
        bomNodeId: loadedNode.value.id,
        branchId: loadedNode.value.branch.id,
        dirtyChildLoading: true,
        groupId,
        projectId: costStore.loadedProject!._id,
      })
      await setNode({ node: resp })
    } finally {
      setManufacturingLoading(false)
    }
  }

  function setNode({
    node,
    update = true,
    action = 'update',
    isRoot = false,
    updateAncestors = false,
  }: {
    node?: BomNodeEntity
    update?: boolean
    action?: 'create' | 'update'
    isRoot?: boolean
    updateAncestors?: boolean
  } = {}) {
    if (!node) {
      resetLoadedNode()
      return
    }
    // Note: we will reject setting a node which is not corresponding to the last targetId set when load manufacturing is dispatched
    if (fetchingId.value && fetchingId.value !== node.id) {
      return
    }
    setNodeInt(node)
    getExchangeRates()
    if (update) {
      switch (action) {
        case 'update':
          // eslint-disable-next-line @typescript-eslint/no-extra-semi
          bomExplorerBus.nodeUpdated.emit({
            id: node.id,
            branchId: node.branch.id,
            updateAncestors,
          })
          dispatchBranchInfoChanged({
            // TODO check against other stores (https://tsetplatform.atlassian.net/browse/COST-73262)
            branch: node.branch,
            updateUrl: true,
          })
          break
        case 'create':
          // Note: here we do not want to navigate as it is taken care of where this action is dispatched
          bomExplorerBus.nodeCreated.emit({
            id: node.id,
            branchId: node.branch.id,
            isRoot,
          })
          break

        default:
          throw shouldNeverHappen(
            'manufacturing.setNode: action is wrong',
            action
          )
      }

      // COST-39903: the unsaved branch should be added, but there should not be duplicated branches
      if (
        branches.value &&
        !branches.value?.find((branch) => branch.id === node.branch.id)
      ) {
        setBranches([...branches.value, node.branch])
      }
    }
    if (node.cbd) {
      getCbdTable(node.cbd)
    }
  }

  function resetLoadedNode() {
    setNodeInt()
  }

  function setNodeInt(node?: BomNodeEntity) {
    loadedNode.value = node ?? null
    setBroken({
      cost: node?.kpi?.costPerPart?.broken ?? true,
      co2: node?.kpi?.co2PerPart?.broken ?? true,
    })
    if (node?.manufacturing) {
      loadedManufacturingId.value = node.manufacturing.id
      ManufacturingDb.update(node.manufacturing)
    }
  }

  async function deleteManufacturing(req: {
    branch: Branch
    bomNodeId: string
    entityId: string
  }): Promise<BomNodeEntity> {
    const { bomNodeId, branch, entityId } = req
    const { data } = await deleteEntity(bomNodeId, branch, entityId)
    bomExplorerBus.nodeUpdated.emit({
      id: data.id,
      branchId: data.branch.id,
    })
    setNode()
    return data
  }

  async function deleteRootManufacturing(req: {
    branchId: string
    bomNodeId: string
  }) {
    try {
      await _deleteRootManufacturing(req)
      bomExplorerBus.nodeDeleted.emit({
        id: req.bomNodeId,
        isRoot: true,
      })
    } finally {
      setNode()
    }
  }

  async function subscribeToLoadManufacturing() {
    return loadManufacturingObserver.subscribe()
  }

  async function loadManufacturing(req: LoadManufacturingReq) {
    if (!req?.bomNodeId) {
      return
    }

    if (
      !(
        req.force ||
        req.bomNodeId !== fetchingId.value || // We accept the requested bomNodeId if it is not already fetching
        !loadedNode.value ||
        req.bomNodeId !== loadedNode.value.id ||
        req.branch != loadedNode.value.branch.id
      )
    ) {
      return
    }

    loadManufacturingObserver.start()
    if (req.branch == 'master') {
      req.branch = undefined
    }
    try {
      setSoonToBeLoadedBomNodeBranch({
        bomNodeId: req.bomNodeId,
        branchId: req.branch ?? undefined,
      })
      // Note: we set the fetching id to sort out racing conditions in order
      // to set the node which corresponds to the last fetchingId set
      setFetchingId(req.bomNodeId)
      setManufacturingLoading(true)

      const params = {
        branch: req.branch,
      }

      const id = req.bomNodeId

      const [data, localBranches] = await Promise.all([
        fetchManufacturing(id, params),
        loadBranches({ force: true, id }),
      ])

      setNode({ node: data, update: req.update ?? false })
      // COST-39903: the unsaved branch should be added, but there should not be duplicated branches
      // TODO: the interfaces should be unified: (https://tsetplatform.atlassian.net/browse/COST-19817)
      if (
        localBranches &&
        !localBranches?.find((branch) => branch.id === data.branch.id)
      ) {
        setBranches([...localBranches, data.branch])
      } else {
        setBranches(localBranches)
      }
    } catch (error: unknown) {
      if (!isAxiosError(error)) {
        // restore component state
      }
      loadManufacturingObserver.flush('reject')
      throw error
    } finally {
      setManufacturingLoading(false)
      clearSoonToBeLoadedBomNodeBranch()
      setLoadingCurrency(false)
    }

    loadManufacturingObserver.flush('resolve')
    navigationStore.setChangingBetweenCalculations(false)
  }

  async function reloadManufacturing() {
    if (!loadedNode.value) {
      return
    }
    const { branch, id: bomNodeId } = loadedNode.value
    await loadManufacturing({
      bomNodeId,
      branch: branch.id,
      force: true,
    })
  }

  async function recalculateManufacturing() {
    setManufacturingLoading(true)
    try {
      if (loadedNode.value) {
        const node = await manuallyRecalculate(
          loadedNode.value.id,
          loadedNode.value.branch.id
        )
        await setNode({ node })
      }
    } finally {
      setManufacturingLoading(false)
    }
  }

  // TODO reintegrate into loadManufacturing (https://tsetplatform.atlassian.net/browse/COST-73262)
  async function loadManufacturingWithOpenMerges() {
    if (!loadedNode.value) {
      return
    }
    setManufacturingLoadingChanges(true)
    try {
      const { branch, id: bomNodeId } = loadedNode.value

      const params = {
        branch: branch.id,
        showOpenMerges: true,
      }
      const data = await fetchManufacturing(bomNodeId, params)
      await setNode({ node: data })
    } catch (error: unknown) {
      if (!isAxiosError(error)) {
        // restore component state
      }
      throw error
    } finally {
      setManufacturingLoadingChanges(false)
    }
  }

  const pendingChanges = computed(() => {
    if (!loadedNode.value) {
      return false
    }
    if (isProcessing(loadedNode.value.id)) {
      return true
    }
    return loadedNode.value.branch && !loadedNode.value.branch.global // TODO reuse isMaster? (https://tsetplatform.atlassian.net/browse/COST-73262)
  })

  function updateManufacturingCancel(body: ManufacturingUpdateBody) {
    ManufacturingDb.removeOptimisticField(body.entityId, body.name)
  }

  async function updateManufacturingOverwrite(body: ManufacturingUpdateBody) {
    const dto: ManufacturingUpdateBody & IField = {
      ...body,
      currency: displayCurrency.value,
    }
    let canceled = false
    try {
      if (!loadedNode.value) {
        throw new Error(`loadedNode is not defined`)
      }
      const { id, branch } = loadedNode.value

      ManufacturingDb.addOptimisticFieldOverwrite(dto)
      ManufacturingDb.updateQueue.increase(body.entityId, body.name)

      const dirtyChildLoading = isNotDirtyChildLoadingException(body.name)
      const { data } = await updateField(id, branch, dto, dirtyChildLoading)
      setNode({ node: data })
      ManufacturingDb.updateQueue.decrease(body.entityId, body.name)
    } catch (error: unknown) {
      if (isAbortError(error)) {
        canceled = true
      }
      ManufacturingDb.updateQueue.decrease(body.entityId, body.name)
    } finally {
      const currentQueueLength: number = ManufacturingDb.updateQueue.length(
        body.entityId,
        body.name
      )

      if (!canceled && currentQueueLength <= 0) {
        ManufacturingDb.removeOptimisticField(body.entityId, body.name)
      }
    }
  }

  async function updateManufacturingUnlock(body: ManufacturingUpdateBody) {
    let canceled = false
    try {
      ManufacturingDb.addOptimisticFieldRestore({
        ...body,
        currency: displayCurrency.value,
      })
      ManufacturingDb.updateQueue.increase(body.entityId, body.name)

      if (!loadedNode.value) {
        throw new Error(`loadedNode was undefined`)
      }

      const { id, branch } = loadedNode.value
      const dirtyChildLoading = isNotDirtyChildLoadingException(body.name)
      const { data } = await restoreField(id, branch, body, dirtyChildLoading)
      ManufacturingDb.updateQueue.decrease(body.entityId, body.name)
      setNode({ node: data })
    } catch (error: unknown) {
      if (isAbortError(error)) {
        canceled = true
      }
      ManufacturingDb.updateQueue.decrease(body.entityId, body.name)
    } finally {
      const currentQueueLength: number = ManufacturingDb.updateQueue.length(
        body.entityId,
        body.name
      )

      if (!canceled && currentQueueLength <= 0) {
        ManufacturingDb.removeOptimisticField(body.entityId, body.name)
      }
      setManufacturingLoading(false)
    }
  }

  async function getUpdatesFromMasterData(recursive: boolean) {
    const { id, branch } = loadedNode.value!
    const { data } = await fetchMasterDataUpdates(id, branch, recursive)
    setNode({ node: data })
    getExchangeRates()
  }

  function getCbdTable(data: CostBreakDownDTO) {
    //! private
    if (!loadedNode.value) {
      return
    }

    setCbdTableLoading(true)

    const tranformTableData = function (
      data: TableData[],
      totalObject: TableData
    ) {
      let value: number = 0
      const tableData: Array<TableData> = data.map((rowObject: TableData) => {
        value += getValue<number>(rowObject.cost, displayCurrency)
        if (rowObject.cost.currencyInfo) {
          totalObject.cost = sumMoneyFields(
            totalObject.cost as ResultField<number>,
            rowObject.cost as ResultField<number>
          )
        }
        totalObject.fraction += rowObject.fraction
        return {
          id: rowObject.id,
          source: rowObject.source,
          cost: rowObject.cost,
          fraction: rowObject.fraction,
          nestedTable: rowObject.nestedTable!,
          unit: rowObject.unit!,
        }
      })
      totalObject = {
        ...totalObject,
        cost: { ...totalObject.cost, value },
      }
      return [...tableData, totalObject]
    }

    const tableData = tranformTableData(data.cost, {
      id: '0',
      source: 'Total',
      cost: newField('cost', 0, 'Money', {}, '', 'I', newCurrencyInfo()),
      fraction: 0,
      unit: 'Money',
    })
    setTabledata(tableData)

    if (data.co2) {
      const co2tableData = tranformTableData(data.co2, {
        id: '0',
        source: 'Total',
        cost: newField('cost', 0, 'CO2', {
          defaultUnit: { unit: 'KILOGRAMM', isFixed: false },
        }),
        fraction: 0,
        unit: 'KILOGRAMM',
      })
      setco2TableSetup(co2tableData)
    } else {
      setco2TableSetup([])
    }
    setCbdTableLoading(false)
  }

  function getManufacturingField(name: string) {
    return loadedManufacturing.value?.getField(name)
  }

  async function saveChanges(
    dirtyChildLoadingFlag: DirtyChildLoadingFlag = DirtyChildLoadingFlag.FAIL_ON_DIRTY
  ): Promise<void> {
    await publishBomNode.execute(dirtyChildLoadingFlag)
  }

  async function updateAndSaveChanges(): Promise<void> {
    await updateAndPublishBomNode()
  }

  async function updateAndPublishBomNode(): Promise<void> {
    const { id, branch } = loadedNode.value!
    const { data } = await updateAndPublish(id, branch)

    setNode({ node: data })
    dispatchBranchInfoChanged({ branch: data.branch, updateUrl: true })
    await loadBranches({ force: true })
  }

  async function updateAndUseAsMain(): Promise<void> {
    const { id, branch } = loadedNode.value!
    const { data } = await _updateAndUseAsMain(id, branch)

    setNode({ node: data, updateAncestors: true })
    await loadBranches({ force: true })
  }

  async function mergeBranch(mergeSource: MergeSource) {
    const { id, branch } = loadedNode.value!

    try {
      const { data } = await _mergeBranch(id, branch, mergeSource)
      setNode({ node: data })
    } catch (error: unknown) {
      if (!isAxiosError(error)) {
        // restore component state
      }
      throw error
    }
  }

  async function saveBranch({
    name,
    renameDescendants,
    dirtyChildLoadingFlag = DirtyChildLoadingFlag.FAIL_ON_DIRTY,
  }: {
    name: string
    renameDescendants: boolean
    dirtyChildLoadingFlag?: DirtyChildLoadingFlag
  }): Promise<void> {
    if (!loadedNode.value) {
      return
    }
    setManufacturingLoading(true)
    const { id: bomNodeId, branch } = loadedNode.value

    try {
      const { data } = await savePublicVariant(
        { bomNodeId, branch, name, renameDescendants },
        dirtyChildLoadingFlag
      )
      bomExplorerBus.nodeUpdated.emit({ id: bomNodeId, branchId: data.id })

      // TODO: check if it's still needed (https://tsetplatform.atlassian.net/browse/COST-73262)
      // TODO: T0086 add a root dipatch
      dispatchBranchInfoChanged({ branch: data, updateUrl: true })

      await loadBranches()
      if (
        dirtyChildLoadingFlag === DirtyChildLoadingFlag.RECALCULATE_ON_DIRTY
      ) {
        await loadManufacturing({
          bomNodeId: loadedNode.value.id,
          branch: data.id,
          force: true,
        })
      }
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
    } catch (error: any) {
      if (!isAxiosError(error)) {
        // restore component state
      }
      if (isDirtyChildError(error)) {
        return Promise.reject(error)
      }
      throw error
    } finally {
      setManufacturingLoading(false)
    }
  }

  async function duplicateBranch({
    name,
    renameDescendants,
    dirtyChildLoadingFlag = DirtyChildLoadingFlag.FAIL_ON_DIRTY,
  }: {
    name: string
    renameDescendants: boolean
    dirtyChildLoadingFlag?: DirtyChildLoadingFlag
  }): Promise<void> {
    if (!loadedNode.value) {
      return
    }
    const { id: nodeId, branch } = loadedNode.value
    try {
      const { data } = loadedNode.value!.branch.master
        ? await copyMain(
            { nodeId, branch, name, renameDescendants }!,
            dirtyChildLoadingFlag
          )
        : await _duplicateBranch(
            { nodeId, branch, name, renameDescendants },
            dirtyChildLoadingFlag
          )
      bomExplorerBus.nodeUpdated.emit({ id: nodeId, branchId: data.id })
      await loadManufacturing({
        bomNodeId: loadedNode.value!.id,
        branch: data.id,
      })
      dispatchBranchInfoChanged({ branch: data, updateUrl: true })
      if (!branches.value.find((b: Branch) => b.id === data.id)) {
        setBranches([...branches.value, data])
      }
    } catch (error: unknown) {
      if (!isAxiosError(error)) {
        // restore component state
      }
      if (isDirtyChildError(error)) {
        return Promise.reject(error)
      }
      throw error
    }
  }

  function handleManufacturingLoading({
    bomNodeId,
    newValue,
  }: {
    bomNodeId: BomNodeId
    newValue: boolean
  }) {
    if (
      loadedNode.value?.id === bomNodeId &&
      newValue !== manufacturingIsLoading.value
    ) {
      setManufacturingLoading(newValue)
    }
  }

  // inlined from NavigationStore
  function dispatchBranchInfoChanged(payload: BranchInfoChangedPayload) {
    setBranchInfo(payload)

    const router = getRouter()

    const {
      branch: { id: branchId },
      updateUrl,
    } = payload

    const currentRoute = getCurrentRoute()
    if (updateUrl && currentRoute.query.branch !== branchId) {
      router
        .replace({
          ...currentRoute,
          query: {
            ...currentRoute.query,
            branch: branchId,
          },
        } as RouteLocation)
        .catch((error: Error) => {
          console.error('router complains about ', error)
        })
    }
  }

  function setBranchInfo(payload: BranchInfoChangedPayload) {
    if (loadedNode.value) {
      loadedNode.value.branch = payload.branch
      loadedNode.value.title = payload.name ?? loadedNode.value.title
    }
  }

  async function discardChanges(): Promise<void> {
    await _discardChanges.execute()
  }

  async function undoChanges(): Promise<void> {
    try {
      const { id, branch } = loadedNode.value!
      const { data } = await _undoChanges(id, branch)
      setNode({ node: data })
    } catch (error: unknown) {
      if (!isAxiosError(error)) {
        // restore component state
      }
      throw error
    }
  }

  async function redoChanges(): Promise<void> {
    try {
      const { id, branch } = loadedNode.value!
      const { data } = await _redoChanges(id, branch)
      setNode({ node: data })
    } catch (error: unknown) {
      if (!isAxiosError(error)) {
        // restore component state
      }
      throw error
    }
  }

  async function rearrangeSteps(req: ReorderStepsBaseReq): Promise<void> {
    try {
      const { id, branch } = loadedNode.value!
      const { data } = await reorderSteps(id, branch, req)
      setNode({ node: data })
    } catch (error: unknown) {
      if (!isAxiosError(error)) {
        // restore component state
      }
      throw error
    }
  }

  async function rearrangeCycletimeSteps(
    req: ReorderCycleTimeStepsBaseReq
  ): Promise<void> {
    try {
      const { id, branch } = loadedNode.value!
      const { data } = await reorderCycleTimeSteps(id, branch, req)
      // This lets the table know that it needs to use replaceData to render the correct status
      ctsTableIsReordering.value = true
      setNode({ node: data })
    } catch (error: unknown) {
      if (!isAxiosError(error)) {
        // restore component state
      }
      throw error
    }
  }

  async function deleteObject(entityId: ManufacturingDTO['id']) {
    try {
      const { id, branch } = loadedNode.value!
      const { data } = await deleteEntity(id, branch, entityId)
      setNode({ node: data })
    } catch (error: unknown) {
      if (!isAxiosError(error)) {
        // restore component state
      }
      throw error
    }
  }

  async function loadBranches(req?: {
    force?: boolean
    id?: string
  }): Promise<Branch[]> {
    const nodeId = req?.id || loadedNode.value?.id
    try {
      if (!nodeId) {
        return []
      }
      const data = await fetchBranchesApi(nodeId)
      setBranches(data)
      return data
    } catch (error: unknown) {
      if (!isAxiosError(error)) {
        // restore component state
      }
      throw error
    }
  }

  function setTabledata(tableData: TableData[]) {
    //! private
    tableSetup.value.tableData = tableData
  }

  function setBranches(payload: Array<Branch>) {
    branches.value = payload
  }

  function getExchangeRates() {
    //! private besides test
    if (loadedManufacturing.value) {
      const entities = allEntitiesOfType(
        loadedManufacturing.value,
        'EXCHANGE_RATES'
      )
      if (entities.length == 0) {
        return
      }
      const currencyInfo =
        (getField(entities[0], 'exchangeRates')?.value as CurrencyInfo) ?? null
      exchangeRates.value = currencyInfo
    }
  }

  async function renameProjectBranch({
    branchId,
    title,
    renameDescendants,
  }: {
    branchId: string
    title: string
    renameDescendants: boolean
  }): Promise<Nullable<BomNodeEntity>> {
    const bomNodeId = loadedNode.value?.id

    if (branchId && bomNodeId) {
      try {
        const data = await _renameProjectBranch(
          bomNodeId,
          branchId,
          title,
          renameDescendants
        )
        await setNode({ node: data })
        return data
      } catch (error: unknown) {
        if (!isAxiosError(error)) {
          // restore component state
        }
        throw error
      }
    }
    return null
  }

  async function uploadAttachment({
    file,
    isMainImage,
  }: {
    file: File
    isMainImage: boolean
  }) {
    const { id: bomNodeId, branch } = loadedNode.value ?? {}
    const projectId = costStore.loadedProject?._id
    if (!bomNodeId || !projectId || !branch) {
      return
    }

    const formData = createFormDataFiles([file], bomNodeId)
    formData.append('uploadType', UploadType.BOM_NODE_ATTACHMENT)

    const data = await postUpload(formData)
    const newFile = data[0]

    const dto: CreateObjectDto = {
      currency: displayCurrency.value,
      entityType: 'ATTACHMENT',
      fields: [
        newField('fileId', newFile.id),
        newField(
          'isMainImage',
          isMimeTypeImage(newFile.mimeType) && isMainImage,
          'Bool'
        ),
        newField('name', newFile.filename),
        newField('mimeType', newFile.mimeType),
        newField('createdDate', newFile.createdDate),
        newField('displayDesignation', newFile.filename),
      ],
      masterDataKey: null,
      parentId: loadedManufacturing?.value?.id,
    }

    const threeDbVersionedPart = newFile.threeDbVersionedPart
    if (threeDbVersionedPart) {
      dto.fields.push(
        newField(
          'versionedPart',
          threeDbVersionedPart,
          'VersionedPart',
          {},
          '',
          'I'
        )
      )
    }

    const { data: newBomNode } = await createObject(
      bomNodeId,
      branch,
      projectId,
      dto
    )
    setNode({ node: newBomNode })
  }

  const testObject = {
    /** state */
    activeStepId, //! private
    bomNode: loadedNode, //! private
    branches,
    cbdTableIsLoading,
    clickedNodePath,
    exchangeRates,
    fetchingId,
    loadedManufacturingId, //! private
    loadManufacturingObserver, //! private
    manufacturingIsLoading,
    manufacturingIsLoadingChanges,
    soonToBeLoadedBomNodeBranch,
    tableSetup,
    ctsTableIsReordering,

    /** getter */
    activeStep,
    calculationCurrency,
    calculationType,
    conflicts,
    conflictStatus,
    costPerPart,
    isCo2Broken,
    isCostBroken,
    isProtectedCalculation,
    isMaster,
    isMasterWorkingCopy,
    loadedManufacturing,
    loadedNode,
    loadedNodeHasUnsavedState,
    manufacturingUnitField,
    pendingChanges,
    sourceBranchId,
    stepRootEntity,
    technology,

    /** mutations */
    clearSoonToBeLoadedBomNodeBranch,
    resetFetchingId,
    resetLoadedNode,
    setBranches,
    setCbdTableLoading, // for testing
    setClickedNodePath,
    setFetchingId,
    setManufacturingLoading,
    setSoonToBeLoadedBomNodeBranch,
    setStepId,
    updateManufacturingCancel,

    /** actions */
    addAutomatedLine,
    deleteManufacturing,
    deleteObject,
    deleteRootManufacturing,
    discardChanges,
    dispatchBranchInfoChanged,
    duplicateBranch,
    getManufacturingField,
    getUpdatesFromMasterData,
    handleManufacturingLoading,
    loadBranches,
    loadManufacturing,
    loadManufacturingWithOpenMerges,
    mergeBranch,
    rearrangeCycletimeSteps,
    rearrangeSteps,
    recalculateManufacturing,
    redoChanges,
    reloadManufacturing,
    renameProjectBranch,
    revertAutomatedLine,
    saveBranch,
    saveChanges,
    setNode,
    setNodeInt,
    subscribeToLoadManufacturing,
    undoChanges,
    updateAndPublishBomNode,
    updateAndSaveChanges,
    updateAndUseAsMain,
    updateManufacturingOverwrite,
    updateManufacturingUnlock,
    uploadAttachment,
  }

  return testObject
})

export const manufacturingStore = useManufacturingStore(getPiniaInstance())
