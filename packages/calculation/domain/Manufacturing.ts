import { isMissingValueField } from '@domain/calculation/utils/isMissingFieldValue'
import { vueSet } from '@tset/shared-utils/helpers/component/componentMigration'
import { isFieldVisible } from '@tset/shared-utils/helpers/field/isFieldVisible'
import { containsAllCriterias } from '@tset/shared-utils/helpers/general'
import { isFieldManuallyOverridden } from '@tset/shared-utils/helpers/overriden'
import { shouldNeverHappen } from '@tset/shared-utils/helpers/typescript'
import { reactive } from 'vue'
import ManufacturingDb from './ManufacturingDb'

export default class Manufacturing implements ManufacturingDTO {
  id: string
  className: string
  copyable: boolean
  createdByField?: Nullable<ManufacturingCreatedBy>
  createdByMocked?: boolean
  createdOnBranch?: Nullable<string>
  deletable: boolean
  generated?: boolean
  isolated: boolean
  key: Nullable<string>
  latestMasterDataVersion?: number
  masterDataKey?: MasterDataComposite
  model?: Nullable<ModelListEntry>
  name: string
  parentId?: ManufacturingDTO['id']
  parentManufacturingId: Nullable<ManufacturingDTO['id']>
  part?: Nullable<Part>
  ref: string
  shape?: Nullable<Shape>
  type: ManufacturingEntityType
  usedMasterDataVersion?: number
  version: number
  customProcurementType?: string

  private _fields: Record<ResultField['name'], ResultField> = reactive({})
  // loading spinner enabled on fields

  constructor(
    entity: ManufacturingDTO,
    parentManufacturingId: Nullable<string> = null
  ) {
    this.id = entity.id
    this.className = entity.className
    this.copyable = entity.copyable
    this.createdByField = entity?.createdByField
    this.createdByMocked = entity?.createdByMocked
    this.createdOnBranch = entity?.createdOnBranch
    this.deletable = entity.deletable
    this.generated = entity?.generated
    this.isolated = entity.isolated
    this.key = entity.key
    this.latestMasterDataVersion = entity?.latestMasterDataVersion
    this.masterDataKey = entity?.masterDataKey
    this.model = entity?.model
    this.name = entity.name
    this.parentId = entity?.parentId
    this.parentManufacturingId = parentManufacturingId
    this.part = entity?.part
    this.ref = entity.ref
    this.shape = entity?.shape
    this.type = entity.type
    this.usedMasterDataVersion = entity?.usedMasterDataVersion
    this.version = entity.version
    this.customProcurementType = entity.customProcurementType

    this.setFields(entity.fields)

    // make getters of this class enumerable, otherwise they would get lost when using the spread operator
    makeEnumerable(this, 'children')
    makeEnumerable(this, 'hasOptimisticField')
    makeEnumerable(this, 'hasManuallyOverriddenField')
    makeEnumerable(this, 'isMasterData')
    makeEnumerable(this, 'fields')
  }

  private setFields(fieldList: ResultField[]): void {
    for (const field of fieldList) {
      this.setField(field)
    }
  }

  setField(field: ResultField) {
    vueSet(this._fields, field.name, {
      ...field,
      metaInfo: {
        ...(field.metaInfo ?? {}),
        overwriteParentInfo: {
          id: this.id,
          type: this.type,
          ref: this.ref,
          className: this.className,
        },
      },
    })
  }

  resetField(fieldName: ResultField['name']) {
    vueSet(this._fields, fieldName, { ...this._fields[fieldName] })
  }

  get hasOptimisticField(): boolean {
    for (const fieldName in this._fields) {
      if (this.getField(fieldName)?.source === 'O') {
        return true
      }
    }
    return false
  }

  get hasManuallyOverriddenField(): boolean {
    for (const fieldName in this._fields) {
      const field = this.getField(fieldName)
      if (field && isFieldManuallyOverridden(field)) {
        return true
      }
    }
    return false
  }

  get isMasterData(): boolean {
    return !!this.masterDataKey
  }

  get fields() {
    return this.getFields()
  }

  get fieldsMap() {
    return {
      ...this._fields,
    }
  }

  get children() {
    return this.getChildren()
  }

  /**
   * This function is to be used to extract any entity inside the loaded Manufacturing.
   * If no criterias are passed, it will return all Entities.
   *
   * @param criterias can contain any primitive attributes on a Manufacturing.
   * You can pass in a single value per criteria or an array of values which are combined via an OR logic.
   * @returns An array of Manufacturings which fulfill the criterias passed
   * @example
   * entity.getEntities({ type: ['MATERIAL', 'BOM_ENTRY'] })
   * entity.getEntities({ type: 'MATERIAL' })
   */
  getEntities(criterias: Nullable<EntityCriteria> = null) {
    return ManufacturingDb.getEntities(criterias)
  }

  /**
   *
   * @param id the id of the entity to get.
   * @returns A Manufacturing with given id
   */
  getEntity(id: Manufacturing['id']) {
    return ManufacturingDb.getEntity(id)
  }

  /**
   * This function is to be used to extract any entity inside the entity which the method was called on.
   * If no criterias are passed, it will return all Entities.
   *
   * @param criterias can contain any primitive attributes on a Manufacturing.
   * You can pass in a single value per criteria or an array of values which are combined via an OR logic.
   * @returns An array of Manufacturings which fulfill the criterias passed
   * @example
   * entity.getEntities({type: ['MATERIAL', 'BOM_ENTRY']})
   * entity.getEntities({type: 'MATERIAL'})
   */
  getSubEntities(criterias: Nullable<EntityCriteria> = null): Manufacturing[] {
    return ManufacturingDb.getSubEntities(this, criterias)
  }

  /**
   * This function is used to extract any entity nested inside the Manufacturing.
   * If no criterias are passed, it will return all chil Entities.
   *
   * @param criterias
   * @returns An array of child Manufacturings which fulfill the criterias passed
   * @example
   * entity.getChildren({type: ['MATERIAL', 'BOM_ENTRY']})
   * entity.getChildren({type: 'MATERIAL'})
   */
  getChildren(criterias: Nullable<EntityCriteria> = null): Manufacturing[] {
    return ManufacturingDb.getChildren(this, criterias)
  }

  /**
   *
   * @param id THe id of the child to get.
   * @returns A child Manufacturing with given id
   */
  getChild(id: ManufacturingDTO['id']): Nullable<Manufacturing> {
    return ManufacturingDb.getChild(this, id)
  }

  /**
   *
   * @param fieldName The name of the field to get.
   * @param injectedMetaInfo Optional parameter to inject metaInfo.
   * @param optimistic Optional parameter to include the optimistic state of the field. Defaults to true.
   * @returns The field if found
   * @example
   * entity.getField<number>('costPerPart')
   * entity.getField<number>('costPerPart', {readOnly: true})
   */
  getField<T extends ResultFieldValueType = ResultFieldValueType>(
    fieldName: ResultField['name'],
    injectedMetaInfo: Nullable<InjectedMetaInfo<T>> = null,
    optimistic: boolean = true
  ): Nullable<ResultField<T>> {
    let field = ManufacturingDb.getOptimisticField<T>(this, fieldName)
    if (!optimistic || !field) {
      field = this._fields[fieldName] as ResultField<T>
    }
    if (!field) {
      return null
    }
    if (injectedMetaInfo) {
      // !NOTE generate new field object when metaInfo is injected to erase previous reference
      field = { ...field }
      if (typeof injectedMetaInfo === 'function') {
        field = injectedMetaInfo(field)
      } else {
        field.metaInfo = { ...field.metaInfo, ...injectedMetaInfo }
      }
    }
    return field
  }

  /**
   * Unsafe method to get a non-nullable resultfield
   * @param fieldName The name of the field to get.
   * @param injectedMetaInfo Optional parameter to inject metaInfo.
   * @param optimistic Optional parameter to include the optimistic state of the field. Defaults to true.
   * @returns The field on the assumption that it will always exist
   */
  getFieldUnsafe<T extends ResultFieldValueType = ResultFieldValueType>(
    ...args: Parameters<typeof this.getField<T>>
  ): ResultField<T> {
    return this.getField(...args)!
  }

  /**
   *
   * @param fieldNames An array of field names to extract.
   * @param injectedMetaInfo A Dictionary mapping metaInfo to infect to the requested field names.
   * @returns An array of fields. Might be empty.
   * @example
   * entity.getFields(['costPerPart', 'displayDesignation'])
   * entity.getFields(['costPerPart', 'displayDesignation'], {
   *  costPerPart: {readOnly: true},
   *  displayDesignation: {translationSection: 'actions'}
   * })
   */
  getFields<T extends ResultField['name']>(
    fieldNames: Nullable<readonly T[]> = null,
    injectedMetaInfo: Nullable<
      InjectedMetaInfoRecord<T> & InjectedMetaInfoRecord<'*'>
    > = null
  ): ResultField[] {
    const fieldArray: ResultField[] = []
    for (const name of fieldNames ?? (Object.keys(this._fields) as T[])) {
      const field = this.getField(
        name,
        injectedMetaInfo?.[name] ?? injectedMetaInfo?.['*'] ?? null
      )
      if (field) {
        fieldArray.push(field)
      }
    }
    return fieldArray
  }

  /**
   *
   * @param criterias any partial metaInfo which are primitive
   * @param scope the scope to search for the fields.
   * 'global' searches all entities, 'local' only the entity on which the method was called,
   * 'recursive' only on the entity where the method was called and recursively its children. 'global' by default
   * @returns all fields which match the criterias
   * @example
   * fieldFieldsByMetaInfo({translationsSection: 'ABC'}): returns all fields with 'ABC' as translationSection
   * fieldFieldsByMetaInfo({translationsSection: ['ABC', 'DEF']}): returns all fields with 'ABC' OR 'DEF' as translationSection
   *
   *
   */
  findFieldsByMetaInfo(
    criterias: FieldCriteria,
    scope: 'local' | 'global' | 'recursive' = 'global'
  ): ResultField[] {
    try {
      switch (scope) {
        case 'local':
          return this.fields.filter(({ metaInfo }) => {
            if (!metaInfo) {
              return false
            }
            return containsAllCriterias(metaInfo, criterias)
          })
        case 'global':
          return ManufacturingDb.findFields(criterias)
        case 'recursive':
          return ManufacturingDb.findFields(criterias, this)
        default:
          throw shouldNeverHappen('criteria', scope)
      }
    } catch (e: unknown) {
      console.error(`findFields error'd: ${e}`)

      return []
    }
  }

  /**
   *
   * @returns The parent Manufacturing.
   */
  getParent(): Nullable<Manufacturing> {
    return ManufacturingDb.getParent(this)
  }

  getUiConfigurationKey(): UIConfigurationIdentifiers {
    return ManufacturingDb.getRootEntity().getField<UIConfigurationIdentifiers>(
      'uiConfigKeys'
    )!.value
  }

  /**
   *
   * @returns a boolean that indicates if there are direct missing fields on the entity itself
   */
  get isMissingFields() {
    return this.fields.some(
      (field) =>
        isMissingValueField(field) && isFieldVisible(field, this.fields)
    )
  }

  // list of entityTypes that are not actually child of similar entities because they are linked entities
  private readonly nonChildTypes: ManufacturingEntityType[] = [
    'MANUFACTURING_STEP',
    'CONSUMABLE',
    'BOM_ENTRY',
    'C_PART',
    'MATERIAL',
    'RAW_MATERIAL_MANUAL',
  ]

  /**
   * Check if this entity has missing fields in itself or its child entities
   *
   * @param checkChildren indicates if the check should look into the child entity
   * @returns a boolean that indicates if there are missing fields present in the entity
   */
  hasMissingFields({ checkChildren = true } = {}): boolean {
    if (this.isMissingFields) {
      return true
    }
    return (
      checkChildren &&
      this.children.some(
        (child) =>
          !this.nonChildTypes.includes(child.type) &&
          child.hasMissingFields({ checkChildren })
      )
    )
  }

  getEntityFields(fieldNames: string[]) {
    return ManufacturingDb.getRootEntity().getFields<string>(fieldNames)
  }
}

/**
 * Make a getter enumerable
 *
 * Useful if you want to preserve the property when using the spread operator/iterating via for .. in
 * @param instance instance of the class
 * @param property name of the getter
 */
function makeEnumerable<T>(instance: T, property: keyof T): void {
  Object.defineProperty(instance, property, {
    enumerable: true,
    get() {
      return Reflect.get(Object.getPrototypeOf(instance), property, instance)
    },
  })
}
