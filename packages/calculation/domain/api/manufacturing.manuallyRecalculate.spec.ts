import { gAxiosResponse } from '@tset/shared-utils/tests/generators/axiosResponse'
import { withAxiosMock } from '@tset/shared-utils/tests/mocks/withAxiosMock'
import { manuallyRecalculate } from './manufacturing'

const { getLastCall, axiosMock } = withAxiosMock()

const testId = 'testId'

describe('api/manufacturing => manuallyRecalculate()', () => {
  afterEach(() => {
    vi.resetAllMocks()
  })
  describe('throws an error without', () => {
    it('- `bomNodeId` and `branchId`', invalidInputTest(null, null))
    it('- `branchId`', invalidInputTest(testId, null))
    it('- `bomNodeId`', invalidInputTest(null, testId))
    it('- `data` in the `response`', invalidResponseTest)
  })
  it('- calls axios with the provided values', axiosTest)
  it('- returns the `data` from the `response`', validResponseTest)
})

const invalidInputTest =
  (bomNodeId: unknown, branchId: unknown) => async () => {
    // @ts-expect-error we test invalid IDs as well
    const actual = manuallyRecalculate(bomNodeId, branchId)
    const expected =
      "request '/api/man/{bomNodeId}/recalculate?source={branchId}' not possible without 'bomNodeId' and 'branchId'"
    await expect(actual).rejects.toThrowError(expected)
  }

const invalidResponseTest = async () => {
  axiosMock.post.mockResolvedValueOnce({})
  const actual = manuallyRecalculate(testId, testId)
  const expected = `request '/api/man/${testId}/recalculate?source=${testId}' returned no 'response.data'`
  await expect(actual).rejects.toThrowError(expected)
}

const axiosTest = async () => {
  axiosMock.post.mockResolvedValueOnce(gAxiosResponse())
  const bomNodeId = 'bomNodeId'
  const branchId = 'branchId'
  await manuallyRecalculate(bomNodeId, branchId)
  const expected = [`/api/man/${bomNodeId}/recalculate?source=${branchId}`]
  expect(getLastCall('post')).toMatchObject(expected)
}

const validResponseTest = async () => {
  const expected = 'expectedPayload'
  axiosMock.post.mockResolvedValueOnce(gAxiosResponse({ data: expected }))
  const actual = await manuallyRecalculate(testId, testId)
  expect(actual).toStrictEqual(expected)
}
