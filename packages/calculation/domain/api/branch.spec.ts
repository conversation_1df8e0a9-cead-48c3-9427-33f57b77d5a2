import { gBranch } from '@tset/shared-model/calculation/Branch'
import { DirtyChildLoadingFlag } from '@tset/shared-model/calculation/DirtyLoading'
import { MergeSource } from '@tset/shared-model/calculation/Merge'
import queue, {
  branchIdPlaceholder,
} from '@tset/shared-utils/api/ConcurrencyHandler'
import { gAxiosResponse } from '@tset/shared-utils/tests/generators/axiosResponse'
import { gBomNode } from '@tset/shared-utils/tests/generators/bomNode'
import type { MockedFunction } from 'vitest'
import { vi } from 'vitest'
import { copyMain, duplicateBranch, mergeBranch } from './branch'

vi.mock('@tset/shared-utils/api/ConcurrencyHandler/ConcurrencyHandler')
vi.mock('@tset/shared-utils/helpers/api')

describe('Branch API layer', () => {
  describe('Context aware calls', () => {
    let queueMock: MockedFunction<typeof queue>
    const mockBranch = [gBranch()]
    beforeAll(() => {
      queueMock = queue as MockedFunction<typeof queue>
    })

    beforeEach(() => {
      queueMock.mockClear()
    })

    afterAll(() => {
      queueMock.mockRestore()
    })

    describe('mergeBranch', () => {
      it('should use the Concurrency Handler queueing system', async () => {
        const bomNodeId = '0dd9e905-769a-4d8e-94a0-be9b71cde0ac'
        const branch = gBranch()
        const mergeSource = MergeSource.MASTER
        await mergeBranch(bomNodeId, branch, mergeSource)
        expect(queueMock).toHaveBeenCalledWith(bomNodeId, {
          method: 'post',
          urlTemplate: `/api/branch/\${branch.id}/merge?node=\${bomNodeId}&mergeSource=${mergeSource}`,
          context: {
            branch,
          },
        })
      })
    })
    describe('duplicateBranch', () => {
      it('should duplicate the Branch with the new name and return it with FAIL_ON_DIRTY flag', async () => {
        queueMock.mockResolvedValueOnce(gAxiosResponse({ data: mockBranch }))
        const branch = gBranch()
        const bomNodeId = gBomNode().bomNodeId
        const name = 'theBestVariantEVER'
        const renameDescendants = false
        const dirtyChildLoadingFlag = DirtyChildLoadingFlag.FAIL_ON_DIRTY
        const actual = await duplicateBranch({
          nodeId: bomNodeId,
          branch,
          name,
          renameDescendants,
        })
        expect(queueMock).toHaveBeenCalledTimes(1)
        expect(queueMock).toHaveBeenCalledWith(bomNodeId, {
          method: 'post',
          urlTemplate: `/api/branch/${branchIdPlaceholder()}/copy`,
          context: {
            branch,
          },
          queryParams: {
            title: name,
            rootNode: bomNodeId,
            dirtyChildLoadingMode: dirtyChildLoadingFlag,
            renameDescendants,
          },
        })
        expect(actual.data).toBe(mockBranch)
      })
    })
    describe('copyMain', () => {
      it('calls the ConcurrencyHandler', async () => {
        await copyMain({
          nodeId: 'some-random-id',
          branch: gBranch(),
          name: 'some-random-name',
          renameDescendants: true,
        })
        expect(queueMock).toHaveBeenCalledOnce()
      })
    })
  })
})
