import { fieldUpdateAbortCtrlAdapter } from '@/api/AbortControllers'
import { finishUpload } from '@/api/smf'
import { smfStore } from '@/store'
import type { ManufacturingCreationResponse } from '@tset/shared-model/calculation/CreateManufacturing'
import { DirtyChildLoadingFlag } from '@tset/shared-model/calculation/DirtyLoading'
import {
  CalculationCreationModalMode,
  CalculationPosition,
} from '@tset/shared-model/calculation/WizardCalculationEnums'
import queue, {
  bomNodeIdPlaceholder,
  branchIdPlaceholder,
  withBranchQueryPlaceholder,
  withSourceQueryPlaceholder,
  type Call,
} from '@tset/shared-utils/api/ConcurrencyHandler'
import { $axios, isAxiosError } from '@tset/shared-utils/helpers/api'
import { isAbortError } from '@tset/shared-utils/helpers/typeGuard/isAbortError'

// TODO: move to /api/project.ts
export async function createManufacturing(
  projectId: string,
  wizardId: string,
  currentSectionKey: string,
  input?: CalculationUpdateInput
): Promise<ManufacturingCreationResponse> {
  const dirtyChildLoading = !!(
    input &&
    input.mode === CalculationCreationModalMode.NEW &&
    input.position === CalculationPosition.SUB
  )
  const url = `/api/projects/${projectId}/wizards/${wizardId}/finish?dirtyChildLoading=${dirtyChildLoading}`
  const { data } = await $axios.post<ManufacturingCreationResponse>(url)
  // SMF | we need to finish the upload, if we started it in the smf-shape-wizard-step
  if (currentSectionKey === 'UNFOLD' && smfStore.fileUploadId !== null) {
    await finishUpload([smfStore.fileUploadId], data.bomNode.id)
  }
  return data
}

export async function updateField(
  id: BomNodeId,
  branch: Branch,
  body: ManufacturingUpdateBody,
  dirtyChildLoading: boolean = true
) {
  const abortController = fieldUpdateAbortCtrlAdapter.abortAndRenew(
    body.entityId,
    body.name
  )

  try {
    const urlTemplate = withSourceQueryPlaceholder(
      `/api/man/${bomNodeIdPlaceholder()}?dirtyChildLoading=${dirtyChildLoading}`,
      branch
    )
    const call: Call = {
      method: 'post',
      urlTemplate,
      body,
      context: {
        branch: branch,
      },
      signal: abortController.signal,
    }
    return queue<BomNodeEntity>(id, call)
  } catch (e: unknown) {
    if (isAbortError(e)) {
      fieldUpdateAbortCtrlAdapter.delete(body.entityId, body.name)
    }

    throw e
  }
}

/**
 * Would be 'updateField' if it was not such a special case for the main image of a calculation
 * @param id
 * @param branch
 * @param body
 * @returns
 */
export async function updateFieldChangeImage(
  id: BomNodeId,
  branch: Branch,
  body: ManufacturingUpdateBody
) {
  const urlTemplate = withSourceQueryPlaceholder(
    `/api/man/${bomNodeIdPlaceholder()}/image`,
    branch
  )
  const call: Call = {
    method: 'post',
    urlTemplate,
    body,
    context: {
      branch: branch,
    },
  }
  return await queue<BomNodeEntity>(id, call)
}

export async function restoreField(
  id: BomNodeId,
  branch: Branch,
  body: ManufacturingUpdateBody,
  dirtyChildLoading: boolean = true
) {
  const abortController = fieldUpdateAbortCtrlAdapter.abortAndRenew(
    body.entityId,
    body.name
  )

  try {
    const urlTemplate = withSourceQueryPlaceholder(
      `/api/man/${bomNodeIdPlaceholder()}/unlock?dirtyChildLoading=${dirtyChildLoading}`,
      branch
    )
    const call: Call = {
      method: 'post',
      urlTemplate,
      context: {
        branch,
      },
      body,
      signal: abortController.signal,
    }
    return await queue<BomNodeEntity>(id, call)
  } catch (e: unknown) {
    if (isAbortError(e)) {
      fieldUpdateAbortCtrlAdapter.delete(body.entityId, body.name)
    }

    throw e
  }
}

export async function publish(
  id: BomNodeId,
  branch: Branch,
  dirtyChildLoadingFlag: DirtyChildLoadingFlag = DirtyChildLoadingFlag.FAIL_ON_DIRTY
) {
  const urlTemplate = withBranchQueryPlaceholder(
    `/api/man/${bomNodeIdPlaceholder()}/publish`,
    branch
  )
  const call: Call = {
    method: 'post',
    urlTemplate,
    context: {
      branch,
    },
    queryParams: {
      dirtyChildLoadingMode: dirtyChildLoadingFlag,
    },
  }
  return await queue<BomNodeEntity>(id, call)
}

// TODO: use publish() instead
export async function publishDangerously(bomNodeId: BomNodeId) {
  const { data } = await $axios.post('/api/man/' + bomNodeId + '/publish')

  return data
}

export async function updateAndPublish(id: BomNodeId, branch: Branch) {
  const urlTemplate = withBranchQueryPlaceholder(
    `/api/man/${bomNodeIdPlaceholder()}/updateAndPublish`,
    branch
  )
  const call: Call = {
    method: 'post',
    urlTemplate,
    context: {
      branch,
    },
  }
  return await queue<BomNodeEntity>(id, call)
}

export async function updateAndUseAsMain(id: BomNodeId, branch: Branch) {
  const urlTemplate = withBranchQueryPlaceholder(
    `/api/man/${bomNodeIdPlaceholder()}/updateAndUseAsMain`,
    branch
  )
  const call: Call = {
    method: 'post',
    urlTemplate,
    context: {
      branch,
    },
  }
  return await queue<BomNodeEntity>(id, call)
}

/**
 * saves the branch as source
 * POST /api/branch/{branchId}/saveToSource
 * @param id id of the loaded node
 * @param branch id of the loaded branch
 * @param dirtyChildLoadingFlag flag to fail if the children need to be recalced
 * @returns Branch
 */
export async function saveToSourceBranch(
  id: BomNodeId,
  branch: Branch,
  dirtyChildLoadingFlag: DirtyChildLoadingFlag = DirtyChildLoadingFlag.FAIL_ON_DIRTY
) {
  const call: Call = {
    method: 'post',
    urlTemplate: `/api/branch/${branchIdPlaceholder()}/saveToSource`,
    context: {
      branch,
    },
    queryParams: {
      bomNodeId: id,
      dirtyChildLoadingMode: dirtyChildLoadingFlag,
    },
  }
  return await queue<Branch>(id, call)
}

export async function updateAndSaveToSourceBranch(
  id: BomNodeId,
  branch: Branch
) {
  const call: Call = {
    method: 'post',
    urlTemplate: `/api/branch/${branchIdPlaceholder()}/updateAndSaveToSource?node=${id}`,
    context: {
      branch,
    },
  }
  return await queue<Branch>(id, call)
}

export async function updateStatus(
  id: BomNodeId,
  branch: Branch,
  action: Pick<StatusAction, 'actionKey'>
) {
  const urlTemplate = withBranchQueryPlaceholder(
    `/api/man/${bomNodeIdPlaceholder()}/status`,
    branch
  )
  const call: Call = {
    method: 'put',
    urlTemplate,
    context: {
      branch,
    },
    body: { status: action.actionKey },
  }
  return await queue<BomNodeEntity>(id, call)
}

export async function fetchMasterDataUpdates(
  id: BomNodeId,
  branch: Branch,
  recursiveUpdate?: boolean
) {
  const urlTemplate = withBranchQueryPlaceholder(
    `/api/man/${bomNodeIdPlaceholder()}/masterdata/update`,
    branch,
    recursiveUpdate
  )
  const call: Call = {
    method: 'post',
    urlTemplate,
    context: {
      branch,
      recursiveUpdate,
    },
  }
  return await queue<BomNodeEntity>(id, call)
}

export async function undoChanges(id: BomNodeId, branch: Branch) {
  const urlTemplate = withBranchQueryPlaceholder(
    `/api/man/${bomNodeIdPlaceholder()}/undo?steps=1`,
    branch
  )
  const call: Call = {
    method: 'post',
    urlTemplate,
    context: {
      branch,
    },
  }
  return await queue<BomNodeEntity>(id, call)
}

export async function redoChanges(id: BomNodeId, branch: Branch) {
  const urlTemplate = withBranchQueryPlaceholder(
    `/api/man/${bomNodeIdPlaceholder()}/undo?steps=-1`,
    branch
  )
  const call: Call = {
    method: 'post',
    urlTemplate,
    context: {
      branch,
    },
  }
  return await queue<BomNodeEntity>(id, call)
}

export async function fetchBranchesApi(id: BomNodeId): Promise<Branch[]> {
  try {
    const { data } = await $axios.get<Branch[]>(`/api/man/${id}/branches`)
    return data
  } catch (error) {
    console.error('error:', error)
    return []
  }
}

export async function renameBranch(
  bomNodeId: BomNodeId,
  branch: Branch,
  name: string
) {
  const urlTemplate = withBranchQueryPlaceholder(
    `/api/man/${bomNodeIdPlaceholder()}/rename`,
    branch
  )
  const call: Call = {
    method: 'post',
    urlTemplate,
    context: {
      branch,
    },
    queryParams: {
      title: name,
    },
  }
  return await queue<BomNodeEntity>(bomNodeId, call)
}

export async function deleteBranch(
  projectId: string,
  bomNodeId: string,
  branch: Branch
) {
  const call: Call = {
    method: 'POST',
    urlTemplate: `/api/projects/${projectId}/branch/${branch.id}/delete`,
    context: {
      branch,
    },
  }

  return queue<Branch>(bomNodeId, call)
}

export async function deleteVariant(
  projectId: string,
  bomNodeId: string,
  variant: Branch
) {
  const call: Call = {
    method: 'POST',
    urlTemplate: `/api/projects/${projectId}/branch/${variant.id}/delete`,
    context: {
      branch: variant,
    },
  }

  return queue<Branch>(bomNodeId, call)
}

/**
 * Fetch the BomNodeEntity by its id
 *
 * GET /api/man/{bomNodeId}
 *
 * @param {BomNodeId} bomNodeId the BomNodeEntity id to fetch
 * @param {IFetchManufacturingParams} [params] params to add to the request
 * @returns the BomNodeEntity
 */
export async function fetchManufacturing(
  bomNodeId: BomNodeId,
  params?: IFetchManufacturingParams
): Promise<BomNodeEntity> {
  // COST-8000: No need for Concurrency Handler as triggered on navigation when switching Calculation
  const { data } = await $axios.get<BomNodeEntity>('/api/man/' + bomNodeId, {
    ...(params && { params }),
  })
  return data
}

/**
 * Used to recalculate dirty children
 *
 * POST /api/man/{bomNodeId}/dirty/recalculate?branch={branchId}
 *
 * @param bomNodeId id of the loaded node
 * @param params target and source are branch ids
 * @returns BomNodeEntity with recalculated children
 */
export async function recalculateManufacturing(
  bomNodeId: BomNodeId,
  params: { branch: string; force?: boolean }
) {
  // COST-8000: No need for Concurrency Handler as triggered on navigation when switching Calculation
  const { data } = await $axios.post<BomNodeEntity>(
    `/api/man/${bomNodeId}/dirty/recalculate?branch=${params?.branch}`
  )
  return data
}

/**
 * Used to save the branch
 *
 * POST /api/branch/{branchId}/savePublicVariant?title={name}&bomNodeId={bomNodeId}&dirtyChildLoadingMode={dirtyChildLoadingFlag}&renameDescendants={renameDescendants}
 *
 * @param branchId id of the loaded branch
 * @param name title of the new branch
 * @param bomNodeId id of the loaded bomNode
 * @param renameDescendants Whether to also update descendants
 * @param dirtyChildLoadingFlag should the new branch children be recalculated
 * @returns the new branch
 */
export async function savePublicVariant(
  {
    bomNodeId,
    branch,
    name,
    renameDescendants,
  }: {
    bomNodeId: BomNodeId
    branch: Branch
    name: string
    renameDescendants: boolean
  },

  dirtyChildLoadingFlag: DirtyChildLoadingFlag = DirtyChildLoadingFlag.FAIL_ON_DIRTY
) {
  const urlTemplate = `/api/branch/${branch.id}/savePublicVariant`

  const call: Call = {
    method: 'post',
    urlTemplate,
    context: {
      branch,
    },
    queryParams: {
      title: name,
      bomNodeId,
      dirtyChildLoadingMode: dirtyChildLoadingFlag,
      renameDescendants: String(renameDescendants),
    },
  }
  return await queue<Branch>(bomNodeId, call)
}

/**
 * Delete a root Calculation
 *
 * DELETE /api/man/{bomNodeId}
 *
 * @param {string} bomNodeId the id of the Calculation to delete
 * @param {string} branchId the branch Id of the Calculation to delete
 */
export async function deleteRootManufacturing(req: {
  bomNodeId: string
  branchId: string
}): Promise<void> {
  await $axios.delete(`/api/man/${req.bomNodeId}?branch=${req.branchId}`)
}

/**
 * Rename a branch
 *
 * POST /api/man/{bomNodeId}/rename?title={title}&branch={branchId}&renameDescendants=${renameDescendants}
 *
 * @param {string} bomNodeId The bomNode id
 * @param {string} branchId The branch id
 * @param {string} title The new branch name
 * @param {boolean} renameDescendants Whether to also update descendants
 */
export async function renameProjectBranch(
  bomNodeId: string,
  branchId: string,
  title: string,
  renameDescendants: boolean
): Promise<BomNodeEntity> {
  const { data } = await $axios.post<BomNodeEntity>(
    `/api/man/${bomNodeId}/rename?title=${title}&branch=${branchId}&renameDescendants=${renameDescendants}`
  )
  return data
}

/**
 * Used to fetch fields/inputs of a shape
 *
 * POST /api/inputs?entity={entity}&partId={partId}&shapeId={shapeid}
 *
 * @param entity the loaded entity
 * @param partId id of the loaded part
 * @param shapeId id of the loaded shape
 * @returns the returned fields
 */
export async function fetchShapeFields(
  entity: string,
  shapeId: ResultFieldValueType
): Promise<ResultField[]> {
  const searchParams = new URLSearchParams()

  searchParams.append('shapeId', String(shapeId))
  searchParams.append('entity', entity)

  const queryString = searchParams.toString()

  const url = `/api/inputs${queryString.length > 0 ? `?${queryString}` : ''}`
  const {
    data: { fields },
  } = await $axios.get<{ fields: ResultField[] }>(url)
  return fields
}

/**
 * Used to recalculate a manufacturing manually
 *
 * POST /api/man/{bomNodeId}/recalculate?source={branchId}
 *
 * @param bomNodeId id of the loaded node
 * @param branchId source are branch id
 * @returns recalculated BomNodeEntity
 */
export async function manuallyRecalculate(
  bomNodeId: BomNodeId,
  branchId: Branch['id']
): Promise<BomNodeEntity> {
  if (!bomNodeId || !branchId) {
    throw new Error(
      `request '/api/man/{bomNodeId}/recalculate?source={branchId}' not possible without 'bomNodeId' and 'branchId'`
    )
  }
  const url = `/api/man/${bomNodeId}/recalculate?source=${branchId}`
  try {
    const response = await $axios.post(url)
    if (!response.data) {
      throw new Error(
        `request '/api/man/${bomNodeId}/recalculate?source=${branchId}' returned no 'response.data'`
      )
    }
    return response.data
  } catch (error: unknown) {
    if (!isAxiosError(error)) {
      // restore component state
    }
    throw error
  }
}
