import {
  gTsetEntityPreviewSection,
  gTsetEntityPreviewSectionDrivers,
} from '@tset/shared-utils/tests/generators/tsetEntityPreview'
import { mount, type ComponentMountingOptions } from '@vue/test-utils'
import type { TsetEntityPreviewSection } from '.'
import TsetEntityPreviewData from './TsetEntityPreviewData.vue'
//#region MOCKS
vi.mock('@tset/shared-utils/helpers/manufacturing', () => ({
  getIconNameFromType: vi.fn(),
}))

vi.mock('@tset/shared-utils/formatters/formatNumber', () => ({
  $formatNumber: vi.fn((value) => value.toString()),
}))

//#endregion MOCKS

//#region TESTS
describe('TsetEntityPreviewData component', () => {
  it('- renders the component', () => {
    const { then } = setup([gTsetEntityPreviewSection()])
    expect(then.wrapperExists()).toBe(true)
  })
  it('- renders the correct number of drivers within a section', () => {
    const mockData = [gTsetEntityPreviewSection(), gTsetEntityPreviewSection()]
    const { then } = setup(mockData)
    expect(then.drivers().length).toBe(mockData.length)
  })
  it('- renders the correct cost and percentage for each driver', () => {
    const mockData = [
      gTsetEntityPreviewSection({
        title: 'Driver 1',
        drivers: [
          gTsetEntityPreviewSectionDrivers({
            label: 'Driver 1',
            cost: '123.00',
            unit: 'EUR',
            percentage: '1.25',
            icon: 'IconName',
          }),
        ],
      }),
    ]
    const { then } = setup(mockData)
    then.drivers().forEach((driver, index) => {
      const driverData = mockData[0]['drivers'][index]
      const cost = driver.find('.text-body-semibold')
      const percentage = driver.findByDataTest('percentage')

      expect(cost.text()).toContain('123.00')
      if (driverData.percentage !== null) {
        // eslint-disable-next-line no-irregular-whitespace
        expect(percentage.text()).toBe(`${driverData.percentage} %`)
      } else {
        expect(percentage.exists()).toBe(false)
      }
    })
  })

  it('- renders no drivers when they are empty', () => {
    const { then } = setup([])
    expect(then.drivers().length).toBe(0)
  })

  describe('show all button', () => {
    const prefilledSection = (amountOfDrivers: number) => {
      return gTsetEntityPreviewSection({
        drivers: Array(amountOfDrivers).fill(
          gTsetEntityPreviewSectionDrivers()
        ),
      })
    }
    it('- renders the "Show all" button when there are more than 4 drivers and the section is not expanded', () => {
      const { then } = setup([prefilledSection(5)])
      expect(then.showAllButton(0).exists()).toBe(true)
      expect(then.showAllButton(0).text()).toBe('Show all')
    })

    it('- does not render the "Show all" button when there are 4 or fewer drivers', () => {
      const { then } = setup([prefilledSection(4)])
      expect(then.showAllButton(0).exists()).toBe(false)
    })

    it('- does not render the "Show all" button when the section is already expanded', async () => {
      const { then } = setup([prefilledSection(5)])

      // Click the "Show all" button to expand the section
      await then.showAllButton(0).trigger('click')

      // Check that the button is no longer rendered
      expect(then.showAllButton(0).exists()).toBe(false)
    })

    it('- shows all drivers when the "Show all" button is clicked', async () => {
      const { then } = setup([prefilledSection(6)])
      // Initially, only 4 drivers should be shown
      expect(then.drivers().length).toBe(4)
      await then.showAllButton(0).trigger('click')
      // Now, all drivers should be shown
      expect(then.drivers().length).toBe(6)
    })

    it('- tracks expanded sections in the shownDriverIndex array', async () => {
      const { then } = setup([prefilledSection(5), prefilledSection(6)])

      // Expand the first section
      const showAllButton1 = then.showAllButton(0)
      await showAllButton1.trigger('click')

      // Check that the first section is tracked as expanded
      expect(then.shownDriverIndex()).toContain(0)
      expect(then.shownDriverIndex()).not.toContain(1)

      // Expand the second section
      const showAllButton2 = then.showAllButton(1)
      await showAllButton2.trigger('click')

      // Check that both sections are tracked as expanded
      expect(then.shownDriverIndex()).toContain(0)
      expect(then.shownDriverIndex()).toContain(1)
    })
  })
})
//#endregion TESTS

//#region SETUP FACTORY
const setup = (data: TsetEntityPreviewSection[]) => {
  const mountOptions: ComponentMountingOptions<typeof TsetEntityPreviewData> = {
    props: {
      sections: data,
    },
  }

  const wrapper = mount(TsetEntityPreviewData, mountOptions)

  //#region HELPERS
  const wrapperExists = () => wrapper.exists()
  //#endregion HELPERS

  //#region THEN
  // wrapper.find('span.text-body-semibold')
  const driverInfoTitle = () => wrapper.findAllByDataTest('driver-info-title')
  const drivers = () => wrapper.findAllByDataTest('driver')
  const driverLabel = () => wrapper.findByDataTest('driver-label')
  const showAllButton = (index: number) =>
    wrapper.findByDataTest(`show-all-button-${index}`)
  const shownDriverIndex = () => wrapper.vm.shownDriverIndex
  const then = {
    drivers,
    driverInfoTitle,
    driverLabel,
    wrapperExists,
    showAllButton,
    shownDriverIndex,
  }
  //#endregion THEN

  return {
    then,
  }
}
//#endregion SETUP FACTORY
