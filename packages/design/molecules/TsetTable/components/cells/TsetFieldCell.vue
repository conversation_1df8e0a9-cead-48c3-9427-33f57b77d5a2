<template>
  <div
    class="flex w-full flex-row items-center"
    :class="{ 'pl-8': hasRightIcon }"
  >
    <slot name="right-icon" />
    <TsetTextCellIcon
      v-if="iconsBefore?.length || hasWarningDot"
      :cell="cell"
      :icons-before="iconsBefore"
    />
    <component
      :is="component"
      v-if="component && field"
      ref="inputRef"
      class="w-full"
      :field="computedField"
      :unit="localUnit"
      :data-type="getType(props.cell.getValue()?.type)"
      :parent-entity="parentEntity"
      :is-manually-overridden="isFieldManuallyOverridden(props.cell.getValue())"
      :tset-table-editor="true"
      :tset-table-editor-last="isCellLastElement"
      :editable-table-cell="true"
      :row-hovered="false"
      :hide-hover-outline="true"
      :hoz-align="cellHozAlignment"
      :currency="rowCurrency ?? displayCurrency"
      @change="change"
      @restore="restore"
      @focusout="focusOut()"
      @focusin="focusIn"
    >
      <template #unit>
        <span
          v-if="cellUnit"
          class="text-caption-semibold w-auto items-end pr-8 text-black-lighter"
        >
          {{ cellUnit }}
        </span>
      </template>
    </component>
  </div>
</template>

<script setup lang="ts">
import { isManuPage, isProjectPage } from '@/router/router-tools'
import { manufacturingStore } from '@domain/calculation/manufacturing.store'
import { type FieldType, type FieldUnit } from '@shared/field-types'
import { getType } from '@shared/field-types/fieldTypes'
import NuCurrencyPicker from '@tset/shared-ui/parts/fields/NuCurrencyPicker/NuCurrencyPicker.vue'
import NuInputDate from '@tset/shared-ui/parts/fields/NuInputDate.vue'
import NuInputMoney from '@tset/shared-ui/parts/fields/NuInputMoney.vue'
import NuInputNumber from '@tset/shared-ui/parts/fields/NuInputNumber.vue'
import NuInputReadonly from '@tset/shared-ui/parts/fields/NuInputReadonly.vue'
import NuInputSelect from '@tset/shared-ui/parts/fields/NuInputSelect.vue'
import NuInputText from '@tset/shared-ui/parts/fields/NuInputText.vue'
import NuInputTree from '@tset/shared-ui/parts/fields/NuInputTree.vue'
import { useDisplayCurrency } from '@tset/shared-utils/api/useDisplayCurrency'
import { hasSlot } from '@tset/shared-utils/helpers/component/componentMigration'
import {
  fieldComponent,
  getRateFieldValue,
  mapFormattedField,
} from '@tset/shared-utils/helpers/field'
import { focusFirstInput } from '@tset/shared-utils/helpers/input/focusFirstInput'
import { newFieldUnit } from '@tset/shared-utils/helpers/manufacturing'
import { isFieldManuallyOverridden } from '@tset/shared-utils/helpers/overriden'
import { isResultField } from '@tset/shared-utils/helpers/typeGuard/isResultField'
import { useResizeObserver } from '@vueuse/core'
import type { CellComponent } from 'tabulator-tables'
import { computed, onMounted, provide, ref, toRaw, toRef, useSlots } from 'vue'
import TsetTextCellIcon from './TsetTextCellParts/TsetTextCellIcon.vue'

//#region PROPS
interface TsetFieldCellProps {
  cell: CellComponent
  cellUnit?: string
  iconsBefore?: TableIcon[]
  hasWarningDot?: boolean
}

const props = defineProps<TsetFieldCellProps>()
const cell = toRef(props, 'cell')
//#endregion PROPS

//#region EMITS
const emit = defineEmits<{
  (e: 'cancel'): void
  (e: 'focusin'): void
  (e: 'change', payload: FieldCellEditEvent): void
  (e: 'restore', payload: FieldCellEditEvent): void
  (e: 'customTextBuilt', payload: string): void
}>()
//#endregion EMITS

//#region PROVIDE
// This provides exist on this level due to the rendering via an
// extra vNode of the cells, which decouples the provide/inject flow
provide('exchangeRates', manufacturingStore.exchangeRates)
provide('calculationBaseCurrency', manufacturingStore.calculationCurrency)
provide(
  'objectBaseCurrency',
  computed(() => {
    return manufacturingStore.loadedManufacturing?.getField('baseCurrency')
      ?.value
  })
)
provide(
  'context',
  isManuPage() || isProjectPage() ? 'calculation' : 'masterdata'
)
//#endregion PROVIDE

//#region INPUT_REF
const inputRef = ref<FixmeType>()
const inputEl = computed(() => inputRef.value?.$el as HTMLElement)
const isFocused = ref(false)

useResizeObserver(inputEl, async () => {
  if (isFocused.value) {
    await props.cell.getColumn().scrollTo('middle', false)
  }
})

async function focusOnField(e?: FocusEvent) {
  if (
    component.value &&
    !['NuCurrencyPicker', 'NuInputSelect'].includes(component.value)
  ) {
    focusFirstInput(inputEl.value)
  } else {
    const inputComponent = inputRef.value as unknown as
      | NuInputSelect
      | typeof NuCurrencyPicker
    inputComponent?.focus?.()
    inputComponent?.clickTsetSelectHeader?.()
  }
}
//#endregion INPUT_REF

//#region LIFECYCLE
onMounted(() => {
  initializeLocalUnit()
})

//#endregion LIFECYCLE

//#region UNITS
const localUnit = ref<Nullable<FieldUnit>>(null)

function initializeLocalUnit() {
  if (field?.unit && metaInfo?.defaultUnit && dataType.value?.units) {
    mountLocalUnit(dataType.value, metaInfo)
  } else if (dataType.value?.units) {
    mountLocalUnit(dataType.value, {
      ...dataType.value,
      defaultUnit: {
        isFixed: false,
        unit: dataType.value.defaultUnit!,
      },
    })
  } else if (field?.unit) {
    localUnit.value = { ...newFieldUnit(), unit: field.unit }
  }
}

function mountLocalUnit(dataType: FieldType, type: FieldMetaInfo): void {
  if (!type) {
    return
  }
  localUnit.value = dataType.units!.find(
    (u) => u.unit === type.defaultUnit?.unit
  )!
}

const { displayCurrency } = useDisplayCurrency()
const rowCurrency = computed<Currency>(
  () => cell.value.getRow().getData()?.tableRowCurrency
)
//#endregion UNITS

//#region FIELD
const field = props.cell.getValue()
const dataType = ref(getType(field?.type))
const metaInfo = field?.metaInfo

const cellHozAlignment = computed(
  () => cell.value.getColumn().getDefinition().hozAlign
)

const component = computed(() => {
  const field = cell.value.getValue() as ResultField

  return getComponent(field)
})
function getComponent(field: ResultField): Nullable<string> {
  if (!dataType.value) {
    return null
  }
  const comp = fieldComponent(field, dataType.value)
  return comp
}

function isRate(field: ResultField) {
  return getType(field?.type)?.type === 'Rate'
}

const parentEntity = computed(() => {
  const row = cell.value.getData() as TableRow
  return row.entityToEdit ?? field.metaInfo?.overwriteParentInfo
})

const computedField = computed(() => {
  const field = props.cell.getValue() as ResultField
  return mapFormattedField(field, isRate(field))
})

const isCellLastElement = computed((): boolean => {
  return !props.cell.getElement().nextElementSibling
})
//#endregion FIELD

//#region INTERACTION
function change(updatedField: ResultField) {
  if (!parentEntity.value) {
    return
  }
  const fieldToChange: ResultField = {
    ...updatedField,
    unit: localUnit.value?.unit,
    value: getRateFieldValue(
      isRate(updatedField) && updatedField.value !== null,
      updatedField
    ),
  }
  // todo this is a hardcoded logic for a special field, eventually we should
  //      think of an alternative. but due to bmw we do not have time for a
  //      proper solution, and product desperately wants the field in the table
  const isRearrange =
    updatedField?.name === 'parentStep' ||
    updatedField?.name === 'parentStepRef'

  emit('change', {
    field: fieldToChange,
    parent: parentEntity.value,
    action: isRearrange ? 'rearrangeSteps' : 'updateManufacturingOverwrite',
    rowId: cell.value.getRow().getData().id,
  })
}

function restore(updatedField?: ResultField) {
  if (!parentEntity.value) {
    return
  }
  let fieldToRestore
  if (isResultField(updatedField)) {
    fieldToRestore = updatedField
  } else {
    const field = toRaw(props.cell.getValue())
    const restoredField = {
      ...field,
      value: field.systemValue,
    }
    fieldToRestore = restoredField
  }
  emit('restore', {
    field: fieldToRestore,
    parent: parentEntity.value,
    action: 'updateManufacturingUnlock',
    rowId: cell.value.getRow().getData().id,
  })
}

function focusOut() {
  isFocused.value = false
  // let the tree select handle this on it's own
  if (component.value === 'NuInputTree') {
    return
  }
  emit('cancel')
}

function focusIn() {
  isFocused.value = true
  // let the tree select handle this on it's own
  if (component.value === 'NuInputTree') {
    return
  }
  emit('focusin')
}
//#endregion INTERACTION

//#region SLOTS
const slots = useSlots()
const hasRightIcon = computed(() => hasSlot('right-icon', slots))
//#endregion SLOTS

defineExpose({
  focusOnField,
})
</script>
<script lang="ts">
/**
 * This component is the Equivalent of NuField but for Table cells
 *
 * Based on the cell value (which is a ResultField), picks the component to be displayed
 * It wrapps it in a way that Tabulator can consume it.
 */
export default {
  name: 'TsetFieldCell',
  components: {
    NuCurrencyPicker,
    NuInputNumber,
    NuInputSelect,
    NuInputTree,
    NuInputText,
    NuInputMoney,
    NuInputReadonly,
    NuInputDate,
  },
}
</script>
