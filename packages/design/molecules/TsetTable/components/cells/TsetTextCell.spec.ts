import { gResultField } from '@tset/shared-utils/tests/generators/resultField'
import { mount, type ComponentMountingOptions } from '@vue/test-utils'
import type { CellComponent } from 'tabulator-tables'
import type { IUseTabulatorColumnDefinition } from '../../tabulator/composables/interfaces'
import TsetTextCell from './TsetTextCell.vue'

//#region MOCKS
const baseCell: CellComponent = {
  getElement: vi.fn(),
  getValue: vi.fn(() => gResultField()),
  getColumn: vi.fn(() => ({
    getDefinition: vi.fn(() => ({ iconsBefore: undefined })),
    getPrevColumn: vi.fn(() => {}),
  })),
  getField: vi.fn(),
  getRow: vi.fn(() => ({
    getData: vi.fn(() => []),
  })),
} as unknown as CellComponent

const unlinkIconCell: CellComponent = {
  getElement: vi.fn(),
  getValue: vi.fn(() => gResultField()),
  getColumn: vi.fn(() => ({
    getDefinition: vi.fn(() => ({ iconsBefore: [{ fieldName: 'unLink' }] })),
    getPrevColumn: vi.fn(() => {}),
  })),
  getField: vi.fn(() => '1'),
  getRow: vi.fn(() => ({
    getData: vi.fn(() => ({
      unLink: 'someValue',
    })),
  })),
} as unknown as CellComponent

const currencyIconCell: CellComponent = {
  getElement: vi.fn(),
  getValue: vi.fn(() => gResultField()),
  getColumn: vi.fn(() => ({
    getDefinition: vi.fn(() => ({ iconsBefore: [{ fieldName: 'currency' }] })),
    getPrevColumn: vi.fn(() => {}),
  })),
  getField: vi.fn(() => '1'),
  getRow: vi.fn(() => ({
    getData: vi.fn(() => ({
      currency: 'someValue',
    })),
  })),
} as unknown as CellComponent

const unlinkCurrencyIconCell: CellComponent = {
  getElement: vi.fn(),
  getValue: vi.fn(() => gResultField()),
  getColumn: vi.fn(() => ({
    getDefinition: vi.fn(() => ({
      iconsBefore: [{ fieldName: 'unLink' }, { fieldName: 'currency' }],
    })),
    getPrevColumn: vi.fn(() => {}),
  })),
  getField: vi.fn(() => '1'),
  getRow: vi.fn(() => ({
    getData: vi.fn(() => ({
      unLink: 'someValue',
      currency: 'someValue',
    })),
  })),
} as unknown as CellComponent

const otherIconsCell: CellComponent = {
  getElement: vi.fn(),
  getValue: vi.fn(() => gResultField()),
  getColumn: vi.fn(() => ({
    getDefinition: vi.fn(() => ({
      iconsBefore: [{ someOtherIcon: 'iconValue' }],
    })),
    getPrevColumn: vi.fn(() => {}),
  })),
  getField: vi.fn(() => '1'),
  getRow: vi.fn(() => ({
    getData: vi.fn(() => []),
  })),
} as unknown as CellComponent

const allIconsCell: CellComponent = {
  getElement: vi.fn(),
  getValue: vi.fn(() => gResultField()),
  getColumn: vi.fn(() => ({
    getDefinition: vi.fn(() => ({
      iconsBefore: [
        { fieldName: 'unLink' },
        { fieldName: 'currency' },
        { someOtherIcon: 'iconValue' },
      ],
    })),
    getPrevColumn: vi.fn(() => {}),
  })),
  getField: vi.fn(() => '1'),
  getRow: vi.fn(() => ({
    getData: vi.fn(() => ({
      unLink: 'someValue',
      currency: 'someValue',
    })),
  })),
} as unknown as CellComponent
//#endregion MOCKS

//#region SETUP FACTORY
const given = ({ cell = baseCell } = {}) => {
  const { iconsBefore } = cell
    .getColumn()
    .getDefinition() as IUseTabulatorColumnDefinition
  const props: InstanceType<typeof TsetTextCell>['$props'] = {
    cell,
    iconsBefore,
  }
  const mountOptions: ComponentMountingOptions<typeof TsetTextCell> = {
    props,
    global: {
      stubs: [
        'IconWarningCurrency',
        'IconTableOverwritten',
        'IconUnlink',
        'IconEdit',
      ],
    },
  }

  const wrapper = mount(TsetTextCell, mountOptions)

  //#region HELPERS
  const getUnlinkCurrencyIcons = () =>
    wrapper.findByDataTest('tset-text-cell-icon-unlink-currency-icons')
  const isUnlinkCurrencyIconsVisible = () => getUnlinkCurrencyIcons().exists()
  const getOtherIcons = () =>
    wrapper.findByDataTest('tset-text-cell-icon-other-icons')
  const isOtherIconsVisible = () => getOtherIcons().exists()
  //#endregion HELPERS

  return {
    then: {
      isUnlinkCurrencyIconsVisible,
      isOtherIconsVisible,
    },
  }
}
//#endregion SETUP FACTORY

//#region TESTS

describe('IconsBefore', () => {
  describe('When cell does not have a single icon', () => {
    const { then } = given()

    it('- does not display unLink/currency icons', () => {
      expect(then.isUnlinkCurrencyIconsVisible()).toBe(false)
    })

    it('- does not display other icons', () => {
      expect(then.isOtherIconsVisible()).toBe(false)
    })
  })

  describe('When cell does has the "unLink" icon', () => {
    const { then } = given({ cell: unlinkIconCell })

    it('- displays unLink/currency icons', () => {
      expect(then.isUnlinkCurrencyIconsVisible()).toBe(true)
    })

    it('- does not display other icons', () => {
      expect(then.isOtherIconsVisible()).toBe(false)
    })
  })

  describe('When cell does has the "currency" icon', () => {
    const { then } = given({ cell: currencyIconCell })

    it('- displays unLink/currency icons', () => {
      expect(then.isUnlinkCurrencyIconsVisible()).toBe(true)
    })

    it('- does not display other icons', () => {
      expect(then.isOtherIconsVisible()).toBe(false)
    })
  })

  describe('When cell does has both the "unLink" & "currency" icons', () => {
    const { then } = given({ cell: unlinkCurrencyIconCell })

    it('- displays unLink/currency icons', () => {
      expect(then.isUnlinkCurrencyIconsVisible()).toBe(true)
    })

    it('- does not display other icons', () => {
      expect(then.isOtherIconsVisible()).toBe(false)
    })
  })

  describe('When cell does has some other icon', () => {
    const { then } = given({ cell: otherIconsCell })

    it('- does not display unLink/currency icons', () => {
      expect(then.isUnlinkCurrencyIconsVisible()).toBe(false)
    })

    it('- displays other icons', () => {
      expect(then.isOtherIconsVisible()).toBe(true)
    })
  })

  describe('When cell does has both the "unLink" & "currency" icons, as well as some other icon', () => {
    const { then } = given({ cell: allIconsCell })

    it('- displays unLink/currency icons', () => {
      expect(then.isUnlinkCurrencyIconsVisible()).toBe(true)
    })

    it('- displays other icons', () => {
      expect(then.isOtherIconsVisible()).toBe(true)
    })
  })
})

//#endregion TESTS
