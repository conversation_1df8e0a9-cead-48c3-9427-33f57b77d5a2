import type { Meta, StoryObj } from '@storybook/vue3-vite'
import { createStoryWithSlots } from '@tset/design/storybook-utils/storymaker'
import TsetCard from './TsetCard.vue'

const meta: Meta<typeof TsetCard> = {
  component: TsetCard,
  tags: ['autodocs'],
}
export default meta

type Story = StoryObj<typeof TsetCard>

export const Card: Story = {
  ...createStoryWithSlots({
    component: TsetCard,
    slots: {
      default: {
        template: '<div class="h-50 w-50 "/>',
      },
    },
  }),
}
