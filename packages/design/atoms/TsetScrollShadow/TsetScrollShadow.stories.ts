import type { <PERSON>a, StoryObj } from '@storybook/vue3-vite'
import { onMounted, ref } from 'vue'
import TsetScrollShadow from './TsetScrollShadow.vue'

const meta: Meta<typeof TsetScrollShadow> = {
  component: TsetScrollShadow,
  tags: ['autodocs'],
}
export default meta

type Story = StoryObj<typeof TsetScrollShadow>

export const VerticalScroll: Story = {
  args: { orientation: 'vertical' },
  render(args) {
    return {
      components: { TsetScrollShadow },
      setup() {
        const element = ref<HTMLElement>()
        onMounted(() => {
          element.value
            ?.querySelector('#second-row5')
            ?.scrollIntoView({ block: 'start' })
          element.value
            ?.querySelector('#third-row9')
            ?.scrollIntoView({ block: 'start' })
        })
        return {
          props: { ...args },
          element,
        }
      },
      template: `
      <div ref="element" style="display: flex;">
        <div style="width: 300px; height: 600px; margin: 50px; padding: 20px;">
          <tset-scroll-shadow v-bind="props">
            <div style="width: 100%; display: flex; flex-direction: column; gap: 10px; padding-block: 10px;">
              <span v-for="i in 10" style="background: #ebf1ec; flex-shrink: 0; border-radius: 5px; width: 100%; height: 100px;"></span>
            </div>
          </tset-scroll-shadow>
        </div>
        <div style="width: 300px; height: 600px; margin: 50px; padding: 20px;">
          <tset-scroll-shadow v-bind="props">
            <div style="width: 100%; display: flex; flex-direction: column; gap: 10px; padding-block: 10px;">
              <span :id="'second-row'+i" v-for="i in 10" style="background: #ebf1ec; flex-shrink: 0; border-radius: 5px; width: 100%; height: 100px;"></span>
            </div>
          </tset-scroll-shadow>
        </div>
        <div style="width: 300px; height: 600px; margin: 50px; padding: 20px;">
          <tset-scroll-shadow v-bind="props">
            <div style="width: 100%; display: flex; flex-direction: column; gap: 10px; padding-block: 10px;">
              <span :id="'third-row'+i" v-for="i in 10" style="background: #ebf1ec; flex-shrink: 0; border-radius: 5px; width: 100%; height: 100px;"></span>
            </div>
          </tset-scroll-shadow>
        </div>
      </div>

      `,
    }
  },
}

export const HorizontalScroll: Story = {
  args: { orientation: 'horizontal' },
  render(args) {
    return {
      components: { TsetScrollShadow },
      setup() {
        const element = ref<HTMLElement>()
        onMounted(() => {
          element.value
            ?.querySelector('#second-row5')
            ?.scrollIntoView({ inline: 'start' })
          element.value
            ?.querySelector('#third-row10')
            ?.scrollIntoView({ inline: 'start' })
        })
        return {
          props: { ...args },
          element,
        }
      },
      template: `
      <div ref="element">
        <div style="max-width: 500px; margin: 50px; padding: 20px;">
          <tset-scroll-shadow v-bind="props">
            <div style="width: 100%; display: flex; gap: 10px; padding-block: 10px;">
              <span v-for="i in 10" style="background: lightblue; flex-shrink: 0; border-radius: 5px; width: 80px; height: 40px;"></span>
            </div>
          </tset-scroll-shadow>
        </div>
        <div style="max-width: 500px; margin: 50px; padding: 20px;">
          <tset-scroll-shadow v-bind="props">
            <div style="width: 100%; display: flex; gap: 10px; padding-block: 10px;">
              <span :id="'second-row'+i" v-for="i in 10" style="background: lightblue; flex-shrink: 0; border-radius: 5px; width: 80px; height: 40px;"></span>
            </div>
          </tset-scroll-shadow>
        </div>
        <div style="max-width: 500px; margin: 50px; padding: 20px;">
          <tset-scroll-shadow v-bind="props">
            <div style="width: 100%; display: flex; gap: 10px; padding-block: 10px;">
              <span :id="'third-row'+i" v-for="i in 10" style="background: lightblue; flex-shrink: 0; border-radius: 5px; width: 80px; height: 40px;"></span>
            </div>
          </tset-scroll-shadow>
        </div>
      </div>
      `,
    }
  },
}
