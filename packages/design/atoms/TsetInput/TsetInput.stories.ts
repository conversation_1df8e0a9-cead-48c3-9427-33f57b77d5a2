import { groupedPropStory } from '@tset/design/storybook-utils/storymaker'
import { fn, userEvent, within } from 'storybook/test'
import type { Meta, StoryObj } from '@storybook/vue3-vite'
import TsetInput from './TsetInput.vue'

const meta: Meta<typeof TsetInput> = {
  component: TsetInput,
  args: {
    onFocusin: fn(),
    onKeydown: fn(),
    onBlur: fn(),
    ['onUpdate:modelValue']: fn(),
  },
  tags: ['autodocs'],
}
export default meta

type Story = StoryObj<typeof TsetInput>

//#region helpers
const iconSlot = {
  description: 'Icon to the right of the input',
  template: `<IconChevronDown />`,
}

const unitSlot = {
  description: 'Unit to the right of the input',
  template: `<div class="pr-8">kg/pc</div>`,
}
//#endregion helpers

//#region stories

export const Input: Story = groupedPropStory({
  component: TsetInput,
  defaults: { modelValue: '82792' },
  group: [
    { modelValue: 'default' },
    { modelValue: 'has error', 'has-error': true },
    { modelValue: 'has warning', 'has-warning': true },
    { modelValue: '', placeholder: 'this is a placeholder' },
    { modelValue: 'disabled', disabled: true },
    { modelValue: 'table editor', tsetTableEditor: true },
  ],
  layout: 'vertical',
})

export const InputFocused: Story = {
  args: { modelValue: '123.321' },
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement)
    const input = canvas.getByTestId('input-element')
    await userEvent.click(input)
  },
}

export const InputFocusedTable: Story = {
  args: { modelValue: '123.321', tsetTableEditor: true },
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement)
    const input = canvas.getByTestId('input-element')
    await userEvent.click(input)
  },
}

export const InputWithSubValue: Story = {
  args: { modelValue: '827929.182958437', subValue: 'subvalue text' },
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement)
    const input = canvas.getByTestId('input-element')
    await userEvent.click(input)
  },
}

export const InputTooltip: Story = {
  args: { modelValue: '123.32', tooltip: '123.321111123' },
  play: async ({ canvasElement }) => {
    const canvas = within(canvasElement)
    const input = canvas.getByTestId('input-element')
    await userEvent.hover(input)
  },
  decorators: [
    () => ({
      template: `
      <div class="pb-40">
        <story/>
      </div>`,
    }),
  ],
}

export const InputSlots: Story = groupedPropStory({
  component: TsetInput,
  defaults: { modelValue: '82792' },
  group: [
    {
      props: { modelValue: 'icon slot' },
      slots: {
        // @ts-expect-error weird type issue
        'icon-right': iconSlot,
      },
    },
    {
      props: { modelValue: 'unit slot' },
      slots: {
        // @ts-expect-error weird type issue
        unit: unitSlot,
      },
    },
    {
      props: { modelValue: 'icon and unit slot' },
      slots: {
        // @ts-expect-error weird type issue
        unit: unitSlot,
        // @ts-expect-error weird type issue
        'icon-right': iconSlot,
      },
    },
    {
      props: { modelValue: 'tabulator icon slot', tsetTableEditor: true },
      slots: {
        // @ts-expect-error weird type issue
        'icon-right': iconSlot,
      },
    },
    {
      props: { modelValue: 'tabulator unit slot', tsetTableEditor: true },
      slots: {
        // @ts-expect-error weird type issue
        unit: unitSlot,
      },
    },
    {
      props: {
        modelValue: 'tabulator with icon and unit slot',
        tsetTableEditor: true,
      },
      slots: {
        // @ts-expect-error weird type issue
        'icon-right': iconSlot,
        // @ts-expect-error weird type issue
        unit: unitSlot,
      },
    },
  ],
  layout: 'vertical',
})

//#endregion stories
