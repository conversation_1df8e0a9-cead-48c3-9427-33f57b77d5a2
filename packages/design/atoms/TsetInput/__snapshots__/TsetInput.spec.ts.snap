// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`TsetInput component > - renders properly 1`] = `
"<div data-test="tset-input-wrapper" label="TestLabel">
  <div id="tset-input">
    <div>
      <div><input type="text" autocomplete="off" maxlength="512" placeholder="" size="1" data-test="input-element" data-value="default value" value="default value">
        <div data-test="tset-input-unit"></div>
      </div>
    </div>
    <!--v-if-->
  </div>
</div>"
`;
