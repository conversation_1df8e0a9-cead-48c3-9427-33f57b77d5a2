import type { Meta, StoryObj } from '@storybook/vue3-vite'
import { createStoryWithSlots } from '@tset/design/storybook-utils/storymaker'
import TsetPageWrapper from './TsetPageWrapper.vue'

const meta: Meta<typeof TsetPageWrapper> = {
  component: TsetPageWrapper,
  tags: ['autodocs'],
}
export default meta

type Story = StoryObj<typeof TsetPageWrapper>

export const PageWrapper: Story = {
  ...createStoryWithSlots({
    component: TsetPageWrapper,
    slots: {
      default: {
        template: `
          <div style="height: 500px" class="border-1 rounded-lg border border-black-lightest bg-white p-16">
            the default slotted card with overflow
          </div>`,
      },
      title: {
        template: `<div>Title</div>`,
      },
      subtitle: {
        template: `Subtitle`,
      },
      kpi: {
        template: `<div>123.321</div>`,
      },
    },
  }),
  decorators: [
    () => ({
      template: `<div class="flex h-full w-full overflow-x-auto rounded-lg max-h-300">
        <div class="flex w-full gap-16">
          <story/>
        </div>
      </div>`,
    }),
  ],
}

export const NoHeader: Story = {
  ...createStoryWithSlots({
    component: TsetPageWrapper,
    args: { isTable: true },
    slots: {
      default: {
        template: `
          <div class="h-400 p-16">
            This story displays page wrapper without any header slots, this is the case if isTable prop is true
          </div>`,
      },
      title: {
        template: `<div>Title</div>`,
      },
      kpi: {
        template: `<div>The kpi slot</div>`,
      },
    },
  }),
}
