<template>
  <button
    v-data-test:details
    class="@md/fields:px-4 @md/fields:py-2"
    :class="[
      'text-caption-semibold flex cursor-pointer items-center gap-2 rounded',
      {
        'px-4 py-2': !isEditable,
        'order-3 inline-flex size-20 flex-none items-center justify-center rounded @md/fields:order-last @md/fields:size-[unset]':
          isEditable,
        'bg-primary-veryLight text-primary-dark hover:bg-primary-lighter':
          severity === 'info',
        'bg-warning-veryLight text-warning-dark hover:bg-warning-lighter':
          severity === 'warning',
      },
    ]"
    @click="emit('click')"
  >
    <span
      :class="{
        'hidden @md/fields:inline': isEditable,
      }"
    >
      {{ label }}
    </span>
    <IconDetails class="inline h-[13px] w-[13px]" />
  </button>
</template>

<script setup lang="ts">
//#region PROPS
defineProps<{
  severity: SeverityType
  isEditable: boolean
  label: string
}>()
//#endregion PROPS

//#region EMITS
const emit = defineEmits<{
  click: []
}>()
//#endregion EMITS
</script>

<script lang="ts">
export default {
  name: 'DetailsLabel',
}
</script>
