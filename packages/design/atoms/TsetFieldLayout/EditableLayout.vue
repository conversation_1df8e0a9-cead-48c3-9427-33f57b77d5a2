<template>
  <div class="editable-layout flex min-w-[250px] flex-grow basis-0 flex-col">
    <div
      class="flex w-full flex-grow basis-0 flex-col gap-x-4 gap-y-4 @md/fields:flex-row @md/fields:items-center"
    >
      <!-- left side - label -->
      <div
        class="flex flex-grow items-center gap-x-4 gap-y-2 @md/fields:grid @md/fields:basis-6/12 @md/fields:grid-cols-[1fr_auto] @xl/fields:basis-5/12 @2xl/fields:basis-4/12"
      >
        <div v-if="hasLabel" class="flex items-center gap-4 @md/fields:flex-1">
          <slot name="icon-before-label" />
          <span
            v-data-test:label
            class="editable-layout__label text-body-light text-balance text-black-default"
          >
            <slot name="label" />
          </span>
          <span v-if="hasInfo" v-data-test:info>
            <slot name="info" />
          </span>
        </div>
        <!-- left-icons -->
        <div
          v-data-test:left-icon
          class="left-icon row-span-2 ml-auto flex h-full items-center"
        >
          <slot name="left-icon" />
        </div>
        <!-- details -->
        <div v-if="$slots['details']" class="@md/fields:basis-full">
          <slot name="details" />
        </div>
      </div>

      <!-- right side - input -->
      <div
        v-data-test:input
        class="order-last flex-grow basis-full focus-within:overflow-visible @md/fields:basis-6/12 @xl/fields:basis-7/12 @2xl/fields:basis-8/12"
      >
        <slot name="input" />
      </div>
    </div>
    <div
      v-data-test:error-section
      class="@md/fields:ml-auto @md/fields:w-6/12 @md:pl-4 @xl/fields:w-7/12 @2xl/fields:w-8/12"
    >
      <slot name="error" />
    </div>
  </div>
</template>

<script setup lang="ts">
import { useHasSlot } from '@tset/shared-utils/helpers/component/useHasSlot'

const emit = defineEmits<{
  (e: 'details-clicked'): void
}>()

const hasLabel = useHasSlot('label')
const hasIconBeforeLabel = useHasSlot('icon-before-label')
const hasInfo = useHasSlot('info')
</script>
<script lang="ts">
export default {
  name: 'EditableLayout',
}
</script>

<style lang="postcss" scoped>
.left-icon {
  & > :deep(svg) {
    @apply h-[15px] w-[15px];
  }
}
</style>
