<template>
  <div class="flex min-w-[250px] flex-grow basis-0 flex-wrap items-center">
    <div class="flex max-w-full basis-full flex-row items-center gap-x-4">
      <!-- left side - label and icons -->
      <div
        v-if="hasLabel"
        class="grid grid-cols-[1fr_auto] items-center gap-x-4 gap-y-2"
      >
        <!-- label -->
        <div class="flex flex-1 items-center gap-4">
          <slot name="icon-before-label" />
          <span
            v-data-test:label
            class="text-body-light text-balance text-black-default transition-all"
          >
            <slot name="label" />
          </span>
          <span v-if="hasInfo" v-data-test:info>
            <slot name="info" />
          </span>
        </div>
        <div
          v-data-test:left-icon
          class="left-icon row-span-2 ml-auto flex h-full flex-none items-center"
        >
          <slot name="left-icon" />
        </div>

        <div v-if="$slots['details']" class="basis-full">
          <slot name="details" />
        </div>
      </div>

      <!-- right side - input -->
      <div
        v-data-test:input
        class="order-last min-w-0 flex-grow basis-1/2 transition-all *:basis-full focus-within:overflow-visible"
      >
        <slot name="input" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useHasSlot } from '@tset/shared-utils/helpers/component/useHasSlot'

const hasLabel = useHasSlot('label')
const hasInfo = useHasSlot('info')
</script>

<script lang="ts">
export default {
  name: 'ReadonlyLayout',
}
</script>

<style lang="postcss" scoped>
.left-icon {
  & > :deep(svg) {
    @apply h-[15px] w-[15px];
  }
}
</style>
