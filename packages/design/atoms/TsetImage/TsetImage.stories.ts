import { fn } from 'storybook/test'
import type { Meta, StoryObj } from '@storybook/vue3-vite'

import TsetImage from './TsetImage.vue'
import horizontal from './mock/horizontal.png'
import square from './mock/square.png'
import vertical from './mock/vertical.png'

const meta: Meta<typeof TsetImage> = {
  component: TsetImage,
  args: { onError: fn() },
  tags: ['autodocs'],
}

export default meta

type Story = StoryObj<typeof TsetImage>

export const HorizontalContained: Story = {
  args: {
    src: horizontal,
    alt: 'Alt-Text',
    class: 'object-contain h-full w-full',
  },
  decorators: [
    () => ({
      template: `
      <div class="space-y-16">
        <div class="bg-primary-dark h-128 w-128">
          <story/>
        </div>
        <div class="bg-primary-dark h-10 w-128">
           <story/>
        </div>
        <div class="bg-primary-dark h-128 w-10">
           <story/>
        </div>
      </div>`,
    }),
  ],
}

export const VerticalContained: Story = {
  args: {
    src: vertical,
    alt: 'Alt-Text',
    class: 'object-contain h-full w-full',
  },
  decorators: [
    () => ({
      template: `
      <div class="space-y-16">
        <div class="bg-primary-dark h-128 w-128">
          <story/>
        </div>
        <div class="bg-primary-dark h-32 w-128">
           <story/>
        </div>
        <div class="bg-primary-dark h-128 w-32">
           <story/>
        </div>
      </div>`,
    }),
  ],
}

export const SquareContained: Story = {
  args: { src: square, alt: 'Alt-Text', class: 'object-contain h-full w-full' },
  decorators: [
    () => ({
      template: `
      <div class="space-y-16">
        <div class="bg-primary-dark h-128 w-128">
          <story/>
        </div>
        <div class="bg-primary-dark h-32 w-128">
           <story/>
        </div>
        <div class="bg-primary-dark h-128 w-32">
           <story/>
        </div>
      </div>`,
    }),
  ],
}
export const HorizontalCovered: Story = {
  args: {
    src: horizontal,
    alt: 'Alt-Text',
    class: 'object-covered h-full w-full',
  },
  decorators: [
    () => ({
      template: `
      <div class="space-y-16">
        <div class="bg-primary-dark h-128 w-128">
          <story/>
        </div>
        <div class="bg-primary-dark h-10 w-128">
           <story/>
        </div>
        <div class="bg-primary-dark h-128 w-10">
           <story/>
        </div>
      </div>`,
    }),
  ],
}

export const VerticalCovered: Story = {
  args: {
    src: vertical,
    alt: 'Alt-Text',
    class: 'object-covered h-full w-full',
  },
  decorators: [
    () => ({
      template: `
      <div class="space-y-16">
        <div class="bg-primary-dark h-128 w-128">
          <story/>
        </div>
        <div class="bg-primary-dark h-32 w-128">
           <story/>
        </div>
        <div class="bg-primary-dark h-128 w-32">
           <story/>
        </div>
      </div>`,
    }),
  ],
}

export const SquareCovered: Story = {
  args: { src: square, alt: 'Alt-Text', class: 'object-covered h-full w-full' },
  decorators: [
    () => ({
      template: `
      <div class="space-y-16">
        <div class="bg-primary-dark h-128 w-128">
          <story/>
        </div>
        <div class="bg-primary-dark h-32 w-128">
           <story/>
        </div>
        <div class="bg-primary-dark h-128 w-32">
           <story/>
        </div>
      </div>`,
    }),
  ],
}

// TODO
// 1. Placeholder (will need a proxy to deployed static assets bc of placeholder image living there)
// 2. Loadingstate good question.
export const PlaceholderDefault: Story = {
  args: {
    src: square + '_NOT',
    alt: 'Alt-Text',
    class: 'object-contain h-full w-full',
  },
  decorators: [
    () => ({
      template: `
        <div class="bg-primary-veryLight h-128 w-128">
          <story/>
      </div>`,
    }),
  ],
}

export const PlaceholderCustom: Story = {
  args: {
    src: square + '_NOT',
    defaultSrc: horizontal,
    alt: 'Alt-Text',
    class: 'object-contain h-full w-full',
  },
  decorators: [
    () => ({
      template: `
        <div class="bg-primary-veryLight h-128 w-128">
          <story/>
      </div>`,
    }),
  ],
}

export const Loading: Story = {
  args: {
    src: square + '_NOT',
    alt: 'Alt-Text',
    class: 'object-contain h-full w-full',
  },
  decorators: [
    () => ({
      template: `
        <div class="bg-primary-veryLight h-128 w-128">
          <story/>
      </div>`,
    }),
  ],
}
