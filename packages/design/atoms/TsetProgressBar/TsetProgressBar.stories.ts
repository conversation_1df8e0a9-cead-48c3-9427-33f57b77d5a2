import type { Meta, StoryObj } from '@storybook/vue3-vite'

import TsetProgressBar from './TsetProgressBar.vue'

const meta: Meta<typeof TsetProgressBar> = {
  component: TsetProgressBar,
  tags: ['autodocs'],
}

export default meta

type Story = StoryObj<typeof TsetProgressBar>

export const Animated: Story = {
  args: { isAnimated: true, progress: 0.42 },
  parameters: { chromatic: { disableSnapshot: true } },
}

export const Still: Story = {
  args: { isAnimated: false, progress: 0.62 },
}
