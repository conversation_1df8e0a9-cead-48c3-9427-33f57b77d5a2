import { extendMountOptions } from '@tset/shared-utils/tests/mountOptions'
import { mount } from '@vue/test-utils'
import TsetKpi from '.'

const defaultMountOptions = {
  props: { formattedValue: 'Label' },
  global: {
    stubs: ['IconMultiCurrency'],
  },
}

describe('TsetKpi Component | The general KPI compoment', () => {
  describe('snapshots', () => {
    it('should show correctly without unit', () => {
      const wrapper = mount(TsetKpi, defaultMountOptions)
      expect(wrapper).toMatchSnapshot()
    })
    it('should show correctly with unit', () => {
      const wrapper = mount(
        TsetKpi,
        extendMountOptions(defaultMountOptions, {
          props: {
            unit: 'EUR',
          },
        })
      )
      expect(wrapper).toMatchSnapshot()
    })
    it('should show correctly with unit and icon', () => {
      const wrapper = mount(
        TsetKpi,
        extendMountOptions(defaultMountOptions, {
          props: {
            unit: 'EUR',
          },
          slots: {
            icon: '<IconMultiCurrency />',
          },
        })
      )
      expect(wrapper).toMatchSnapshot()
    })
  })
  it('should be accessible to QA', () => {
    const wrapper = mount(TsetKpi, defaultMountOptions)
    expect(wrapper.find('[data-test="tset-kpi"]').exists()).toBe(true)
  })

  it('should have default props', () => {
    const wrapper = mount(TsetKpi, defaultMountOptions)
    const { $props } = wrapper.vm
    expect($props.unit).toBe(null)
    expect($props.unformattedValue).toBe(null)
    expect($props.size).toBe('small')
    expect($props.title).toBe(null)
    expect($props.titlePosition).toBe('top')
  })

  describe('tooltipText', () => {
    it.each([
      {
        formattedValue: 10.34,
        unformattedValue: 10.3432,
        unit: 'EUR',
        expected: '10.3432 EUR',
      },
      {
        formattedValue: 10.34,
        unformattedValue: null,
        unit: 'USD',
        expected: '10.34 USD',
      },
      {
        formattedValue: 10.34,
        unformattedValue: 10.3432,
        unit: null,
        expected: '10.3432',
      },
      {
        formattedValue: 10.34,
        unformattedValue: null,
        unit: null,
        expected: '10.34',
      },
    ])(
      'should show the correct tooltip text for formattedValue=$formattedValue, unformattedValue=$unformattedValue, unit=$unit',
      ({ formattedValue, unformattedValue, unit, expected }) => {
        const wrapper = mount(
          TsetKpi,
          extendMountOptions(defaultMountOptions, {
            props: {
              formattedValue,
              unformattedValue,
              unit,
            },
          })
        )
        const { vm } = wrapper
        expect(vm.tooltipText).toBe(expected)
      }
    )
  })
})
