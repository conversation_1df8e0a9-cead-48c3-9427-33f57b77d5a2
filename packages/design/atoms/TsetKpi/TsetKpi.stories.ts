import type { Meta, StoryObj } from '@storybook/vue3-vite'
import { PropStoryMaker } from '@tset/design/storybook-utils/storymaker'
import TsetKpi from './TsetKpi.vue'

const meta: Meta<typeof TsetKpi> = {
  component: TsetKpi,
  tags: ['autodocs'],
  args: {
    formattedValue: '13.15',
  },
}
export default meta

type Story = StoryObj<typeof TsetKpi>
export const Kpi: Story = {
  parameters: { chromatic: { disableSnapshot: true } },
}

const propStory = PropStoryMaker(TsetKpi, {
  formattedValue: '13.15',
  unit: 'kg',
})

export const Size: Story = propStory({
  args: { title: 'Net weight per part' },
  prop: 'size',
  values: ['small', 'medium', 'large', 'wrapper'],
})

export const TitlePosition: Story = propStory({
  args: { title: 'Net weight per part' },
  prop: 'titlePosition',
  values: ['bottom', 'top'],
})

export const UnitsPosition: Story = propStory({
  args: { title: 'Net weight per part', stackUnits: true },
  prop: 'stackUnits',
  values: [true, false],
})
