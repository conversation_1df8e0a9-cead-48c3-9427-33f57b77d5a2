import { fn } from 'storybook/test'
import type { Meta, StoryObj } from '@storybook/vue3-vite'
import { PropStoryMaker } from '@tset/design/storybook-utils/storymaker'
import { ChipSize } from './TsetChip.types'
import TsetChip from './TsetChip.vue'

const meta: Meta<typeof TsetChip> = {
  component: TsetChip,
  tags: ['autodocs'],
  args: { onClose: fn(), label: 'This is our Chip' },
}
export default meta

type Story = StoryObj<typeof TsetChip>

export const Chip: Story = PropStoryMaker(
  TsetChip,
  {}
)({ prop: 'size', values: [...ChipSize] })
