import { expect, fn, userEvent, within } from 'storybook/test'
import type { Meta, StoryObj } from '@storybook/vue3-vite'
import TsetCheckbox from './TsetCheckbox.vue'

const meta: Meta<typeof TsetCheckbox> = {
  component: TsetCheckbox,
  args: { onClick: fn() },
  tags: ['autodocs'],
}

export default meta

type Story = StoryObj<typeof TsetCheckbox>

export const Checked: Story = {
  args: { status: 'checked' },
  play: async ({ canvasElement, args }) => {
    const canvas = within(canvasElement)
    const checkbox = canvas.getByTestId('tset-checkbox')
    await userEvent.click(checkbox)

    expect(args.onClick).toHaveBeenCalledOnce()
  },
}
export const Unchecked: Story = {
  args: { status: 'unchecked' },
}
export const Indeterminate: Story = {
  args: { status: 'indeterminate' },
}
export const Disabled: Story = {
  args: { isDisabled: true },
  play: async ({ canvasElement, args }) => {
    const canvas = within(canvasElement)
    const checkbox = canvas.getByTestId('tset-checkbox')
    await userEvent.click(checkbox)

    expect(args.onClick).not.toHaveBeenCalled()
  },
}
