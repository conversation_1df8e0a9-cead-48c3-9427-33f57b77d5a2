import type { Meta, StoryObj } from '@storybook/vue3-vite'
import {
  createStoryWithSlots,
  PropStoryMaker,
} from '@tset/design/storybook-utils/storymaker'
import { expect, fn, userEvent, within } from 'storybook/test'
import { h } from 'vue'
import TsetRadioInput from './TsetRadioInput.vue'

const meta: Meta<typeof TsetRadioInput> = {
  component: TsetRadioInput,
  args: { onChange: fn() },
  tags: ['autodocs'],
}
export default meta

type Story = StoryObj<typeof TsetRadioInput>

const defaultSlot = {
  default: () => [h('div', 'some text')],
}

const propStory = PropStoryMaker(
  TsetRadioInput,
  { value: 'TsetRadioInput' },
  defaultSlot
)

export const Selected: Story = {
  ...createStoryWithSlots({
    component: TsetRadioInput,
    args: { value: 'selected', modelValue: 'selected', name: 'radio selected' },
    slots: {
      default: {
        template: 'radio label selected',
      },
    },
  }),
  play: async ({ canvasElement, args }) => {
    const canvas = within(canvasElement)
    const radio = canvas.getByTestId('tset-radio-input')
    await userEvent.click(radio)
    expect(args.onChange).toHaveBeenCalledOnce()
  },
}

export const Unselected: Story = {
  ...createStoryWithSlots({
    component: TsetRadioInput,
    args: {
      value: 'unselected',
      modelValue: 'def-not-selected',
      name: 'radio unselected',
    },
    slots: {
      default: {
        template: 'radio label unselected',
      },
    },
  }),
  play: async ({ canvasElement, args }) => {
    const canvas = within(canvasElement)
    const radio = canvas.getByTestId('tset-radio-input')
    await userEvent.click(radio)
    expect(args.onChange).toHaveBeenCalledOnce()
  },
}

export const Indent: Story = propStory({
  ...createStoryWithSlots({
    component: TsetRadioInput,
    args: {
      value: 'indent',
      modelValue: 'indent',
      name: 'indent',
    },

    slots: {
      default: {
        template: 'radio label indent',
      },
    },
  }),
  prop: 'indent',
  values: ['s', 'm'],
  play: async ({ canvasElement, args }) => {
    const canvas = within(canvasElement)
    const radios = canvas.getAllByTestId('tset-radio-input')
    for (const radio of radios) {
      await expect(radio).not.toBeDisabled()

      await userEvent.click(radio)
    }
    await expect(args.onChange).toHaveBeenCalledTimes(radios.length)
  },
})

export const DisabledSelected: Story = {
  ...createStoryWithSlots({
    component: TsetRadioInput,
    args: {
      value: 'disabled',
      modelValue: 'disabled',
      name: 'radio disabled selected',
      disabled: true,
    },
    slots: {
      default: {
        template: 'radio label disabled selected',
      },
    },
  }),
}

export const DisabledUnSelected: Story = {
  ...createStoryWithSlots({
    component: TsetRadioInput,
    args: {
      value: 'disabled',
      modelValue: 'disabled-unselected',
      name: 'radio disabled unselected',
      disabled: true,
    },
    slots: {
      default: {
        template: 'radio label disabled unselected',
      },
    },
  }),
}
