import { knowledgeStore } from '@/store'
import { manufacturingStore } from '@domain/calculation/manufacturing.store'
import { wait } from '@tset/shared-utils/tests/general'
import { flushPromises } from '@vue/test-utils'
import { beforeEach, describe, expect, it, vi, type Mock } from 'vitest'
import { defineComponent } from 'vue'
import type { Input } from './addObjectToCalculation'
import { addNewMasterdataEntity } from './addNewMasterdataEntity'

// Mock dependencies
const hoistedMocks = vi.hoisted(() => ({
  mockedShowModal: vi.fn(),
  mockedHFunction: vi.fn(),
}))

vi.mock('@/store', () => ({
  knowledgeStore: {
    startAddObject: vi.fn(),
    projectId: 'test-project-id',
    changeSelectedMasterdataItem: vi.fn(),
    setFieldsMasterdata: vi.fn(),
    setLoadingState: vi.fn(),
    setFetchNewMasterdataFieldsError: vi.fn(),
  },
}))

vi.mock('@tset/shared-utils/plugins/vue-modal', () => ({
  useModal: vi.fn(() => ({
    show: hoistedMocks.mockedShowModal,
  })),
}))

vi.mock('vue', async () => {
  const actual = await vi.importActual('vue')
  return {
    ...actual,
    h: hoistedMocks.mockedHFunction,
  }
})

vi.mock('@domain/masterdata/data/HeaderType.service', () => ({
  useMasterdataHeaderTypeService: () => ({
    prefetchHeaderType: vi.fn(),
    getHeaderType: () => ({
      data: { value: null },
    }),
  }),
}))

// Mock Components
vi.mock('@calculation/knowledge/KnowledgeModal.vue', () => ({
  default: defineComponent({
    name: 'TestKnowledge',
    template: `<div><slot name="nuMasterdataSelect" :entitySubmit="() => {}" /></div>`,
  }),
}))
vi.mock('@masterdata/detail-select/DetailSelect.vue', () => ({
  default: vi.fn(),
}))

vi.mock('@domain/calculation/manufacturing.store', () => ({
  manufacturingStore: {
    loadedManufacturing: {
      getField: vi.fn(),
      getFields: vi.fn(),
    },
  },
}))

// Setup mock field values
const mockGetField = manufacturingStore.loadedManufacturing!.getField as Mock
mockGetField.mockImplementation((fieldName: string) => {
  const fieldValues: Record<string, string> = {
    consumableClassification: 'test-consumable-classification',
    materialHeaderType: 'test-material-header-type',
    materialClassificationType: 'test-material-classification-type',
  }
  return { value: fieldValues[fieldName] }
})

describe('addNewMasterdataEntity', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should retrieve required fields and set up modal with correct props and slots', () => {
    const input = { type: 'CONSUMABLE' as ManufacturingEntityType }
    addNewMasterdataEntity(input)

    // Verify field retrievals
    expect(
      manufacturingStore.loadedManufacturing!.getField
    ).toHaveBeenCalledWith('consumableClassification')
    expect(
      manufacturingStore.loadedManufacturing!.getField
    ).toHaveBeenCalledWith('materialHeaderType')
    expect(
      manufacturingStore.loadedManufacturing!.getField
    ).toHaveBeenCalledWith('materialClassificationType')

    // Verify h function call for DetailSelect
    expect(hoistedMocks.mockedShowModal.mock.calls[0][0]['props']).toEqual({
      projectId: 'test-project-id',
      modalName: 'knowledgeModal',
    })

    // execute the slot rendering
    hoistedMocks.mockedShowModal.mock.calls[0][0]['slots']?.[
      'nuMasterdataSelect'
    ]({ entitySubmit: vi.fn() })

    expect(hoistedMocks.mockedHFunction).toHaveBeenCalledWith(
      expect.any(Function),
      expect.objectContaining({
        key: 'material-select',
        classificationConfig: [
          {
            classificationTypeKey: 'test-material-classification-type',
            classificationKeys: ['test-consumable-classification'],
          },
        ],
        onSelect: expect.any(Function),
      })
    )
  })

  it('handle selection of item', async () => {
    const input = { type: 'CONSUMABLE' as ManufacturingEntityType }
    addNewMasterdataEntity(input)
    const entitySubmit = vi.fn()
    hoistedMocks.mockedShowModal.mock.lastCall?.[0]['slots']?.[
      'nuMasterdataSelect'
    ]({ entitySubmit })

    const onSelect =
      hoistedMocks.mockedHFunction.mock.lastCall?.[1]['onSelect']

    const rowValue = newRowValue()
    await onSelect(rowValue)

    const mockedChangeSelected = vi.mocked(
      knowledgeStore.changeSelectedMasterdataItem
    )

    expect(mockedChangeSelected).toHaveBeenCalledWith({
      headerKey: 'htk',
      headerClassificationKey: 'key',
    })
    expect(entitySubmit).not.toHaveBeenCalled()
  })

  it('should submit on double click', async () => {
    const input = { type: 'CONSUMABLE' as ManufacturingEntityType }
    addNewMasterdataEntity(input)
    const entitySubmit = vi.fn()
    hoistedMocks.mockedShowModal.mock.lastCall?.[0]['slots']?.[
      'nuMasterdataSelect'
    ]({ entitySubmit })

    const onSelect =
      hoistedMocks.mockedHFunction.mock.lastCall?.[1]['onSelect']

    const rowValue = newRowValue()

    // simulate double selection/click
    onSelect(rowValue)
    await wait(200)
    onSelect(rowValue)
    await flushPromises()

    expect(knowledgeStore.changeSelectedMasterdataItem).toHaveBeenCalledWith({
      headerKey: 'htk',
      headerClassificationKey: 'key',
    })
    // The doubleclick submit has been disabled again because of unstable behaviour
    // expect(entitySubmit).toHaveBeenCalledOnce()
  })
})

function newRowValue() {
  return {
    headerDto: {
      active: true,
      headerTypeKey: '',
      key: 'htk',
      name: 'HeaderKey1',
      classifications: {
        'tset.ref.classification-type.material': [
          { classificationTypeKey: 'type-key', key: 'key', name: 'name' },
        ],
      },
    },
    detail: {
      active: true,
      effectivities: {},
      headerKey: 'htk',
      value: {
        type: 'numeric',
        value: 100,
        numerator: { type: 'currency', key: 'USD' },
      },
      detailValueTypeKey: 'price',
      getValueCurrency: () => 'USD',
    },
  }
}
