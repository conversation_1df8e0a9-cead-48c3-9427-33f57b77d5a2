import {
  Detail,
  type DetailSearchContentItem,
} from '@domain/masterdata/model/Detail'
import { beforeEach, describe, expect, it, vi } from 'vitest'
import { getDetailValue } from './getDetailValue'

describe('getDetailValue', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should return undefined if the detail value type is not numeric', () => {
    const item = {
      detail: {
        value: { type: 'lov', key: 'test-key' },
      },
    } as unknown as DetailSearchContentItem

    const result = getDetailValue(
      item as DetailSearchContentItem,
      {} as AddObjectRequest,
      'cost'
    )

    expect(result).toBeUndefined()
  })

  it('should return undefined if the lookup field is not found', () => {
    const result = getDetailValue(
      {} as DetailSearchContentItem,
      {} as AddObjectRequest,
      'cost'
    )
    expect(result).toBeUndefined()
  })

  it('should return a ResultField with overwritten value, if the price lookup value and detail value dont match', () => {
    const item = {
      detail: {
        value: { type: 'numeric', value: 100, valueInBaseSiUnit: 100 },
        detailValueTypeKey: 'price',
      },
    } as unknown as DetailSearchContentItem

    const selectedItem: Partial<AddObjectRequest> = {
      fields: [
        {
          name: 'materialBasePrice',
          value: 100,
          currencyInfo: {
            USD: 94,
            EUR: 100,
          },
          type: 'Number',
          source: 'I',
          systemValue: null,
        },
        {
          name: 'materialBaseCO2',
          value: 100,
          type: 'Number',
          source: 'I',
          systemValue: null,
        },
      ],
    }

    const result = getDetailValue(
      item as DetailSearchContentItem,
      selectedItem as AddObjectRequest,
      'cost',
      'USD'
    )

    expect(result).toEqual({
      name: 'materialBasePrice',
      value: 100,
      currencyInfo: {
        USD: 94,
        EUR: 100,
      },
      type: 'Number',
      source: 'I',
      systemValue: null,
    })
  })

  it('should return undefined, if the lookup value and detail value dont match but the valueTypeKey does not match', () => {
    const item: Partial<DetailSearchContentItem> = {
      detail: {
        value: { type: 'numeric', value: 100, valueInBaseSiUnit: 100 },
        detailValueTypeKey: 'co2',
      } as Partial<Detail> as Detail,
    }

    const selectedItem: Partial<AddObjectRequest> = {
      fields: [
        {
          name: 'pricePerUnit',
          value: 50,
          type: 'Number',
          source: 'I',
          systemValue: null,
        },
      ],
    }

    const result = getDetailValue(
      item as DetailSearchContentItem,
      selectedItem as AddObjectRequest,
      'co2'
    )

    expect(result).toBeUndefined()
  })
})
