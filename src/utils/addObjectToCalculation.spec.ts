import { knowledgeStore } from '@/store'
import { PAGE } from '@tset/shared-model/navigation/navigation'
import { beforeEach, describe, expect, it, vi } from 'vitest'
import type { Input } from './addObjectToCalculation'
import * as addObjectToCalculationUtils from './addObjectToCalculation'

const wizardCalculationStore = vi.hoisted(() => ({
  startAddCalculation: vi.fn(() => Promise.resolve()),
  setParentStepId: vi.fn(),
  prefillTheStep: vi.fn(),
}))

vi.mock('@/store', () => ({
  knowledgeStore: {
    startAddObject: vi.fn(),
    projectId: 'test-project-id',
    changeSelectedMasterdataItem: vi.fn(),
    setFieldsMasterdata: vi.fn(),
    setLoadingState: vi.fn(),
    setFetchNewMasterdataFieldsError: vi.fn(),
  },
}))

vi.mock('@domain/wizard/wizardCalculation.store', () => {
  return {
    useWizardCalculationStore: () => wizardCalculationStore,
  }
})

vi.mock('./addNewMasterdataEntity', () => ({
  addNewMasterdataEntity: vi.fn(),
}))

describe('addObjectToCalculation utils', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('addObjectToCalculation', () => {
    describe('with a new masterdata entity type', () => {
      it('should call addNewMasterdataEntity with the input', async () => {
        const { addNewMasterdataEntity } = await import(
          './addNewMasterdataEntity'
        )
        const input: Input = { type: 'CONSUMABLE' }

        await addObjectToCalculationUtils.addObjectToCalculation(input)

        expect(addNewMasterdataEntity).toHaveBeenCalledWith({
          type: 'CONSUMABLE',
          entityClass: undefined,
        })
      })
    })

    describe('with an entity that is not a new masterdata entity', () => {
      describe('with the entityType MANUFACTURING_STEP', () => {
        it('should call startAddObject in the knowledgeStore with the entityType MANUFACTURING_STEP', async () => {
          const input: Input = { type: 'MANUFACTURING_STEP' }
          await addObjectToCalculationUtils.addObjectToCalculation(input)

          expect(knowledgeStore.startAddObject).toHaveBeenCalledWith({
            entityType: 'MANUFACTURING_STEP',
          })
        })
      })

      describe('with the entityType SPECIAL_DIRECT_COST', () => {
        describe('with a loadedManufacturingId', () => {
          it('should call startAddObject in the knowledgeStore with the entityType SPECIAL_DIRECT_COST and the loadedManufacturingId', async () => {
            const input: Input = {
              type: 'SPECIAL_DIRECT_COST',
              loadedManufacturingId: 'test-id',
            }
            await addObjectToCalculationUtils.addObjectToCalculation(input)

            expect(knowledgeStore.startAddObject).toHaveBeenCalledWith({
              entityType: 'SPECIAL_DIRECT_COST',
              parentId: 'test-id',
            })
          })
        })

        describe('without a loadedManufacturingId', () => {
          it('should throw an error', async () => {
            const input: Input = { type: 'SPECIAL_DIRECT_COST' }
            await expect(
              addObjectToCalculationUtils.addObjectToCalculation(input)
            ).rejects.toThrow(
              'loadedManufacturingId is required for the special direct cost type'
            )
          })
        })
      })

      describe('with the entityType BOM_ENTRY', () => {
        it('should call startAddCalculation in the wizardCalculationStore with the calculationMode NEW and the calculationPosition SUB', async () => {
          const input: Input = {
            type: 'BOM_ENTRY',
            targetPage: PAGE.MANU_MACHINE,
          }
          await addObjectToCalculationUtils.addObjectToCalculation(input)

          expect(
            wizardCalculationStore.startAddCalculation
          ).toHaveBeenCalledWith({
            calculationMode: 'CALCULATION_MODE_NEW',
            calculationPosition: 'SUB',
            targetPage: PAGE.MANU_MACHINE,
          })
        })
        it('should prefillStep if provided', async () => {
          const input: Input = {
            type: 'BOM_ENTRY',
            targetPage: PAGE.MANU_STEP,
            prefillStep: { id: 'stepId', displayDesignation: 'stepName' },
          }
          await addObjectToCalculationUtils.addObjectToCalculation(input)

          expect(wizardCalculationStore.prefillTheStep).toHaveBeenCalledWith(
            'stepId',
            'stepName'
          )
        })
      })

      describe('with an unknown entityType', () => {
        it('should throw an error', async () => {
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          const input: Input = { type: 'UNKNOWN' as any }
          await expect(
            addObjectToCalculationUtils.addObjectToCalculation(input)
          ).rejects.toThrow('unhandled object type - UNKNOWN')
        })
      })
    })
  })

  describe('addSubCalculationToStep', () => {
    it('should call setParentStepId in the wizardCalculationStore with the stepId', () => {
      const stepId = 'test-step-id'
      addObjectToCalculationUtils.addSubCalculationToStep(stepId)

      expect(wizardCalculationStore.setParentStepId).toHaveBeenCalledWith(
        stepId
      )
    })

    it('should call addObjectToCalculation with the entityType BOM_ENTRY', () => {
      const stepId = 'test-step-id'
      addObjectToCalculationUtils.addSubCalculationToStep(stepId)

      expect(wizardCalculationStore.startAddCalculation).toHaveBeenCalledWith({
        calculationMode: 'CALCULATION_MODE_NEW',
        calculationPosition: 'SUB',
        targetPage: undefined,
      })
    })
  })
})
