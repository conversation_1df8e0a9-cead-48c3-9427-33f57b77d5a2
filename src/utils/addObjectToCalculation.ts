import { knowledgeStore } from '@/store'
import KnowledgeModal from '@calculation/knowledge/KnowledgeModal.vue'
import { useWizardCalculationStore } from '@domain/wizard/wizardCalculation.store'
import {
  CalculationCreationModalMode,
  CalculationPosition,
} from '@tset/shared-model/calculation/WizardCalculationEnums'
import { masterdataEntityTypesInCost } from '@tset/shared-model/masterdata/NuMasterdata'
import type { PAGE } from '@tset/shared-model/navigation/navigation'
import { useModal } from '@tset/shared-utils/plugins/vue-modal'
import { addNewMasterdataEntity } from './addNewMasterdataEntity'

export type Input = {
  type: ManufacturingEntityType
  loadedManufacturingId?: string
  targetPage?: PAGE
  /**
   * Only used for masterdata, so we don't prefix it with MD_ or anything.
   */
  parentType?: string
  parentId?: string
  entityClass?: string
  // for sub calculation
  prefillStep?: { id: string; displayDesignation: string }

  // entity linking
  linkEntityId?: string
  entityClassFilters?: string[]
  tabsToShow?: SelectedKnowledgeTab[]
  linkEntityField?: string
}

const wizardCalculationStore = useWizardCalculationStore()

const { show } = useModal()

const entityClassMap: Partial<Record<ManufacturingEntityType, string>> = {
  C_PART: 'ElectronicComponent',
}

/**
 * Used to trigger the add flow to add an object like a material,
 * consumable, manufacturing step, ... to a calculation
 *
 * The loadedManufacturingId is only needed for the special direct cost type.
 *
 * @param {Input} input
 * @returns {void}
 */
export async function addObjectToCalculation(input: Input) {
  if (masterdataEntityTypesInCost.includes(input.type)) {
    addNewMasterdataEntity({
      ...input,
      entityClass: input.entityClass ?? entityClassMap[input.type],
    })
    return
  }

  let modalResult: Awaited<ReturnType<typeof knowledgeStore.startAddObject>> =
    undefined

  switch (input.type) {
    case 'MATERIAL':
    case 'MANUFACTURING_STEP':
      modalResult = await knowledgeStore.startAddObject({
        entityType: input.type,
        entityClassFilters: input.entityClassFilters,
        tabsToShow: input.tabsToShow,
        linkEntityId: input.linkEntityId,
        linkEntityField: input.linkEntityField,
      })
      break
    case 'SPECIAL_DIRECT_COST': {
      if (!input.loadedManufacturingId) {
        throw new Error(
          'loadedManufacturingId is required for the special direct cost type'
        )
      }
      modalResult = await knowledgeStore.startAddObject({
        entityType: input.type,
        entityClassFilters: input.entityClassFilters,
        parentId: input.loadedManufacturingId,
        tabsToShow: input.tabsToShow,
        linkEntityId: input.linkEntityId,
        linkEntityField: input.linkEntityField,
      })
      break
    }
    case 'BOM_ENTRY':
      wizardCalculationStore
        .startAddCalculation({
          calculationMode: CalculationCreationModalMode.NEW,
          calculationPosition: CalculationPosition.SUB,
          targetPage: input.targetPage,
        })
        .then(() => {
          if (input.prefillStep) {
            wizardCalculationStore.prefillTheStep(
              input.prefillStep.id,
              input.prefillStep.displayDesignation
            )
          }
        })
      break
    default:
      throw new Error(`unhandled object type - ${input.type}`)
  }

  if (modalResult?.modalProps) {
    await show({
      component: KnowledgeModal,
      props: modalResult.modalProps,
    })
  }
}

export function addSubCalculationToStep(stepId: string) {
  wizardCalculationStore.setParentStepId(stepId)
  addObjectToCalculation({ type: 'BOM_ENTRY' })
}
