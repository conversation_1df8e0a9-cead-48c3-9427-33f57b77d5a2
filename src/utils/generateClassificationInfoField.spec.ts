import {
  Detail,
  type DetailSearchContentItem,
} from '@domain/masterdata/model/Detail'
import { HeaderType } from '@domain/masterdata/model/HeaderType'
import type { HeaderDetailQueryResponseDto } from '@domain/masterdata/model/openapi'
import { beforeEach, describe, expect, it, vi } from 'vitest'
import { generateClassificationInfoField } from './generateClassificationInfoField'

describe('generateClassificationInfoField', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  const pcht = new HeaderType({
    active: true,
    effectivities: {},
    headerKeyColumnName: '',
    headerKeyType: 'header.key.simple',
    index: 0,
    key: '',
    name: '',
    detailValueSchema: {
      type: 'pricecomposition',
      schema: {
        headerTypeKey: 'pchtk',
        classificationTypeKey: 'pcctk',
        type: 'pricecomposition',
      },
    },
  })

  const pcvtht = new HeaderType({
    ...pcht.toDto(),
    detailValueSchema: {
      type: 'valuetype',
      detailValueTypeMapping: {
        pricecomposition: {
          key: '',
          detailValueSchema: {
            headerTypeKey: 'pcvthtk',
            classificationTypeKey: 'pcvtctk',
            type: 'pricecomposition',
          },
        },
      },
    },
  })

  const priceCompositionDetail = new Detail({
    active: true,
    effectivities: {},
    headerKey: 'asdf',
    value: {
      type: 'pricecomposition',
      components: [
        {
          classificationKey: 'ck1',
          massType: 'Manual',
          templateMass: {
            type: 'numeric',
            value: 3,
          },
        },
      ],
    },
  })

  it('returns undefined if the detailValue is not a pricecomposition', () => {
    const result = generateClassificationInfoField(pcht, {
      headerDto: {} as unknown as HeaderDetailQueryResponseDto,
      detail: new Detail({
        active: true,
        effectivities: {},
        headerKey: 'asdf',
        value: {
          type: 'numeric',
          value: 3,
        },
      }),
    })

    expect(result).toBeUndefined()
  })

  it('returns undefined if the headerType has not a pricecomposition schema', () => {
    const result = generateClassificationInfoField(
      new HeaderType({
        ...pcht.toDto(),
        detailValueSchema: {
          type: 'numeric',
          valueSchema: {
            type: 'numeric',
            unitOfMeasurement: {},
          },
        },
      }),
      {
        headerDto: {} as unknown as HeaderDetailQueryResponseDto,
        detail: priceCompositionDetail,
      }
    )

    expect(result).toBeUndefined()
  })

  it('returns the classificationInfo field if the detailValue is a pricecomposition and the headerType has a pricecomposition schema', () => {
    const result = generateClassificationInfoField(pcht, {
      headerDto: {} as unknown as HeaderDetailQueryResponseDto,
      detail: priceCompositionDetail,
    })

    expect(result).toEqual({
      name: 'classificationInfo',
      source: 'I',
      type: 'ClassificationInfoField',
      value: {
        priceCompositionHeaderTypeKey: 'pchtk',
        classificationTypeKey: 'pcctk',
        classificationKeys: ['ck1'],
      },
    })
  })

  it('returns the classificationInfo field if the detailValue is a pricecomposition and the headerType has a pricecomposition valueType', () => {
    const result = generateClassificationInfoField(pcvtht, {
      headerDto: {} as unknown as HeaderDetailQueryResponseDto,
      detail: priceCompositionDetail,
    })

    expect(result).toEqual({
      name: 'classificationInfo',
      source: 'I',
      type: 'ClassificationInfoField',
      value: {
        priceCompositionHeaderTypeKey: 'pcvthtk',
        classificationTypeKey: 'pcvtctk',
        classificationKeys: ['ck1'],
      },
    })
  })
})
