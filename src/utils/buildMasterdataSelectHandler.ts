import { knowledgeStore } from '@/store'
import { useAppMode } from '@/store/useAppMode'
import { useMasterdataHeaderTypeService } from '@domain/masterdata/data/HeaderType.service'
import type { DetailSearchContentItem } from '@domain/masterdata/model/Detail'
import { Header } from '@domain/masterdata/model/Header'
import { newField } from '@tset/shared-utils/helpers/manufacturing'
import { gResultField } from '@tset/shared-utils/tests/generators/resultField'
import { sharedVueQueryClient } from '@tset/shared-utils/vueQuery/vueQueryClient'
import { until } from '@vueuse/core'
import { difference } from 'lodash'
import { ref } from 'vue'
import { getDetailValue } from './getDetailValue'
import { generateClassificationInfoField } from './generateClassificationInfoField'

const { currentMode } = useAppMode()
const { getHeaderType } = useMasterdataHeaderTypeService()

/**
 * This function is called when a new masterdata material is selected in the add modal (KnowledgeModal).
 */
export const buildMasterdataSelectHandler =
  (headerTypeKey: string, classificationType: string) =>
  async (item: DetailSearchContentItem) => {
    knowledgeStore.setLoadingState({ name: 'canClick', state: false })

    const { data: headerType } = getHeaderType(
      ref(headerTypeKey),
      sharedVueQueryClient
    )
    await until(headerType).toBeTruthy()

    const { headerDto, detail } = item

    // Get the assigned classification in the material classificationType
    const headerClassification =
      headerDto.classifications?.[classificationType]?.[0].key

    if (!headerClassification) {
      throw new Error('headerClassification not found in selected item')
    }

    // change the selected Masterdata Item to fetch all the relevant fields from the BE
    let selectedItem: AddObjectRequest | undefined
    try {
      // The call can throw an error if there's a problem with the BE
      // or the selected item is not supported
      selectedItem = await knowledgeStore.changeSelectedMasterdataItem({
        headerKey: headerDto.key,
        headerClassificationKey: headerClassification,
      })
      knowledgeStore.setFetchNewMasterdataFieldsError(null)
    } catch (e) {
      // in case of an error, we set the fetchFieldsResponse to null,
      // to indicate the fields could not be fetched
      knowledgeStore.setFetchNewMasterdataFieldsError(String(e))
    }

    // without the selectedItem response from the BE, we cannot proceed
    if (!selectedItem) {
      return
    }

    /**
     * First we set the headerKey of the selected item
     * as a field named "headerKey" in the knowledgeStore.
     */
    const newMasterdataFields: ResultField[] = [
      newField('headerKey', headerDto.key, 'Text', undefined, undefined, 'I'),
      newField(
        'displayDesignation',
        headerDto.name ?? headerDto.key,
        'Text',
        undefined,
        undefined,
        'I'
      ),
    ]

    /**
     * For the modularized technologies, we need to add
     * the technology classification keys from the header.
     */
    const technologyClassificationType =
      headerDto.classifications?.['tset.ref.classification-type.technology']
    if (technologyClassificationType?.length) {
      newMasterdataFields.push(
        newField(
          'technologyClassifications',
          technologyClassificationType.map((c) => c.key),
          'Text',
          {
            multiple: true,
          }
        )
      )
    }

    // Get all the fields that should be passed through to the new masterdata material
    const passThroughFieldIds = ['stepId']
    const passThroughFields = selectedItem.fields.filter((field) =>
      passThroughFieldIds.includes(field.name)
    )

    newMasterdataFields.push(...passThroughFields)

    // Get the base currency of the item
    const baseCurrencyField = selectedItem.fields.find(
      (field) => field.name === 'baseCurrency'
    )
    const baseCurrency = getMasterdataItemBaseCurrency(item)
    if (baseCurrency && baseCurrency !== baseCurrencyField?.value) {
      newMasterdataFields.push(
        newField(
          'baseCurrency',
          baseCurrency,
          'Currency',
          undefined,
          undefined,
          'I'
        )
      )
    }

    /**
     * The main value of a selected item (that the user selected)
     * needs to be compared with the item
     * that the system selected based on the calculation params (lookup).
     *
     * In case the selected item is of type 'pricecomposition' (hardcoded for now)
     * we need to make a classificationInfo comparison,
     * because the value is a configuration (the classification info)
     *
     * In all other cases, we will do a price/emission comparision,
     * and compare the normal numeric value
     */

    if (detail?.value.type === 'pricecomposition') {
      /**
       * For the classificationInfo comparision:
       * 1. we need to build the classificationInfo field
       * 2. we need to compare it to the classificationInfo field from the lookup result from the server
       */
      if (!headerType.value) {
        throw new Error(
          'The detail value is of type pricecomposition, so we need a headerType to do a comparision. No headerType was found. This should never happen.'
        )
      }
      const lookupField = selectedItem.fields.find(
        (f) => f.name === 'classificationInfo'
      ) as ResultField<ClassificationInfoFieldData> | undefined
      const classificationInfoField = generateClassificationInfoField(
        headerType.value,
        item
      )
      if (
        classificationInfoField &&
        compareClassificationInfoField(classificationInfoField, lookupField)
      ) {
        newMasterdataFields.push(classificationInfoField)
        newMasterdataFields.push(
          gResultField({
            name: 'materialPriceType',
            value: 'COMPOSED_PRICE',
            source: 'I',
            type: 'MaterialPriceType',
          })
        )
      }
    } else {
      /**
       * To comply with the existing knowledge logic on the backend we need to do:
       * 1. Compare the value from the resultField (backend) and the value from the selectedItem (frontend)
       * 2. Overwrite the corresponding value field (cost / co2) if they are different
       */
      // TODO: Also do a 2 step logic here. 1. create the field, 2. do the comparision
      const overwrittenValueField = getDetailValue(
        item,
        selectedItem,
        currentMode.value,
        baseCurrency as Currency
      )
      if (overwrittenValueField) {
        newMasterdataFields.push(overwrittenValueField)
        newMasterdataFields.push(
          gResultField({
            name: 'materialPriceType',
            value: 'SIMPLE_PRICE',
            source: 'I',
            type: 'MaterialPriceType',
          })
        )
      }
    }

    // add all the fields to the knowledgeStore
    knowledgeStore.setFieldsMasterdata(newMasterdataFields)

    // Update the loading state, because there's nothing to wait for loading
    knowledgeStore.setLoadingState({ name: 'canClick', state: true })
  }

// This function should proably live somewhere else, but where?
// We should anyway restructure this code in the next step with electronic components.
function getMasterdataItemBaseCurrency(
  item: DetailSearchContentItem
): string | undefined {
  const { detail, headerDto } = item

  // Check detail value currency
  const detailCurrency = detail?.getValueCurrency()
  if (detailCurrency) {
    return detailCurrency
  }

  // Check header detail value schema currency
  const headerPriceCurrency = new Header({
    ...headerDto,
    classifications: undefined,
  }).getValueTypePriceCurrency()

  if (headerPriceCurrency) {
    return headerPriceCurrency
  }

  return undefined
}

function generatedClassificationConfig(
  classificationType: string,
  classificationKey: string,
  technology?: string
): {
  classificationTypeKey: string
  classificationKeys: string[]
}[] {
  const classifications = [
    {
      classificationTypeKey: classificationType,
      classificationKeys: classificationKey.split(','),
    },
  ]
  if (technology) {
    classifications.push({
      classificationTypeKey: 'tset.ref.classification-type.technology',
      classificationKeys: [technology],
    })
  }
  return classifications
}

/**
 * Compare 2 ClassificationInfo Fields.
 * @return true is they are they are different
 */
function compareClassificationInfoField(
  selectedData: ResultField<ClassificationInfoFieldData>,
  lookupData: ResultField<ClassificationInfoFieldData> | undefined
) {
  if (!lookupData?.value) {
    return true
  }

  if (
    selectedData.value.priceCompositionHeaderTypeKey !==
    lookupData.value.priceCompositionHeaderTypeKey
  ) {
    return true
  }

  if (
    selectedData.value.classificationTypeKey !==
    lookupData.value.classificationTypeKey
  ) {
    return true
  }

  const selectedClassificationKeys = selectedData.value.classificationKeys
  const lookupClassificationKeys = lookupData.value.classificationKeys

  if (selectedClassificationKeys.length !== lookupClassificationKeys.length) {
    return true
  }

  const diff = difference(selectedClassificationKeys, lookupClassificationKeys)
  if (diff.length > 0) {
    return true
  }

  return false
}

export { generatedClassificationConfig }
