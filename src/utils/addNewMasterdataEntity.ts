import { knowledgeStore } from '@/store'
import KnowledgeModal from '@calculation/knowledge/KnowledgeModal.vue'
import { manufacturingStore } from '@domain/calculation/manufacturing.store'
import { useMasterdataHeaderTypeService } from '@domain/masterdata/data/HeaderType.service'
import DetailSelect from '@masterdata/detail-select/DetailSelect.vue'
import { $t } from '@shared/translation/nuTranslation'
import {
  masterdataConfigurations,
  masterdataEntityTypesInCost,
} from '@tset/shared-model/masterdata/NuMasterdata'
import Notifications from '@tset/shared-utils/plugins/notifications'
import { useModal } from '@tset/shared-utils/plugins/vue-modal'
import { sharedVueQueryClient } from '@tset/shared-utils/vueQuery/vueQueryClient'
import { h } from 'vue'
import type { Input } from './addObjectToCalculation'
import {
  buildMasterdataSelectHandler,
  generatedClassificationConfig,
} from './buildMasterdataSelectHandler'

const { prefetchHeaderType } = useMasterdataHeaderTypeService()

export function addNewMasterdataEntity(input: Input) {
  const type = input.type as (typeof masterdataEntityTypesInCost)[number]

  const configuration = masterdataConfigurations[type]
  if (!configuration) {
    console.error(`no configuration found for masterdata entity type ${type}`)
    return
  }

  const headerTypeField = manufacturingStore.loadedManufacturing?.getField(
    configuration.headerTypeId
  )
  const classificationTypeField =
    manufacturingStore.loadedManufacturing?.getField(
      configuration.classificationTypeId
    )
  const classificationKey = manufacturingStore.loadedManufacturing?.getField(
    configuration.classificationId
  )?.value

  if (!classificationKey || !headerTypeField || !classificationTypeField) {
    Notifications.error({
      title: $t('notifications.title.anErrorOccuredWithoutRequest'),
      message: $t('notifications.error.anErrorOccuredWithoutRequest'),
    })
    return
  }

  knowledgeStore.startAddObject(
    {
      entityType: type,
      parentId: input.parentId ?? input.loadedManufacturingId,
      parentType: 'MD_MATERIAL_PARENT',
      entityClass: input.entityClass,
      // entity linking
      linkEntityId: input.linkEntityId,
      entityClassFilters: input.entityClassFilters,
      tabsToShow: input.tabsToShow,
      linkEntityField: input.linkEntityField,
    },
    true
  )

  prefetchHeaderType(headerTypeField.value as string, sharedVueQueryClient)

  const onNewMasterdataSelect = buildMasterdataSelectHandler(
    headerTypeField.value as string,
    'tset.ref.classification-type.material'
  )

  useModal().show({
    component: KnowledgeModal,
    props: {
      projectId: knowledgeStore.projectId,
      modalName: 'knowledgeModal',
    },
    slots: {
      nuMasterdataSelect: (slotArgs) => [
        h(DetailSelect, {
          key: 'material-select',
          headerTypeKey: headerTypeField.value as string,
          technology: slotArgs?.calculationModuleTechnology,
          classificationConfig: generatedClassificationConfig(
            classificationTypeField.value as string,
            classificationKey as string,
            slotArgs?.calculationModuleTechnology as string
          ),
          onSelect: onNewMasterdataSelect,
        }),
      ],
    },
  })
}
