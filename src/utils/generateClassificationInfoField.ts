import type { DetailSearchContentItem } from '@domain/masterdata/model/Detail'
import type { HeaderType } from '@domain/masterdata/model/HeaderType'
import type {
  FieldSchemaDto,
  PriceCompositionDetailValueFieldSchemaDto,
} from '@domain/masterdata/model/openapi'

export function generateClassificationInfoField(
  headerType: HeaderType,
  item: DetailSearchContentItem
): ResultField<ClassificationInfoFieldData> | undefined {
  const { detail } = item

  const detailValue = detail?.value

  if (detailValue?.type !== 'pricecomposition') {
    return undefined
  }

  let headerTypeDetailValueSchema:
    | PriceCompositionDetailValueFieldSchemaDto
    | undefined = undefined

  if (headerType.detailValueSchema?.type === 'pricecomposition') {
    headerTypeDetailValueSchema = headerType.detailValueSchema.schema
  } else if (
    headerType.detailValueSchema?.type === 'valuetype' &&
    headerType.detailValueSchema.detailValueTypeMapping['pricecomposition']
  ) {
    headerTypeDetailValueSchema = headerType.detailValueSchema
      .detailValueTypeMapping['pricecomposition'].detailValueSchema as Extract<
      FieldSchemaDto,
      { type: 'pricecomposition' }
    >
  } else {
    return undefined
  }

  const detailValueClassificationKeys = detailValue.components.map(
    (c) => c.classificationKey
  )

  return {
    name: 'classificationInfo',
    source: 'I',
    type: 'ClassificationInfoField',
    value: {
      priceCompositionHeaderTypeKey: headerTypeDetailValueSchema.headerTypeKey,
      classificationTypeKey: headerTypeDetailValueSchema.classificationTypeKey,
      classificationKeys: detailValueClassificationKeys,
    },
  }
}
