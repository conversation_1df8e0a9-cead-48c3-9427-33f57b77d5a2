import { knowledgeStore } from '@/store'
import { manufacturingStore } from '@domain/calculation/manufacturing.store'
import {
  Detail,
  type DetailSearchContentItem,
} from '@domain/masterdata/model/Detail'
import { HeaderType } from '@domain/masterdata/model/HeaderType'
import type { HeaderDetailQueryResponseDto } from '@domain/masterdata/model/openapi'
import { wait } from '@tset/shared-utils/tests/general'
import { flushPromises } from '@vue/test-utils'
import { beforeEach, describe, expect, it, vi, type Mock } from 'vitest'
import { defineComponent, ref } from 'vue'
import { buildMasterdataSelectHandler } from './buildMasterdataSelectHandler'

// Mock dependencies
const hoistedMocks = vi.hoisted(() => ({
  mockedShowModal: vi.fn(),
  mockedHFunction: vi.fn(),
}))

vi.mock('@/store', () => ({
  knowledgeStore: {
    startAddObject: vi.fn(),
    projectId: 'test-project-id',
    changeSelectedMasterdataItem: vi.fn(),
    setFieldsMasterdata: vi.fn(),
    setLoadingState: vi.fn(),
    setFetchNewMasterdataFieldsError: vi.fn(),
  },
}))

vi.mock('@/store/useAppMode', () => ({
  useAppMode: () => ({
    currentMode: { value: 'cost' },
    isCostMode: { value: true },
    fromAppMode: <T>(a: T, _b: T) => a,
  }),
}))

vi.mock('vue', async () => {
  const actual = await vi.importActual('vue')
  return {
    ...actual,
    h: hoistedMocks.mockedHFunction,
  }
})

vi.mock('@domain/masterdata/data/HeaderType.service', () => ({
  useMasterdataHeaderTypeService: () => ({
    prefetchHeaderType: vi.fn(),
    getHeaderType: () => ({
      data: ref(
        new HeaderType({
          active: true,
          effectivities: {},
          headerKeyColumnName: '',
          headerKeyType: 'header.key.simple',
          index: 0,
          key: '',
          name: '',
        })
      ),
    }),
  }),
}))

// Mock Components
vi.mock('@calculation/knowledge/KnowledgeModal.vue', () => ({
  default: defineComponent({
    name: 'TestKnowledge',
    template: `<div><slot name="nuMasterdataSelect" :entitySubmit="() => {}" /></div>`,
  }),
}))
vi.mock('@masterdata/detail-select/DetailSelect.vue', () => ({
  default: vi.fn(),
}))

vi.mock('@domain/calculation/manufacturing.store', () => ({
  manufacturingStore: {
    loadedManufacturing: {
      getField: vi.fn(),
      getFields: vi.fn(),
    },
  },
}))

// Setup mock field values
const mockGetField = manufacturingStore.loadedManufacturing!.getField as Mock
mockGetField.mockImplementation((fieldName: string) => {
  const fieldValues: Record<string, string> = {
    consumableClassification: 'test-consumable-classification',
    materialHeaderType: 'test-material-header-type',
    materialClassificationType: 'test-material-classification-type',
  }
  return { value: fieldValues[fieldName] }
})

describe('buildMasterdataSelectHandler', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  describe('onNewMasterdataSelect', () => {
    it('should call changeSelectedMasterdataItem in the knowledgeStore with the headerKey and headerClassificationKey', async () => {
      const item: DetailSearchContentItem = {
        headerDto: {
          key: 'test-key',
          name: 'Test Name',
          active: true,
          headerTypeKey: 'test-header-type',
          classifications: {
            'tset.ref.classification-type.material': [
              {
                key: 'test-classification',
                name: '',
                classificationTypeKey: 'tset.ref.classification-type.material',
              },
            ],
          },
        },
        detail: {
          active: true,
          effectivities: {},
          headerKey: 'test-key',
          value: {
            type: 'numeric',
            value: 100,
            valueInBaseSiUnit: 100,
            numerator: { type: 'currency', key: 'USD' },
          },
          detailValueTypeKey: 'price',
          getValueCurrency: () => 'USD',
          getBuiltinDisplayValue(_field) {
            throw new Error()
          },
          toDetailReferenceDto() {
            throw new Error()
          },
          toDetailQueryDto(_headerType, _showInactive) {
            throw new Error()
          },
          isValid() {
            throw new Error()
          },
          toDto() {
            throw new Error()
          },
          toDetail(_value) {
            throw new Error()
          },
          removeSystemManagedEffectivities(_headerType) {
            throw new Error()
          },
          fillMissingValueSchema(_header) {
            throw new Error()
          },
        },
      }

      vi.mocked(knowledgeStore.changeSelectedMasterdataItem).mockResolvedValue({
        fields: [
          {
            name: 'stepId',
            value: 'test-step-id',
            type: 'Text',
            unit: null,
            source: 'I',
            systemValue: null,
          },
          {
            name: 'materialBasePrice',
            value: 50,
            type: 'Number',
            unit: null,
            source: 'I',
            systemValue: null,
          },
        ],
        parentId: '',
        entityClass: '',
        entityType: '',
        bomNodeId: '',
        refreshTrigger: '',
      })

      const onNewMasterdataSelect = buildMasterdataSelectHandler(
        '',
        'tset.ref.classification-type.material'
      )
      await onNewMasterdataSelect(item)

      expect(knowledgeStore.changeSelectedMasterdataItem).toHaveBeenCalledWith({
        headerKey: 'test-key',
        headerClassificationKey: 'test-classification',
      })

      expect(knowledgeStore.setFieldsMasterdata).toHaveBeenCalledWith(
        expect.arrayContaining([
          expect.objectContaining({
            name: 'headerKey',
            value: 'test-key',
            type: 'Text',
            source: 'I',
          }),
          expect.objectContaining({
            name: 'displayDesignation',
            value: 'Test Name',
            type: 'Text',
            source: 'I',
          }),
          expect.objectContaining({
            name: 'stepId',
            value: 'test-step-id',
            type: 'Text',
            source: 'I',
          }),
          expect.objectContaining({
            name: 'baseCurrency',
            type: 'Currency',
            value: 'USD',
            source: 'I',
          }),
          expect.objectContaining({
            name: 'materialBasePrice',
            value: 100,
            type: 'Number',
            source: 'I',
          }),
        ])
      )

      expect(knowledgeStore.setLoadingState).toHaveBeenCalledWith({
        name: 'canClick',
        state: true,
      })
    })

    it('should throw an error if headerClassification is not found', async () => {
      const item = {
        headerDto: {
          key: 'test-key',
          classifications: [],
        },
      } as unknown as DetailSearchContentItem

      const onNewMasterdataSelect = buildMasterdataSelectHandler('', '')
      await expect(onNewMasterdataSelect(item)).rejects.toThrow(
        'headerClassification not found in selected item'
      )
    })

    it('should add the technologyClassifications field if technologyClassificationType is present', async () => {
      const item = {
        headerDto: {
          key: 'test-key',
          name: 'Test Name',
          active: true,
          headerTypeKey: 'test-header-type',
          classifications: {
            'tset.ref.classification-type.technology': [
              {
                key: 'test-technology',
              },
              {
                key: 'test-technology-2',
              },
            ],
            'tset.ref.classification-type.material': [
              {
                key: 'test-classification',
                name: '',
                classificationTypeKey: 'tset.ref.classification-type.material',
              },
            ],
          },
        },
      }

      vi.mocked(knowledgeStore).changeSelectedMasterdataItem.mockResolvedValue({
        bomNodeId: '',
        entityClass: '',
        entityType: '',
        parentId: '',
        refreshTrigger: '',
        fields: [],
      })

      const onNewMasterdataSelect = buildMasterdataSelectHandler(
        '',
        'tset.ref.classification-type.material'
      )
      await onNewMasterdataSelect(item as unknown as DetailSearchContentItem)

      expect(knowledgeStore.setFieldsMasterdata).toHaveBeenCalledWith(
        expect.arrayContaining([
          expect.objectContaining({
            name: 'technologyClassifications',
            value: ['test-technology', 'test-technology-2'],
            type: 'Text',
            source: 'I',
            metaInfo: expect.objectContaining({
              multiple: true,
            }),
          }),
        ])
      )
    })
  })
})
