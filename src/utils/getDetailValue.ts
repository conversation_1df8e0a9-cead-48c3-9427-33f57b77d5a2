import type { DetailSearchContentItem } from '@domain/masterdata/model/Detail'

/**
 * Gets the value and compares it to the lookup value. Returns the value to set as the value field.
 * @param {DetailSearchContentItem} item
 * @returns The value to set as the value field. Undefined if not value should be set.
 */
export function getDetailValue(
  item: DetailSearchContentItem,
  selectedItem: AddObjectRequest,
  _appMode: Mode,
  baseCurrency?: Currency
): ResultField | undefined {
  /**
   * If the detail value type is not numeric,
   * or the value in the base si unit is not present,
   * we can't perform a comparison and return early.
   */
  if (
    item.detail?.value.type !== 'numeric' ||
    item.detail?.value.valueInBaseSiUnit === undefined
  ) {
    return undefined
  }

  /**
   * Based on the detailValueTypeKey we will get the correct lookup field.
   * If the user selects an item with emission, we have to compare to the
   * materialBaseCO2 field and for the materialBasePrice for the price type.
   */

  let lookupField: ResultField | undefined = undefined
  let detailValueTypeTargetKey: 'price' | 'emission' | undefined = undefined

  switch (item.detail.detailValueTypeKey) {
    case 'price':
      lookupField = selectedItem.fields.find(
        (f) => f.name === 'materialBasePrice'
      )
      detailValueTypeTargetKey = 'price'
      break
    case 'emission':
      lookupField = selectedItem.fields.find(
        (f) => f.name === 'materialBaseCO2'
      )
      detailValueTypeTargetKey = 'emission'
      break
  }

  if (!lookupField) {
    return undefined
  }

  /**
   * If the do a price comparison and the selected lookup field has a currencyInfo
   * property (price in exchange rates), then we need to compare the price
   * to the correct exchange rate with the baseCurrency.
   */
  let lookupValue: number = (lookupField.value as number) ?? 0
  if (
    detailValueTypeTargetKey === 'price' &&
    baseCurrency &&
    lookupField.currencyInfo
  ) {
    lookupValue = lookupField.currencyInfo?.[baseCurrency] ?? 0
  }

  const masterdataBaseSiUnitValue = item.detail.value.valueInBaseSiUnit

  // get the diff of the lookup value and the item value
  const diff = lookupValue - masterdataBaseSiUnitValue
  const threshold = 0.00000001
  const isDifferenceAboveThreshold = Math.abs(diff) > threshold

  // if the values are different, we overwrite the value
  if (isDifferenceAboveThreshold) {
    return {
      ...lookupField,
      source: 'I',
      value: masterdataBaseSiUnitValue,
    }
  }

  return undefined
}
