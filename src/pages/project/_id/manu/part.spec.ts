import { manufacturingStore } from '@domain/calculation/manufacturing.store'
import { useCreateManuStore } from '@domain/wizard/createManu.store'
import MethodPlan from '@tset/shared-ui/manufacturing/CostModule/MethodPlan.vue'
import PartLeft from '@tset/shared-ui/manufacturing/CostModule/PartLeft.vue'
import PartRight from '@tset/shared-ui/manufacturing/CostModule/PartRight.vue'
import { isFti } from '@tset/shared-utils/helpers/fti'
import { gBomNodeEntity } from '@tset/shared-utils/tests/generators/bomNodeEntity'
import { gManufacturing } from '@tset/shared-utils/tests/generators/manufacturing'
import { withModalsMock } from '@tset/shared-utils/tests/mocks/withModalsMock'
import { shallowMount } from '@vue/test-utils'
import { nextTick } from 'vue'
import Part from './part.vue'

withModalsMock()
vi.hoisted(async () => {
  const { createTestingPinia } = await import('@pinia/testing')
  createTestingPinia({ stubActions: false })
})
vi.mock('@/store', () => ({
  smfStore: {},
  navigationStore: {
    navigateTo: vi.fn(),
  },
}))
vi.mock('@domain/wizard/api/wizardCalculation', () => ({
  editWizard: vi.fn(() => ({ id: 'some-id' })),
}))
vi.mock('@tset/shared-utils/helpers/fti')

const createManuStoreMock = vi.mocked(useCreateManuStore())
const isFtiMock = vi.mocked(isFti)

function setup({ isFti = true } = {}) {
  isFtiMock.mockReturnValue(isFti)

  manufacturingStore.setNode({
    node: gBomNodeEntity({
      manufacturing: gManufacturing({
        className: 'ManufacturingProgressiveDieStamping',
      }),
    }),
  })
  const wrapper = shallowMount(Part)
  const getMethodPlan = () => wrapper.findComponent(MethodPlan)
  const editWizard = (payload?: string) =>
    getMethodPlan().vm.$emit('edit-wizard', payload ?? 'FTI_PROC')
  const getPartLeft = () => wrapper.findComponent(PartLeft)
  const getPartRight = () => wrapper.findComponent(PartRight)
  const loadPartLeft = (status = false) =>
    getPartLeft().vm.$emit('loading-part', status)
  const loadPartRight = (status = false) =>
    getPartRight().vm.$emit('loading-part', status)
  const loadMethodPlan = (status = false) =>
    getMethodPlan().vm.$emit('loading', status)
  return {
    wrapper,
    getMethodPlan,
    editWizard,
    loadPartLeft,
    loadPartRight,
    loadMethodPlan,
    getPartLeft,
    getPartRight,
  }
}

describe('part.vue', () => {
  afterEach(() => {
    vi.clearAllMocks()
  })
  describe('when the user clicks the "edit wizard step" button', () => {
    it('- navigates to the selected wizard step', async () => {
      const { editWizard } = setup()
      editWizard()
      expect(createManuStoreMock.editWizard).toHaveBeenCalledOnce()
    })
  })
  describe('when the left part of the part tab gets modified', () => {
    it(
      '- blocks interactions until the change was done',
      testIsLoadingShown('getPartLeft', 'loadPartLeft')
    )
  })
  describe('when the right part of the part tab gets modified', () => {
    it(
      '- blocks interactions until the change was done',
      testIsLoadingShown('getPartRight', 'loadPartRight')
    )
  })
  describe('when the method plan in the part tab gets modified', () => {
    it(
      '- blocks interactions until the change was done',
      testIsLoadingShown('getMethodPlan', 'loadMethodPlan')
    )
  })
  describe('when the calculation is non-fti', () => {
    it('- does not show the method plan card', () => {
      const { getMethodPlan } = setup({ isFti: false })
      expect(getMethodPlan().exists()).toBeFalsy()
    })
  })
})

const testIsLoadingShown =
  (
    componentGetterName: keyof ReturnType<typeof setup>,
    componentLoadingEmitName: keyof ReturnType<typeof setup>
  ) =>
  async () => {
    const {
      [componentGetterName]: getComponent,
      [componentLoadingEmitName]: loadComponent,
    } = setup()
    // @ts-expect-error not all return types of setup are callable
    expect(getComponent().props()).toMatchObject({ isLoading: false })
    // @ts-expect-error not all return types of setup are callable
    loadComponent(true)
    await nextTick()
    // @ts-expect-error not all return types of setup are callable
    expect(getComponent().props()).toMatchObject({ isLoading: true })
  }
